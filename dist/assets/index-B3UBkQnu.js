var L=Object.defineProperty;var W=(c,e,t)=>e in c?L(c,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):c[e]=t;var R=(c,e,t)=>W(c,typeof e!="symbol"?e+"":e,t);(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))i(s);new MutationObserver(s=>{for(const n of s)if(n.type==="childList")for(const a of n.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&i(a)}).observe(document,{childList:!0,subtree:!0});function t(s){const n={};return s.integrity&&(n.integrity=s.integrity),s.referrerPolicy&&(n.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?n.credentials="include":s.crossOrigin==="anonymous"?n.credentials="omit":n.credentials="same-origin",n}function i(s){if(s.ep)return;s.ep=!0;const n=t(s);fetch(s.href,n)}})();class l{constructor(e=0,t=0){this.x=e,this.y=t}static zero(){return new l(0,0)}static one(){return new l(1,1)}static up(){return new l(0,-1)}static down(){return new l(0,1)}static left(){return new l(-1,0)}static right(){return new l(1,0)}add(e){return new l(this.x+e.x,this.y+e.y)}subtract(e){return new l(this.x-e.x,this.y-e.y)}multiply(e){return new l(this.x*e,this.y*e)}divide(e){if(e===0)throw new Error("Division by zero");return new l(this.x/e,this.y/e)}magnitude(){return Math.sqrt(this.x*this.x+this.y*this.y)}normalize(){const e=this.magnitude();return e===0?l.zero():this.divide(e)}distance(e){return this.subtract(e).magnitude()}dot(e){return this.x*e.x+this.y*e.y}addInPlace(e){return this.x+=e.x,this.y+=e.y,this}subtractInPlace(e){return this.x-=e.x,this.y-=e.y,this}multiplyInPlace(e){return this.x*=e,this.y*=e,this}normalizeInPlace(){const e=this.magnitude();return e>0&&(this.x/=e,this.y/=e),this}set(e,t){return this.x=e,this.y=t,this}setFromVector(e){return this.x=e.x,this.y=e.y,this}angle(){return Math.atan2(this.y,this.x)}static fromAngle(e,t=1){return new l(Math.cos(e)*t,Math.sin(e)*t)}rotate(e){const t=Math.cos(e),i=Math.sin(e),s=this.x*t-this.y*i,n=this.x*i+this.y*t;return new l(s,n)}rotateInPlace(e){const t=Math.cos(e),i=Math.sin(e),s=this.x*t-this.y*i,n=this.x*i+this.y*t;return this.x=s,this.y=n,this}perpendicular(){return new l(-this.y,this.x)}clone(){return new l(this.x,this.y)}equals(e,t=0){return t===0?this.x===e.x&&this.y===e.y:Math.abs(this.x-e.x)<=t&&Math.abs(this.y-e.y)<=t}toString(){return`Vector2(${this.x.toFixed(2)}, ${this.y.toFixed(2)})`}}class _{constructor(e){this.canvas=e,this.keys=new Map,this.keysPressed=new Map,this.keysReleased=new Map,this.mousePosition=new l(0,0),this.mouseButtons=new Map,this.mousePressed=new Map,this.mouseReleased=new Map,this.touches=new Map,this.touchStarted=new Map,this.touchEnded=new Map,this.keyMappings=new Map,this.setupDefaultMappings(),this.isTouchDevice="ontouchstart"in window,this.virtualJoystick=null,this.handleKeyDown=this.handleKeyDown.bind(this),this.handleKeyUp=this.handleKeyUp.bind(this),this.handleMouseDown=this.handleMouseDown.bind(this),this.handleMouseUp=this.handleMouseUp.bind(this),this.handleMouseMove=this.handleMouseMove.bind(this),this.handleTouchStart=this.handleTouchStart.bind(this),this.handleTouchEnd=this.handleTouchEnd.bind(this),this.handleTouchMove=this.handleTouchMove.bind(this),this.init()}init(){document.addEventListener("keydown",this.handleKeyDown),document.addEventListener("keyup",this.handleKeyUp),this.canvas.addEventListener("mousedown",this.handleMouseDown),this.canvas.addEventListener("mouseup",this.handleMouseUp),this.canvas.addEventListener("mousemove",this.handleMouseMove),this.canvas.addEventListener("touchstart",this.handleTouchStart,{passive:!1}),this.canvas.addEventListener("touchend",this.handleTouchEnd,{passive:!1}),this.canvas.addEventListener("touchmove",this.handleTouchMove,{passive:!1}),this.canvas.addEventListener("contextmenu",e=>e.preventDefault()),this.isTouchDevice&&this.initVirtualJoystick(),console.log("InputManager initialized")}setupDefaultMappings(){this.keyMappings.set("moveUp",["ArrowUp","KeyW"]),this.keyMappings.set("moveDown",["ArrowDown","KeyS"]),this.keyMappings.set("moveLeft",["ArrowLeft","KeyA"]),this.keyMappings.set("moveRight",["ArrowRight","KeyD"]),this.keyMappings.set("fire",["Space","Enter"]),this.keyMappings.set("pause",["Escape","KeyP"]),this.keyMappings.set("interact",["KeyE","KeyF"]),this.keyMappings.set("debug",["F1"])}handleKeyDown(e){const t=e.code;this.keys.get(t)||this.keysPressed.set(t,!0),this.keys.set(t,!0),this.isGameKey(t)&&e.preventDefault()}handleKeyUp(e){const t=e.code;this.keys.set(t,!1),this.keysReleased.set(t,!0),this.isGameKey(t)&&e.preventDefault()}handleMouseDown(e){const t=e.button;this.mouseButtons.get(t)||this.mousePressed.set(t,!0),this.mouseButtons.set(t,!0),this.updateMousePosition(e),e.preventDefault()}handleMouseUp(e){const t=e.button;this.mouseButtons.set(t,!1),this.mouseReleased.set(t,!0),this.updateMousePosition(e),e.preventDefault()}handleMouseMove(e){this.updateMousePosition(e)}updateMousePosition(e){const t=this.canvas.getBoundingClientRect();this.mousePosition.set(e.clientX-t.left,e.clientY-t.top)}handleTouchStart(e){e.preventDefault();for(const t of e.changedTouches){const i=this.getTouchPosition(t);this.touches.set(t.identifier,i),this.touchStarted.set(t.identifier,i.clone()),this.virtualJoystick&&this.virtualJoystick.handleTouchStart(t.identifier,i)}}handleTouchEnd(e){e.preventDefault();for(const t of e.changedTouches){const i=this.getTouchPosition(t);this.touchEnded.set(t.identifier,i),this.touches.delete(t.identifier),this.virtualJoystick&&this.virtualJoystick.handleTouchEnd(t.identifier)}}handleTouchMove(e){e.preventDefault();for(const t of e.changedTouches){const i=this.getTouchPosition(t);this.touches.set(t.identifier,i),this.virtualJoystick&&this.virtualJoystick.handleTouchMove(t.identifier,i)}}getTouchPosition(e){const t=this.canvas.getBoundingClientRect();return new l(e.clientX-t.left,e.clientY-t.top)}isKeyDown(e){return this.keys.get(e)||!1}isKeyPressed(e){return this.keysPressed.get(e)||!1}isKeyReleased(e){return this.keysReleased.get(e)||!1}isMouseDown(e=0){return this.mouseButtons.get(e)||!1}isMousePressed(e=0){return this.mousePressed.get(e)||!1}isMouseReleased(e=0){return this.mouseReleased.get(e)||!1}isActionDown(e){const t=this.keyMappings.get(e);return t?t.some(i=>this.isKeyDown(i)):!1}isActionPressed(e){const t=this.keyMappings.get(e);return t?t.some(i=>this.isKeyPressed(i)):!1}isActionReleased(e){const t=this.keyMappings.get(e);return t?t.some(i=>this.isKeyReleased(i)):!1}getMovementVector(){const e=new l(0,0);if(this.isActionDown("moveLeft")&&(e.x-=1),this.isActionDown("moveRight")&&(e.x+=1),this.isActionDown("moveUp")&&(e.y-=1),this.isActionDown("moveDown")&&(e.y+=1),this.virtualJoystick&&this.virtualJoystick.isActive()){const t=this.virtualJoystick.getInput();e.addInPlace(t)}return e.magnitude()>1&&e.normalizeInPlace(),e}setKeyMapping(e,t){this.keyMappings.set(e,Array.isArray(t)?t:[t])}addKeyMapping(e,t){const i=this.keyMappings.get(e)||[];i.push(t),this.keyMappings.set(e,i)}removeKeyMapping(e,t){const s=(this.keyMappings.get(e)||[]).filter(n=>n!==t);this.keyMappings.set(e,s)}isGameKey(e){for(const t of this.keyMappings.values())if(t.includes(e))return!0;return!1}initVirtualJoystick(){this.virtualJoystick=new B(this.canvas)}update(){this.keysPressed.clear(),this.keysReleased.clear(),this.mousePressed.clear(),this.mouseReleased.clear(),this.touchStarted.clear(),this.touchEnded.clear(),this.virtualJoystick&&this.virtualJoystick.update()}render(e){this.virtualJoystick&&this.virtualJoystick.render(e)}destroy(){document.removeEventListener("keydown",this.handleKeyDown),document.removeEventListener("keyup",this.handleKeyUp),this.canvas.removeEventListener("mousedown",this.handleMouseDown),this.canvas.removeEventListener("mouseup",this.handleMouseUp),this.canvas.removeEventListener("mousemove",this.handleMouseMove),this.canvas.removeEventListener("touchstart",this.handleTouchStart),this.canvas.removeEventListener("touchend",this.handleTouchEnd),this.canvas.removeEventListener("touchmove",this.handleTouchMove),this.virtualJoystick&&this.virtualJoystick.destroy(),console.log("InputManager destroyed")}}class B{constructor(e){this.canvas=e,this.active=!1,this.touchId=null,this.center=new l(100,e.height-100),this.knobPosition=new l(100,e.height-100),this.maxDistance=50,this.deadZone=.1,this.baseRadius=60,this.knobRadius=25,this.baseColor="rgba(255, 255, 255, 0.3)",this.knobColor="rgba(255, 255, 255, 0.7)"}handleTouchStart(e,t){t.distance(this.center)<=this.baseRadius&&(this.active=!0,this.touchId=e,this.knobPosition.setFromVector(t),this.clampKnobPosition())}handleTouchMove(e,t){this.active&&this.touchId===e&&(this.knobPosition.setFromVector(t),this.clampKnobPosition())}handleTouchEnd(e){this.active&&this.touchId===e&&(this.active=!1,this.touchId=null,this.knobPosition.setFromVector(this.center))}clampKnobPosition(){const e=this.knobPosition.subtract(this.center);e.magnitude()>this.maxDistance&&(e.normalizeInPlace().multiplyInPlace(this.maxDistance),this.knobPosition=this.center.add(e))}getInput(){if(!this.active)return new l(0,0);const e=this.knobPosition.subtract(this.center),t=e.magnitude()/this.maxDistance;return t<this.deadZone?new l(0,0):e.normalize().multiply(t)}isActive(){return this.active}update(){this.center.set(100,this.canvas.height-100),this.active||this.knobPosition.setFromVector(this.center)}render(e){e.save(),e.fillStyle=this.baseColor,e.strokeStyle="rgba(255, 255, 255, 0.5)",e.lineWidth=2,e.beginPath(),e.arc(this.center.x,this.center.y,this.baseRadius,0,Math.PI*2),e.fill(),e.stroke(),e.fillStyle=this.knobColor,e.beginPath(),e.arc(this.knobPosition.x,this.knobPosition.y,this.knobRadius,0,Math.PI*2),e.fill(),e.stroke(),e.restore()}destroy(){}}var P;let D=(P=class{constructor(e=0,t=0){this.position=new l(e,t),this.velocity=new l(0,0),this.acceleration=new l(0,0),this.rotation=0,this.scale=new l(1,1),this.active=!0,this.visible=!0,this.destroyed=!1,this.collisionRadius=0,this.collisionBounds={x:0,y:0,width:0,height:0},this.id=P.generateId(),this.tags=new Set}static generateId(){return++P.idCounter}update(e){this.active&&(this.velocity.addInPlace(this.acceleration.multiply(e/1e3)),this.position.addInPlace(this.velocity.multiply(e/1e3)),this.updateCollisionBounds())}render(e,t=0){if(!this.visible)return;const i=this.position.add(this.velocity.multiply(t/1e3));e.save(),e.translate(i.x,i.y),e.rotate(this.rotation),e.scale(this.scale.x,this.scale.y),this.collisionRadius>0&&(e.strokeStyle="#ff0000",e.beginPath(),e.arc(0,0,this.collisionRadius,0,Math.PI*2),e.stroke()),e.restore()}updateCollisionBounds(){this.collisionBounds.x=this.position.x-this.collisionRadius,this.collisionBounds.y=this.position.y-this.collisionRadius,this.collisionBounds.width=this.collisionRadius*2,this.collisionBounds.height=this.collisionRadius*2}collidesWith(e){return!this.active||!e.active?!1:this.collisionRadius>0&&e.collisionRadius>0?this.position.distance(e.position)<this.collisionRadius+e.collisionRadius:!1}destroy(){this.destroyed=!0,this.active=!1,this.visible=!1}reset(){this.position.set(0,0),this.velocity.set(0,0),this.acceleration.set(0,0),this.rotation=0,this.scale.set(1,1),this.active=!0,this.visible=!0,this.destroyed=!1,this.tags.clear()}addTag(e){this.tags.add(e)}removeTag(e){this.tags.delete(e)}hasTag(e){return this.tags.has(e)}distanceTo(e){return this.position.distance(e.position)}directionTo(e){return e.position.subtract(this.position).normalize()}lookAt(e){const t=this.directionTo(e);this.rotation=t.angle()}moveTowards(e,t,i){const n=this.directionTo(e).multiply(t*i/1e3);this.position.addInPlace(n)}applyForce(e){this.acceleration.addInPlace(e)}isOutOfBounds(e){return this.position.x<e.left||this.position.x>e.right||this.position.y<e.top||this.position.y>e.bottom}wrapAroundBounds(e){this.position.x<e.left&&(this.position.x=e.right),this.position.x>e.right&&(this.position.x=e.left),this.position.y<e.top&&(this.position.y=e.bottom),this.position.y>e.bottom&&(this.position.y=e.top)}clampToBounds(e){this.position.x=Math.max(e.left,Math.min(e.right,this.position.x)),this.position.y=Math.max(e.top,Math.min(e.bottom,this.position.y))}},R(P,"idCounter",0),P);class I extends D{constructor(e=0,t=0){super(e,t),this.speed=600,this.damage=1,this.lifetime=3e3,this.age=0,this.width=4,this.height=12,this.collisionRadius=3,this.color="#FFD700",this.trailColor="#FFA500",this.trailPositions=[],this.maxTrailLength=8,this.trailFadeRate=.8,this.type="player",this.owner=null,this.addTag("projectile"),this.active=!1,this.visible=!1}initialize(e,t,i=600,s="player",n=null){return this.position.setFromVector(e),this.velocity=t.normalize().multiply(i),this.speed=i,this.type=s,this.owner=n,this.age=0,this.trailPositions=[],this.setupVisualsByType(),this.active=!0,this.visible=!0,this.destroyed=!1,this.tags.clear(),this.addTag("projectile"),this.addTag(s+"Projectile"),this}setupVisualsByType(){switch(this.type){case"player":this.color="#FFD700",this.trailColor="#FFA500",this.width=4,this.height=12;break;case"enemy":this.color="#FF4444",this.trailColor="#FF8888",this.width=3,this.height=8;break;default:this.color="#FFFFFF",this.trailColor="#CCCCCC";break}}update(e){if(this.active){if(this.age+=e,this.age>=this.lifetime){this.destroy();return}this.updateTrail(),super.update(e),this.updateCollisionBounds()}}updateTrail(){this.trailPositions.unshift(this.position.clone()),this.trailPositions.length>this.maxTrailLength&&this.trailPositions.pop()}render(e,t=0){if(!this.visible)return;const i=this.position.add(this.velocity.multiply(t/1e3));e.save(),this.renderTrail(e,t),this.renderProjectile(e,i),window.DEBUG_MODE&&this.renderDebugInfo(e,i),e.restore()}renderTrail(e,t){if(!(this.trailPositions.length<2)){e.strokeStyle=this.trailColor,e.lineWidth=2,e.lineCap="round";for(let i=0;i<this.trailPositions.length-1;i++){const s=Math.pow(this.trailFadeRate,i),n=this.trailPositions[i],a=this.trailPositions[i+1];let r=n;if(i===0){const h=this.velocity.multiply(t/1e3);r=n.add(h)}e.globalAlpha=s,e.beginPath(),e.moveTo(r.x,r.y),e.lineTo(a.x,a.y),e.stroke()}e.globalAlpha=1}}renderProjectile(e,t){e.translate(t.x,t.y),e.fillStyle=this.color,e.fillRect(-this.width/2,-this.height/2,this.width,this.height),e.fillStyle="#FFFFFF",e.fillRect(-1,-this.height/2,2,this.height)}drawPlayerProjectile(e){e.beginPath(),e.moveTo(0,-this.height/2),e.lineTo(this.width/2,0),e.lineTo(0,this.height/2),e.lineTo(-this.width/2,0),e.closePath(),e.fill(),e.stroke(),e.fillStyle=this.lightenColor(this.color,.3),e.beginPath(),e.moveTo(0,-this.height/4),e.lineTo(this.width/4,0),e.lineTo(0,this.height/4),e.lineTo(-this.width/4,0),e.closePath(),e.fill()}drawEnemyProjectile(e){e.beginPath(),e.ellipse(0,0,this.width/2,this.height/2,0,0,Math.PI*2),e.fill(),e.stroke(),e.fillStyle=this.lightenColor(this.color,.2),e.beginPath(),e.ellipse(0,-this.height/6,this.width/4,this.height/4,0,0,Math.PI*2),e.fill()}renderDebugInfo(e,t){e.resetTransform(),e.strokeStyle="#FF0000",e.lineWidth=1,e.beginPath(),e.arc(t.x,t.y,this.collisionRadius,0,Math.PI*2),e.stroke(),e.strokeStyle="#00FF00",e.lineWidth=1,e.beginPath(),e.moveTo(t.x,t.y);const i=t.add(this.velocity.multiply(.05));e.lineTo(i.x,i.y),e.stroke(),e.fillStyle="#FFFF00",e.font="10px Arial",e.fillText(`${Math.floor(this.age)}ms`,t.x+10,t.y-10)}isOutOfBounds(e){const t=Math.max(this.width,this.height);return this.position.x<e.left-t||this.position.x>e.right+t||this.position.y<e.top-t||this.position.y>e.bottom+t}onCollision(e){this.destroy()}reset(){super.reset(),this.age=0,this.trailPositions=[],this.type="player",this.owner=null,this.speed=600,this.damage=1,this.lifetime=3e3}darkenColor(e,t){const i=e.replace("#",""),s=Math.floor(parseInt(i.substr(0,2),16)*(1-t)),n=Math.floor(parseInt(i.substr(2,2),16)*(1-t)),a=Math.floor(parseInt(i.substr(4,2),16)*(1-t));return`rgb(${s}, ${n}, ${a})`}lightenColor(e,t){const i=e.replace("#",""),s=Math.min(255,Math.floor(parseInt(i.substr(0,2),16)*(1+t))),n=Math.min(255,Math.floor(parseInt(i.substr(2,2),16)*(1+t))),a=Math.min(255,Math.floor(parseInt(i.substr(4,2),16)*(1+t)));return`rgb(${s}, ${n}, ${a})`}}class O{constructor(e,t){this.owner=e,this.gameObjectManager=t,this.fireRate=300,this.lastFireTime=0,this.canFire=!0,this.projectileSpeed=600,this.projectileDamage=25,this.projectileLifetime=3e3,this.projectileType="player",this.currentPattern="single",this.previousPattern="single",this.spreadAngle=Math.PI/6,this.muzzleFlashDuration=100,this.muzzleFlashTime=0,this.muzzleFlashPositions=[],this.fireSound=null,this.soundVolume=.5,this.initializeProjectilePool(),console.log("WeaponSystem initialized for owner:",e.constructor.name)}initializeProjectilePool(){this.gameObjectManager.pools.has("projectile")||this.gameObjectManager.createPool("projectile",()=>new I,e=>e.reset(),20)}update(e){this.canFire||(this.lastFireTime+=e,this.lastFireTime>=this.fireRate&&(this.canFire=!0,this.lastFireTime=0)),this.muzzleFlashTime>0&&(this.muzzleFlashTime-=e,this.muzzleFlashTime<=0&&(this.muzzleFlashPositions=[]))}fire(e=l.up()){if(console.log("WeaponSystem.fire() called, canFire:",this.canFire),!this.canFire)return console.log("Cannot fire - weapon on cooldown"),!1;switch(this.currentPattern){case"single":this.fireSingle(e);break;case"double":this.fireDouble(e);break;case"triple":this.fireTriple(e);break;case"spread":this.fireSpread(e);break;default:this.fireSingle(e);break}return this.canFire=!1,this.lastFireTime=0,this.triggerMuzzleFlash(),this.playFireSound(),!0}fireSingle(e){const t=this.getFirePosition();this.createProjectile(t,e)}fireDouble(e){const t=this.getFirePosition(),i=e.perpendicular().multiply(8);this.createProjectile(t.subtract(i),e),this.createProjectile(t.add(i),e)}fireTriple(e){const t=this.getFirePosition(),i=e.perpendicular().multiply(12);this.createProjectile(t,e),this.createProjectile(t.subtract(i),e),this.createProjectile(t.add(i),e)}fireSpread(e){const t=this.getFirePosition(),i=e.angle(),s=this.spreadAngle/2;for(let n=-2;n<=2;n++){const a=i+n*s,r=l.fromAngle(a);this.createProjectile(t,r)}}createProjectile(e,t){const i=new I;i.initialize(e,t,this.projectileSpeed,this.projectileType,this.owner),i.damage=this.projectileDamage,i.lifetime=this.projectileLifetime,this.gameObjectManager.add(i),console.log("Projectile created at:",i.position.toString(),"with velocity:",i.velocity.toString())}getFirePosition(){const e=this.owner.position.clone(),t=new l(0,-this.owner.height/2-5);return e.add(t)}triggerMuzzleFlash(){switch(this.muzzleFlashTime=this.muzzleFlashDuration,this.muzzleFlashPositions=[],this.currentPattern){case"single":this.muzzleFlashPositions.push(this.getFirePosition());break;case"double":const e=this.getFirePosition(),t=new l(8,0);this.muzzleFlashPositions.push(e.subtract(t)),this.muzzleFlashPositions.push(e.add(t));break;case"triple":const i=this.getFirePosition(),s=new l(12,0);this.muzzleFlashPositions.push(i),this.muzzleFlashPositions.push(i.subtract(s)),this.muzzleFlashPositions.push(i.add(s));break;case"spread":this.muzzleFlashPositions.push(this.getFirePosition());break}}playFireSound(){this.fireSound&&typeof this.fireSound.play=="function"&&(this.fireSound.volume=this.soundVolume,this.fireSound.currentTime=0,this.fireSound.play().catch(e=>{console.warn("Could not play fire sound:",e)}))}render(e){this.muzzleFlashTime>0&&this.renderMuzzleFlash(e)}renderMuzzleFlash(e){const t=this.muzzleFlashTime/this.muzzleFlashDuration;e.save(),e.globalAlpha=t;for(const i of this.muzzleFlashPositions){const s=e.createRadialGradient(i.x,i.y,0,i.x,i.y,15);s.addColorStop(0,"#FFFFFF"),s.addColorStop(.3,"#FFD700"),s.addColorStop(.6,"#FF6B35"),s.addColorStop(1,"rgba(255, 107, 53, 0)"),e.fillStyle=s,e.beginPath(),e.arc(i.x,i.y,15,0,Math.PI*2),e.fill(),e.fillStyle="#FFFFFF",e.beginPath(),e.arc(i.x,i.y,3,0,Math.PI*2),e.fill()}e.restore()}setPattern(e){["single","double","triple","spread"].includes(e)?(this.currentPattern=e,console.log(`Weapon pattern changed to: ${e}`)):console.warn(`Invalid weapon pattern: ${e}`)}setFireRate(e){this.fireRate=Math.max(50,e)}setProjectileSpeed(e){this.projectileSpeed=Math.max(100,e)}setProjectileDamage(e){this.projectileDamage=Math.max(1,e)}setSpreadAngle(e){this.spreadAngle=Math.max(0,Math.min(Math.PI,e))}isReady(){return this.canFire}getCooldownProgress(){return this.canFire?1:this.lastFireTime/this.fireRate}resetCooldown(){this.canFire=!0,this.lastFireTime=0}enableSpreadPattern(e){if(e)this.previousPattern=this.currentPattern,this.setPattern("spread");else{const t=this.previousPattern||"single";this.setPattern(t)}}getStats(){return{pattern:this.currentPattern,fireRate:this.fireRate,projectileSpeed:this.projectileSpeed,projectileDamage:this.projectileDamage,isReady:this.canFire,cooldownProgress:this.getCooldownProgress()}}}class $ extends D{constructor(e,t,i,s,n=null){super(e,t),this.canvasWidth=i,this.canvasHeight=s,this.maxSpeed=300,this.acceleration=800,this.friction=.85,this.maxHealth=100,this.health=this.maxHealth,this.maxLives=3,this.lives=this.maxLives,this.isInvulnerable=!1,this.invulnerabilityDuration=2e3,this.invulnerabilityTimer=0,this.isDestroyed=!1,this.damageFlashTimer=0,this.damageFlashDuration=200,this.isFlashing=!1,this.width=32,this.height=48,this.collisionRadius=16,this.boundaryPadding=Math.max(this.width,this.height)/2,this.animationTime=0,this.thrusterAnimationSpeed=8,this.isMoving=!1,this.movementInput=new l(0,0),this.weaponSystem=null,n&&(this.weaponSystem=new O(this,n)),this.addTag("player"),console.log("PlayerShip created at position:",this.position.toString())}update(e,t=new l(0,0)){if(this.active){if(this.movementInput=t.clone(),this.isMoving=t.magnitude()>.1,this.isMoving){const n=t.multiply(this.maxSpeed).subtract(this.velocity).multiply(this.acceleration*e/1e3);this.velocity.addInPlace(n),this.velocity.magnitude()>this.maxSpeed&&(this.velocity=this.velocity.normalize().multiply(this.maxSpeed))}else this.velocity.multiplyInPlace(Math.pow(this.friction,e/16.67)),this.velocity.magnitude()<1&&this.velocity.set(0,0);this.position.addInPlace(this.velocity.multiply(e/1e3)),this.checkBoundaries(),this.animationTime+=e/1e3,this.updateHealthSystem(e),this.updateCollisionBounds(),this.weaponSystem&&this.weaponSystem.update(e)}}checkBoundaries(){const e=this.boundaryPadding,t=this.canvasWidth-this.boundaryPadding,i=this.boundaryPadding,s=this.canvasHeight-this.boundaryPadding;this.position.x<e?(this.position.x=e,this.velocity.x=Math.max(0,this.velocity.x)):this.position.x>t&&(this.position.x=t,this.velocity.x=Math.min(0,this.velocity.x)),this.position.y<i?(this.position.y=i,this.velocity.y=Math.max(0,this.velocity.y)):this.position.y>s&&(this.position.y=s,this.velocity.y=Math.min(0,this.velocity.y))}render(e,t=0){if(!this.visible)return;const i=this.position.add(this.velocity.multiply(t/1e3));e.save(),e.translate(i.x,i.y),e.rotate(this.rotation),this.drawShipBody(e),this.isMoving&&this.drawThrusterEffects(e),window.DEBUG_MODE&&this.drawDebugInfo(e),e.restore(),this.weaponSystem&&this.weaponSystem.render(e)}drawShipBody(e){let t=1,i="#4A90E2",s="#2E5C8A";if(this.isInvulnerable){const a=Math.sin(this.invulnerabilityTimer*8*Math.PI/1e3);t=.3+.7*Math.abs(a)}if(this.isFlashing){const n=this.damageFlashTimer/this.damageFlashDuration;i=this.interpolateColor("#4A90E2","#FF4444",n),s=this.interpolateColor("#2E5C8A","#CC2222",n)}e.globalAlpha=t,e.fillStyle=i,e.strokeStyle=s,e.lineWidth=2,e.beginPath(),e.moveTo(0,-this.height/2),e.lineTo(-this.width/3,this.height/3),e.lineTo(this.width/3,this.height/3),e.closePath(),e.fill(),e.stroke(),e.fillStyle="#7BB3F0",e.beginPath(),e.ellipse(0,-this.height/4,this.width/6,this.height/8,0,0,Math.PI*2),e.fill(),e.fillStyle="#2E5C8A",e.fillRect(-this.width/4,this.height/6,this.width/8,this.height/4),e.fillRect(this.width/8,this.height/6,this.width/8,this.height/4),e.fillStyle="#87CEEB",e.beginPath(),e.ellipse(-this.width/6,this.height/3,3,6,0,0,Math.PI*2),e.ellipse(this.width/6,this.height/3,3,6,0,0,Math.PI*2),e.fill(),e.globalAlpha=1}drawThrusterEffects(e){const t=this.velocity.magnitude()/this.maxSpeed,i=Math.sin(this.animationTime*this.thrusterAnimationSpeed*Math.PI*2),s=15*t,n=5*i*t,a=s+n;a>2&&(this.drawThrusterFlame(e,-this.width/6,this.height/3,a),this.drawThrusterFlame(e,this.width/6,this.height/3,a)),this.drawDirectionalThrusters(e,t,i)}drawThrusterFlame(e,t,i,s){const n=e.createLinearGradient(t,i,t,i+s);n.addColorStop(0,"#FFD700"),n.addColorStop(.5,"#FF6B35"),n.addColorStop(1,"rgba(255, 0, 0, 0)"),e.fillStyle=n,e.beginPath(),e.moveTo(t-3,i),e.lineTo(t+3,i),e.lineTo(t+1,i+s),e.lineTo(t-1,i+s),e.closePath(),e.fill()}drawDirectionalThrusters(e,t,i){const s=3*t,n=.7*t;if(Math.abs(this.movementInput.x)>.1&&(e.fillStyle=`rgba(135, 206, 235, ${n})`,this.movementInput.x>0?e.fillRect(-this.width/2-s,-2,s,4):e.fillRect(this.width/2,-2,s,4)),this.movementInput.y<-.1){e.fillStyle=`rgba(255, 215, 0, ${n})`;const a=8*t*(1+.3*i);e.fillRect(-2,-this.height/2-a,4,a)}}drawDebugInfo(e){if(e.strokeStyle="#FF0000",e.lineWidth=1,e.beginPath(),e.arc(0,0,this.collisionRadius,0,Math.PI*2),e.stroke(),this.velocity.magnitude()>1){e.strokeStyle="#00FF00",e.lineWidth=2,e.beginPath(),e.moveTo(0,0);const t=.1;e.lineTo(this.velocity.x*t,this.velocity.y*t),e.stroke()}e.fillStyle="#FFFF00",e.fillRect(-1,-1,2,2)}getCurrentSpeed(){return this.velocity.magnitude()}getBoundaryStatus(){const e=this.boundaryPadding,t=this.canvasWidth-this.boundaryPadding,i=this.boundaryPadding,s=this.canvasHeight-this.boundaryPadding;return{left:this.position.x<=e,right:this.position.x>=t,top:this.position.y<=i,bottom:this.position.y>=s}}resetToPosition(e,t){this.position.set(e,t),this.velocity.set(0,0),this.rotation=0,this.animationTime=0,this.isMoving=!1,this.movementInput.set(0,0),this.active=!0,this.visible=!0,this.destroyed=!1}updateCanvasDimensions(e,t){this.canvasWidth=e,this.canvasHeight=t,this.checkBoundaries()}fire(e=l.up()){if(console.log("PlayerShip.fire() called, weaponSystem exists:",!!this.weaponSystem),this.weaponSystem){const t=this.weaponSystem.fire(e);return console.log("WeaponSystem.fire() returned:",t),t}return!1}setWeaponSystem(e){this.weaponSystem=e}getWeaponSystem(){return this.weaponSystem}canFire(){return this.weaponSystem?this.weaponSystem.isReady():!1}setWeaponPattern(e){this.weaponSystem&&this.weaponSystem.setPattern(e)}getWeaponStats(){return this.weaponSystem?this.weaponSystem.getStats():null}updateHealthSystem(e){this.isInvulnerable&&(this.invulnerabilityTimer-=e,this.invulnerabilityTimer<=0&&(this.isInvulnerable=!1,this.invulnerabilityTimer=0)),this.isFlashing&&(this.damageFlashTimer-=e,this.damageFlashTimer<=0&&(this.isFlashing=!1,this.damageFlashTimer=0))}takeDamage(e){if(this.isInvulnerable||this.isDestroyed)return{damageTaken:0,health:this.health,lives:this.lives,destroyed:this.isDestroyed};const t=Math.min(e,this.health);return this.health-=t,this.isFlashing=!0,this.damageFlashTimer=this.damageFlashDuration,console.log(`PlayerShip took ${t} damage. Health: ${this.health}/${this.maxHealth}, Lives: ${this.lives}`),this.health<=0?this.destroyShip():(this.isInvulnerable=!0,this.invulnerabilityTimer=this.invulnerabilityDuration),{damageTaken:t,health:this.health,lives:this.lives,destroyed:this.isDestroyed}}destroyShip(){this.health=0,this.lives--,console.log(`PlayerShip destroyed! Lives remaining: ${this.lives}`),this.lives<=0?(this.isDestroyed=!0,this.active=!1,console.log("Game Over - No lives remaining")):this.respawn()}respawn(){this.health=this.maxHealth;const e=this.canvasWidth/2,t=this.canvasHeight-100;this.resetToPosition(e,t),this.isInvulnerable=!0,this.invulnerabilityTimer=this.invulnerabilityDuration*2,this.isFlashing=!1,this.damageFlashTimer=0,console.log(`PlayerShip respawned with full health. Lives: ${this.lives}`)}heal(e){if(this.isDestroyed)return 0;const t=Math.min(e,this.maxHealth-this.health);return this.health+=t,console.log(`PlayerShip healed for ${t}. Health: ${this.health}/${this.maxHealth}`),t}addLives(e){this.lives+=e,console.log(`PlayerShip gained ${e} lives. Total lives: ${this.lives}`)}getHealthStatus(){return{health:this.health,maxHealth:this.maxHealth,healthPercentage:this.health/this.maxHealth,lives:this.lives,maxLives:this.maxLives,isInvulnerable:this.isInvulnerable,invulnerabilityTimeRemaining:this.invulnerabilityTimer,isDestroyed:this.isDestroyed,isFlashing:this.isFlashing}}resetHealthAndLives(){this.health=this.maxHealth,this.lives=this.maxLives,this.isInvulnerable=!1,this.invulnerabilityTimer=0,this.isDestroyed=!1,this.isFlashing=!1,this.damageFlashTimer=0,console.log("PlayerShip health and lives reset to maximum")}interpolateColor(e,t,i){i=Math.max(0,Math.min(1,i));const s=e.replace("#",""),n=t.replace("#",""),a=parseInt(s.substr(0,2),16),r=parseInt(s.substr(2,2),16),h=parseInt(s.substr(4,2),16),d=parseInt(n.substr(0,2),16),m=parseInt(n.substr(2,2),16),p=parseInt(n.substr(4,2),16),f=Math.round(a+(d-a)*i),g=Math.round(r+(m-r)*i),v=Math.round(h+(p-h)*i),E=A=>{const C=A.toString(16);return C.length===1?"0"+C:C};return`#${E(f)}${E(g)}${E(v)}`}}class G{constructor(e,t,i=10){this.createFn=e,this.resetFn=t,this.pool=[],this.active=[];for(let s=0;s<i;s++)this.pool.push(this.createFn())}get(){let e;return this.pool.length>0?e=this.pool.pop():e=this.createFn(),this.active.push(e),e}release(e){const t=this.active.indexOf(e);t!==-1&&(this.active.splice(t,1),this.resetFn(e),this.pool.push(e))}releaseAll(){for(;this.active.length>0;){const e=this.active.pop();this.resetFn(e),this.pool.push(e)}}getStats(){return{pooled:this.pool.length,active:this.active.length,total:this.pool.length+this.active.length}}}class z{constructor(){this.objects=[],this.objectsToAdd=[],this.objectsToRemove=[],this.pools=new Map}add(e){this.objectsToAdd.push(e)}remove(e){this.objectsToRemove.push(e)}createPool(e,t,i,s=10){this.pools.set(e,new G(t,i,s))}getFromPool(e){const t=this.pools.get(e);if(t)return t.get();throw new Error(`Pool for type '${e}' not found`)}returnToPool(e,t){const i=this.pools.get(e);i&&(i.release(t),this.remove(t))}update(e){this.processAdditions(),this.processRemovals();for(let t=this.objects.length-1;t>=0;t--){const i=this.objects[t];if(i.destroyed){this.objectsToRemove.push(i);continue}i.active&&i.update(e)}}render(e,t=0){for(const i of this.objects)i.visible&&!i.destroyed&&i.render(e,t)}processAdditions(){this.objectsToAdd.length>0&&(this.objects.push(...this.objectsToAdd),this.objectsToAdd.length=0)}processRemovals(){if(this.objectsToRemove.length>0){for(const e of this.objectsToRemove){const t=this.objects.indexOf(e);t!==-1&&this.objects.splice(t,1)}this.objectsToRemove.length=0}}findByTag(e){return this.objects.filter(t=>t.hasTag(e))}findById(e){return this.objects.find(t=>t.id===e)}getActive(){return this.objects.filter(e=>e.active&&!e.destroyed)}getVisible(){return this.objects.filter(e=>e.visible&&!e.destroyed)}checkCollisions(e,t,i){const s=this.findByTag(e),n=this.findByTag(t);for(const a of s)if(a.active)for(const r of n)r.active&&a.collidesWith(r)&&i(a,r)}checkCollisionsOptimized(e,t,i,s=64){const n=this.findByTag(e),a=this.findByTag(t),r=new Map;for(const h of a){if(!h.active)continue;const d=Math.floor(h.position.x/s),m=Math.floor(h.position.y/s),p=`${d},${m}`;r.has(p)||r.set(p,[]),r.get(p).push(h)}for(const h of n){if(!h.active)continue;const d=Math.floor(h.position.x/s),m=Math.floor(h.position.y/s);for(let p=-1;p<=1;p++)for(let f=-1;f<=1;f++){const g=`${d+p},${m+f}`,v=r.get(g);if(v)for(const E of v)h.collidesWith(E)&&i(h,E)}}}clear(){this.objects.length=0,this.objectsToAdd.length=0,this.objectsToRemove.length=0;for(const e of this.pools.values())e.releaseAll()}getStats(){const e={};for(const[t,i]of this.pools.entries())e[t]=i.getStats();return{totalObjects:this.objects.length,activeObjects:this.getActive().length,visibleObjects:this.getVisible().length,pendingAdditions:this.objectsToAdd.length,pendingRemovals:this.objectsToRemove.length,pools:e}}}const T={CANVAS_WIDTH:800,CANVAS_HEIGHT:600,BASE_LEVEL_REWARD:50,POWER_UP_COSTS:{EXTRA_WINGMAN:100,EXTRA_LIFE:150,SPREAD_AMMO:75}},u={SPACE:"space",UNDERWATER:"underwater",VOLCANIC:"volcanic",CRYSTAL:"crystal",FOREST:"forest",DESERT:"desert",ICE:"ice"},o={WATER:"water",FIRE:"fire",AIR:"air",EARTH:"earth",CRYSTAL:"crystal",SHADOW:"shadow"};class j{constructor(e=null){this.gameObjectManager=e,this.currentLevel=1,this.levelInProgress=!1,this.levelStartTime=0,this.levelCompletionTime=0,this.levelConfig=null,this.maxLevels=50,this.currentScore=0,this.levelScore=0,this.enemiesDefeated=0,this.levelEnemiesDefeated=0,this.totalScore=0,this.levelStartScore=0,this.perfectCompletion=!0,this.speedBonus=0,this.accuracyBonus=0,this.requiredEnemiesDefeated=0,this.wavesCompleted=0,this.requiredWaves=0,this.onLevelStartCallback=null,this.onLevelCompleteCallback=null,this.onScoreUpdateCallback=null,console.log("LevelManager initialized")}startLevel(e=null){return e!==null&&(this.currentLevel=e),this.levelConfig=this.generateLevelConfig(this.currentLevel),this.levelInProgress=!0,this.levelStartTime=performance.now(),this.levelCompletionTime=0,this.levelScore=0,this.levelEnemiesDefeated=0,this.levelStartScore=this.currentScore,this.perfectCompletion=!0,this.speedBonus=0,this.accuracyBonus=0,this.requiredEnemiesDefeated=this.levelConfig.totalEnemies,this.requiredWaves=this.levelConfig.totalWaves,this.wavesCompleted=0,console.log(`Starting Level ${this.currentLevel}:`,this.levelConfig),this.onLevelStartCallback&&this.onLevelStartCallback(this.currentLevel,this.levelConfig),this.levelConfig}generateLevelConfig(e){const t=Math.min(3,1+(e-1)*.1),i=8,s=Math.floor((e-1)/2),n=Math.floor((i+s)*t),a=3,r=Math.floor((e-1)/3),h=Math.min(8,a+r),d=this.selectLevelEnvironment(e),m=e%10===0,p=e%5===0,f=120,g=Math.min(60,e*2),v=f+g,A=Math.floor(1e3*t*e);return{levelNumber:e,difficulty:Math.min(10,Math.floor(e/5)+1),totalEnemies:n,totalWaves:h,environment:d,hasBoss:m,hasSpecialMechanics:p,timeLimit:v,scoreTarget:A,completionReward:this.calculateBaseLevelReward(e),difficultyMultiplier:t,enemyDistribution:this.generateEnemyDistribution(e,d),conditions:this.generateLevelConditions(e),backgroundMusic:this.selectBackgroundMusic(e,d),visualEffects:this.selectVisualEffects(e,d)}}selectLevelEnvironment(e){if(e<=5)return u.SPACE;const t=[u.SPACE,u.UNDERWATER,u.VOLCANIC,u.CRYSTAL,u.FOREST,u.DESERT,u.ICE];if(e%10===0){const a=[u.VOLCANIC,u.CRYSTAL,u.ICE];return a[Math.floor((e/10-1)%a.length)]}const i=Math.floor((e-6)/3)%t.length,s=(e+7)%3,n=(i+s)%t.length;return t[n]}generateEnemyDistribution(e,t){const i={};return e<=3?(i.basic=.7,i.advanced=.3,i.elite=0):e<=10?(i.basic=.5,i.advanced=.4,i.elite=.1):(i.basic=.3,i.advanced=.5,i.elite=.2),i.environmentalBonus=this.getEnvironmentalEnemyBonus(t),i}getEnvironmentalEnemyBonus(e){return{[u.SPACE]:{air:1.2,crystal:1.1},[u.UNDERWATER]:{water:1.5,earth:.8},[u.VOLCANIC]:{fire:1.6,earth:1.3},[u.CRYSTAL]:{crystal:1.8,shadow:1.2},[u.FOREST]:{earth:1.4,shadow:1.3},[u.DESERT]:{fire:1.2,earth:1.1},[u.ICE]:{water:1.3,air:.7}}[e]||{}}generateLevelConditions(e){const t={primary:"defeat_all_enemies",secondary:[],bonus:[]};return e>=3&&t.secondary.push("complete_under_time_limit"),e>=5&&t.secondary.push("maintain_accuracy_above_70"),e>=7&&t.secondary.push("take_minimal_damage"),t.bonus.push("perfect_accuracy"),t.bonus.push("speed_completion"),t.bonus.push("no_damage_taken"),t}selectBackgroundMusic(e,t){return e%10===0?"boss_theme":{[u.SPACE]:"space_ambient",[u.UNDERWATER]:"underwater_theme",[u.VOLCANIC]:"volcanic_intensity",[u.CRYSTAL]:"crystal_harmony",[u.FOREST]:"forest_mystery",[u.DESERT]:"desert_winds",[u.ICE]:"ice_caverns"}[t]||"default_theme"}selectVisualEffects(e,t){const i=[];switch(t){case u.UNDERWATER:i.push("water_bubbles","light_rays");break;case u.VOLCANIC:i.push("lava_particles","heat_distortion");break;case u.CRYSTAL:i.push("crystal_reflections","energy_pulses");break;case u.FOREST:i.push("floating_spores","dappled_light");break;case u.DESERT:i.push("sand_particles","heat_shimmer");break;case u.ICE:i.push("snow_particles","ice_crystals");break;default:i.push("star_field","nebula_clouds")}return e>=10&&i.push("intensity_overlay"),i}update(e,t={}){this.levelInProgress&&(this.levelCompletionTime=performance.now()-this.levelStartTime,this.checkLevelCompletion(t),this.updatePerformanceMetrics(t))}checkLevelCompletion(e){if(!this.levelInProgress||!this.levelConfig)return;const t=this.levelEnemiesDefeated>=this.requiredEnemiesDefeated,i=this.wavesCompleted>=this.requiredWaves;t&&i&&this.completeLevel();const s=this.levelCompletionTime>this.levelConfig.timeLimit*1e3,n=e.playerDestroyed||!1;(s||n)&&this.failLevel(s?"time_expired":"player_destroyed")}completeLevel(){if(!this.levelInProgress)return;this.levelInProgress=!1;const e=this.levelCompletionTime/1e3,t=this.calculateLevelScore(e);console.log(`Level ${this.currentLevel} completed!`,{time:e.toFixed(2)+"s",score:t.totalScore,enemies:this.levelEnemiesDefeated}),this.currentScore+=t.totalScore,this.totalScore=this.currentScore;const i={levelNumber:this.currentLevel,completed:!0,completionTime:e,score:t,enemiesDefeated:this.levelEnemiesDefeated,perfectCompletion:this.perfectCompletion,bonuses:t.bonuses,nextLevel:this.currentLevel+1};return this.onLevelCompleteCallback&&this.onLevelCompleteCallback(i),i}failLevel(e){if(!this.levelInProgress)return;this.levelInProgress=!1;const t=this.levelCompletionTime/1e3;console.log(`Level ${this.currentLevel} failed: ${e}`);const i={levelNumber:this.currentLevel,completed:!1,reason:e,completionTime:t,score:this.levelScore,enemiesDefeated:this.levelEnemiesDefeated,canRetry:!0};return this.onLevelCompleteCallback&&this.onLevelCompleteCallback(i),i}calculateLevelScore(e){const t=this.levelConfig,i=this.levelScore,s=this.calculateTimeBonus(e,t.timeLimit),n=this.calculateAccuracyBonus(),a=this.perfectCompletion?Math.floor(i*.5):0,r=t.completionReward,h=t.difficultyMultiplier,d=i+s+n+a+r,m=Math.floor(d*h);return{enemyScore:i,timeBonus:s,accuracyBonus:n,perfectBonus:a,completionBonus:r,difficultyMultiplier:h,totalScore:m,bonuses:{speed:s>0,accuracy:n>0,perfect:a>0}}}calculateTimeBonus(e,t){const i=t*.6;if(e<=i){const n=e/i;return Math.floor(500*(2-n))}else{if(e<=t*.8)return 200;if(e<=t)return 50}return 0}calculateAccuracyBonus(){return this.accuracyBonus}updatePerformanceMetrics(e){if(e.playerDamageTaken&&(this.perfectCompletion=!1),e.shotsFired&&e.shotsHit){const t=e.shotsHit/e.shotsFired;t>=.9?this.accuracyBonus=300:t>=.7?this.accuracyBonus=150:t>=.5&&(this.accuracyBonus=50)}}recordEnemyDefeat(e,t){this.enemiesDefeated++,this.levelEnemiesDefeated++,this.levelScore+=t,this.currentScore+=t,this.onScoreUpdateCallback&&this.onScoreUpdateCallback({enemiesDefeated:this.enemiesDefeated,levelEnemiesDefeated:this.levelEnemiesDefeated,currentScore:this.currentScore,levelScore:this.levelScore,scoreGained:t})}recordWaveCompletion(e,t=0){this.wavesCompleted++,t>0&&(this.levelScore+=t,this.currentScore+=t),console.log(`Wave ${e} completed. Waves: ${this.wavesCompleted}/${this.requiredWaves}`),this.onScoreUpdateCallback&&this.onScoreUpdateCallback({wavesCompleted:this.wavesCompleted,currentScore:this.currentScore,levelScore:this.levelScore,scoreGained:t})}calculateBaseLevelReward(e){const t=T.BASE_LEVEL_REWARD,i=Math.floor(e/5)+1;return t*i}getLevelStatus(){return{currentLevel:this.currentLevel,levelInProgress:this.levelInProgress,levelConfig:this.levelConfig,completionTime:this.levelCompletionTime,score:{current:this.currentScore,level:this.levelScore,total:this.totalScore},progress:{enemiesDefeated:this.levelEnemiesDefeated,requiredEnemies:this.requiredEnemiesDefeated,wavesCompleted:this.wavesCompleted,requiredWaves:this.requiredWaves},performance:{perfectCompletion:this.perfectCompletion,speedBonus:this.speedBonus,accuracyBonus:this.accuracyBonus}}}reset(){this.currentLevel=1,this.levelInProgress=!1,this.levelStartTime=0,this.levelCompletionTime=0,this.currentScore=0,this.levelScore=0,this.enemiesDefeated=0,this.levelEnemiesDefeated=0,this.totalScore=0,this.perfectCompletion=!0,this.speedBonus=0,this.accuracyBonus=0,this.wavesCompleted=0,console.log("LevelManager reset")}setOnLevelStart(e){this.onLevelStartCallback=e}setOnLevelComplete(e){this.onLevelCompleteCallback=e}setOnScoreUpdate(e){this.onScoreUpdateCallback=e}}class y{static clamp(e,t,i){return Math.min(Math.max(e,t),i)}static lerp(e,t,i){return e+(t-e)*i}static pointInRect(e,t){return e.x>=t.x&&e.x<=t.x+t.width&&e.y>=t.y&&e.y<=t.y+t.height}static rectCollision(e,t){return e.x<t.x+t.width&&e.x+e.width>t.x&&e.y<t.y+t.height&&e.y+e.height>t.y}static circleCollision(e,t){const i=e.x-t.x,s=e.y-t.y;return Math.sqrt(i*i+s*s)<e.radius+t.radius}static random(e,t){return Math.random()*(t-e)+e}static randomInt(e,t){return Math.floor(Math.random()*(t-e+1))+e}static degToRad(e){return e*(Math.PI/180)}static radToDeg(e){return e*(180/Math.PI)}static circleRectCollision(e,t){const i=y.clamp(e.x,t.x,t.x+t.width),s=y.clamp(e.y,t.y,t.y+t.height),n=e.x-i,a=e.y-s;return Math.sqrt(n*n+a*a)<e.radius}static lineCircleCollision(e,t,i){const s=t.x-e.x,n=t.y-e.y,a=e.x-i.x,r=e.y-i.y,h=s*s+n*n,d=2*(a*s+r*n),m=a*a+r*r-i.radius*i.radius,p=d*d-4*h*m;if(p<0)return!1;const f=Math.sqrt(p),g=(-d-f)/(2*h),v=(-d+f)/(2*h);return g>=0&&g<=1||v>=0&&v<=1}static smoothStep(e,t,i){const s=y.clamp((i-e)/(t-e),0,1);return s*s*(3-2*s)}static easeInQuad(e){return e*e}static easeOutQuad(e){return e*(2-e)}static easeInOutQuad(e){return e<.5?2*e*e:-1+(4-2*e)*e}static wrapAngle(e){for(;e>Math.PI;)e-=2*Math.PI;for(;e<-Math.PI;)e+=2*Math.PI;return e}static angleDifference(e,t){let i=t-e;return y.wrapAngle(i)}static moveTowards(e,t,i){const s=t-e;return Math.abs(s)<=i?t:e+Math.sign(s)*i}static approximately(e,t,i=1e-4){return Math.abs(e-t)<i}static map(e,t,i,s,n){return(e-t)*(n-s)/(i-t)+s}}class x extends D{constructor(e,t,i=o.AIR,s=800,n=600){super(e,t),this.canvasWidth=s,this.canvasHeight=n,this.type=i,this.maxHealth=this.getTypeMaxHealth(i),this.health=this.maxHealth,this.baseSpeed=this.getTypeBaseSpeed(i),this.currentSpeed=this.baseSpeed,this.width=24,this.height=24,this.collisionRadius=12,this.movementPattern="linear",this.movementTimer=0,this.movementPhase=Math.random()*Math.PI*2,this.amplitude=50,this.frequency=2,this.predefinedPattern=null,this.predefinedState=0,this.predefinedTimer=0,this.initialPosition=new l(e,t),this.formationOffset=new l(0,0),this.formationTarget=new l(e,t),this.isInFormation=!1,this.attackCooldown=0,this.baseAttackRate=this.getTypeAttackRate(i),this.currentAttackRate=this.baseAttackRate,this.canAttack=!0,this.environmentalEffectiveness=1,this.currentEnvironment="space",this.animationTime=0,this.animationSpeed=4,this.spriteFrame=0,this.totalFrames=4,this.isDestroyed=!1,this.deathAnimationTime=0,this.deathAnimationDuration=500,this.scoreValue=this.getTypeScoreValue(i),this.addTag("enemy"),this.addTag(i),this.setMovementPattern("linear"),console.log(`Enemy created: type=${i}, health=${this.health}, position=${this.position.toString()}`)}getTypeMaxHealth(e){return{[o.AIR]:20,[o.WATER]:30,[o.FIRE]:25,[o.EARTH]:40,[o.CRYSTAL]:35,[o.SHADOW]:15}[e]||20}getTypeBaseSpeed(e){return{[o.AIR]:120,[o.WATER]:80,[o.FIRE]:100,[o.EARTH]:60,[o.CRYSTAL]:90,[o.SHADOW]:140}[e]||100}getTypeAttackRate(e){return{[o.AIR]:1.5,[o.WATER]:1,[o.FIRE]:2,[o.EARTH]:.8,[o.CRYSTAL]:1.2,[o.SHADOW]:2.5}[e]||1}getTypeScoreValue(e){return{[o.AIR]:100,[o.WATER]:150,[o.FIRE]:125,[o.EARTH]:200,[o.CRYSTAL]:175,[o.SHADOW]:300}[e]||100}update(e,t=null){if(this.active){if(this.movementTimer+=e/1e3,this.animationTime+=e/1e3,this.isDestroyed){this.updateDeathAnimation(e);return}this.updateMovement(e,t),this.updateAttackCooldown(e),this.updateAnimation(e),this.checkOffScreen(),this.updateCollisionBounds()}}updateMovement(e,t){const i=e/1e3;switch(this.movementPattern){case"linear":this.updateLinearMovement(i);break;case"sine":this.updateSineMovement(i);break;case"spiral":this.updateSpiralMovement(i);break;case"dive":this.updateDiveMovement(i,t);break;case"formation":this.updateFormationMovement(i);break;case"zigzag":this.updateZigzagMovement(i);break;case"circle":this.updateCircleMovement(i);break;case"hover":this.updateHoverMovement(i,t);break;case"predefined":this.updatePredefinedMovement(i,t);break}const s=this.currentSpeed*this.environmentalEffectiveness;this.velocity.multiplyInPlace(s/this.velocity.magnitude()),this.position.addInPlace(this.velocity.multiply(i))}updateLinearMovement(e){this.velocity.set(0,this.currentSpeed)}updateSineMovement(e){const t=Math.sin(this.movementTimer*this.frequency+this.movementPhase)*this.amplitude,i=this.formationTarget.x+t,s=Math.sign(i-this.position.x);this.velocity.set(s*this.currentSpeed*.5,this.currentSpeed*.8)}updateSpiralMovement(e){const t=this.amplitude*(1-this.movementTimer*.1),i=this.movementTimer*this.frequency+this.movementPhase,s=this.formationTarget.x+Math.cos(i)*t,n=this.formationTarget.y+this.movementTimer*this.currentSpeed*.3,a=new l(s-this.position.x,n-this.position.y).normalize();this.velocity=a.multiply(this.currentSpeed)}updateDiveMovement(e,t){if(!t){this.updateLinearMovement(e);return}if(this.movementTimer<1){const i=this.movementPhase>Math.PI?-1:1;this.velocity.set(i*this.currentSpeed,this.currentSpeed*.5)}else if(this.movementTimer<2){const i=t.subtract(this.position).normalize();this.velocity=i.multiply(this.currentSpeed*1.5)}else this.velocity.set(0,this.currentSpeed)}updateFormationMovement(e){const i=this.formationTarget.add(this.formationOffset).subtract(this.position);i.magnitude()>5?this.velocity=i.normalize().multiply(this.currentSpeed):this.velocity.set(0,this.currentSpeed*.2)}updateZigzagMovement(e){const i=Math.floor(this.movementTimer*this.frequency)%2===0?1:-1;this.velocity.set(i*this.currentSpeed*.7,this.currentSpeed*.8)}updateCircleMovement(e){const t=this.movementTimer*this.frequency+this.movementPhase,i=this.formationTarget.x,s=this.formationTarget.y+this.movementTimer*this.currentSpeed*.2,n=i+Math.cos(t)*this.amplitude,a=s+Math.sin(t)*this.amplitude*.5,r=new l(n-this.position.x,a-this.position.y).normalize();this.velocity=r.multiply(this.currentSpeed)}updateHoverMovement(e,t){if(!t){this.updateLinearMovement(e);return}const i=150,s=this.position.distance(t);if(s>i+20){const n=t.subtract(this.position).normalize();this.velocity=n.multiply(this.currentSpeed*.6)}else if(s<i-20){const n=this.position.subtract(t).normalize();this.velocity=n.multiply(this.currentSpeed*.6)}else{const n=t.subtract(this.position).perpendicular().normalize();this.velocity=n.multiply(this.currentSpeed*.8)}}updatePredefinedMovement(e,t){if(this.predefinedTimer+=e,!this.predefinedPattern){this.updateLinearMovement(e);return}switch(this.predefinedPattern){case"straight_down":this.updateStraightDownPattern(e);break;case"triangle_left_right":this.updateTriangleLeftRightPattern(e);break;case"triangle_zigzag":this.updateTriangleZigzagPattern(e);break;case"diamond_sine":this.updateDiamondSinePattern(e);break;case"coordinated_sweep_left":this.updateCoordinatedSweepPattern(e,-1);break;case"coordinated_sweep_right":this.updateCoordinatedSweepPattern(e,1);break;case"advanced_weave":this.updateAdvancedWeavePattern(e);break;case"dive_attack":this.updateDiveAttackPattern(e,t);break;case"spiral_descent":this.updateSpiralDescentPattern(e);break;case"pincer_left":this.updatePincerPattern(e,-1);break;case"pincer_right":this.updatePincerPattern(e,1);break;case"center_rush":this.updateCenterRushPattern(e,t);break;case"wedge_assault":this.updateWedgeAssaultPattern(e);break;case"side_sweep":this.updateSideSweepPattern(e);break;default:this.updateLinearMovement(e);break}}updateAttackCooldown(e){this.attackCooldown>0&&(this.attackCooldown-=e,this.canAttack=this.attackCooldown<=0)}updateAnimation(e){const t=1e3/(this.animationSpeed*this.totalFrames);this.animationTime*1e3%t<e&&(this.spriteFrame=(this.spriteFrame+1)%this.totalFrames)}updateStraightDownPattern(e){this.velocity.set(0,this.currentSpeed)}updateTriangleLeftRightPattern(e){switch(Math.floor(this.predefinedTimer/2)%3){case 0:this.velocity.set(-this.currentSpeed*.8,this.currentSpeed*.6);break;case 1:this.velocity.set(this.currentSpeed*1.2,this.currentSpeed*.6);break;case 2:this.velocity.set(-this.currentSpeed*.4,this.currentSpeed*.8);break}}updateTriangleZigzagPattern(e){const i=Math.sin(this.predefinedTimer*1.5)>0?1:-1;this.velocity.set(i*this.currentSpeed*.6,this.currentSpeed*.8)}updateDiamondSinePattern(e){const t=Math.sin(this.predefinedTimer*2)*60,i=this.initialPosition.x+t,s=Math.sign(i-this.position.x);this.velocity.set(s*this.currentSpeed*.4,this.currentSpeed*.9)}updateCoordinatedSweepPattern(e,t){const i=Math.sin(this.predefinedTimer*.8)*t;this.velocity.set(i*this.currentSpeed*.7,this.currentSpeed*.7)}updateAdvancedWeavePattern(e){const t=Math.sin(this.predefinedTimer*2.5)*this.currentSpeed*.8,i=Math.cos(this.predefinedTimer*1.2)*this.currentSpeed*.3;this.velocity.set(t,this.currentSpeed*.6+i)}updateDiveAttackPattern(e,t){if(!t){this.updateLinearMovement(e);return}if(this.predefinedTimer<1.5)this.velocity.set(0,this.currentSpeed*.5);else if(this.predefinedTimer<3){const i=t.subtract(this.position).normalize();this.velocity=i.multiply(this.currentSpeed*1.8)}else this.velocity.set(0,this.currentSpeed*1.2)}updateSpiralDescentPattern(e){const t=80-this.predefinedTimer*10,i=this.predefinedTimer*3,s=Math.cos(i)*Math.max(0,t),n=Math.sin(i)*Math.max(0,t)*.3,a=this.initialPosition.x+s,r=this.initialPosition.y+this.predefinedTimer*this.currentSpeed*.4+n,h=new l(a-this.position.x,r-this.position.y).normalize();this.velocity=h.multiply(this.currentSpeed)}updatePincerPattern(e,t){const i=Math.sin(this.predefinedTimer*1.5)*t;this.velocity.set(i*this.currentSpeed*.9,this.currentSpeed*.8)}updateCenterRushPattern(e,t){if(!t){this.velocity.set(0,this.currentSpeed);return}const i=new l(this.canvasWidth/2,this.canvasHeight/2),n=(this.predefinedTimer<2?i:t).subtract(this.position).normalize();this.velocity=n.multiply(this.currentSpeed*1.3)}updateWedgeAssaultPattern(e){const t=Math.sin(this.predefinedTimer*3)*20,i=1+this.predefinedTimer*.3;this.velocity.set(t,this.currentSpeed*i)}updateSideSweepPattern(e){const t=this.position.x<this.canvasWidth/2?1:-1;this.velocity.set(t*this.currentSpeed*1.1,this.currentSpeed*.5)}updateDeathAnimation(e){this.deathAnimationTime+=e,this.velocity.multiplyInPlace(.95),this.deathAnimationTime>=this.deathAnimationDuration&&this.destroy()}checkOffScreen(){(this.position.x<-50||this.position.x>this.canvasWidth+50||this.position.y>this.canvasHeight+50)&&this.destroy()}render(e,t=0){if(!this.visible)return;const i=this.position.add(this.velocity.multiply(t/1e3));e.save(),e.translate(i.x,i.y),e.rotate(this.rotation),this.isDestroyed?this.renderDeathAnimation(e):this.renderEnemySprite(e),window.DEBUG_MODE&&this.renderDebugInfo(e),e.restore()}renderEnemySprite(e){const t=.7+this.environmentalEffectiveness*.3;e.globalAlpha=t;const i=this.getTypeColors(),s=Math.sin(this.animationTime*this.animationSpeed*Math.PI*2),n=1+s*.1;switch(e.scale(n,n),this.type){case o.AIR:this.drawAirEnemy(e,i,s);break;case o.WATER:this.drawWaterEnemy(e,i,s);break;case o.FIRE:this.drawFireEnemy(e,i,s);break;case o.EARTH:this.drawEarthEnemy(e,i,s);break;case o.CRYSTAL:this.drawCrystalEnemy(e,i,s);break;case o.SHADOW:this.drawShadowEnemy(e,i,s);break;default:this.drawDefaultEnemy(e,i,s);break}e.globalAlpha=1}getTypeColors(){const e={[o.AIR]:{primary:"#87CEEB",secondary:"#4682B4",accent:"#B0E0E6"},[o.WATER]:{primary:"#4169E1",secondary:"#1E90FF",accent:"#00BFFF"},[o.FIRE]:{primary:"#FF4500",secondary:"#FF6347",accent:"#FFD700"},[o.EARTH]:{primary:"#8B4513",secondary:"#A0522D",accent:"#DEB887"},[o.CRYSTAL]:{primary:"#9370DB",secondary:"#8A2BE2",accent:"#DDA0DD"},[o.SHADOW]:{primary:"#2F2F2F",secondary:"#4A4A4A",accent:"#696969"}};return e[this.type]||e[o.AIR]}drawAirEnemy(e,t,i){const s=Math.sin(this.animationTime*8)*.3;e.fillStyle=t.primary,e.beginPath(),e.ellipse(0,0,this.width*.3,this.height*.4,0,0,Math.PI*2),e.fill(),e.fillStyle=t.secondary,e.beginPath(),e.ellipse(-this.width*.4,s*5,this.width*.3,this.height*.2,-.3,0,Math.PI*2),e.ellipse(this.width*.4,s*5,this.width*.3,this.height*.2,.3,0,Math.PI*2),e.fill(),e.fillStyle=t.accent,e.beginPath(),e.arc(-4,-4,2,0,Math.PI*2),e.arc(4,-4,2,0,Math.PI*2),e.fill()}drawWaterEnemy(e,t,i){const s=1+i*.2;e.fillStyle=t.primary,e.beginPath(),e.ellipse(0,-this.height*.2,this.width*.4*s,this.height*.3*s,0,0,Math.PI*2),e.fill(),e.strokeStyle=t.secondary,e.lineWidth=2;for(let n=0;n<4;n++){const a=n/4*Math.PI*2,r=Math.sin(this.animationTime*6+n)*3;e.beginPath(),e.moveTo(Math.cos(a)*6,this.height*.1),e.quadraticCurveTo(Math.cos(a)*8+r,this.height*.3,Math.cos(a)*4+r*2,this.height*.4),e.stroke()}e.fillStyle=t.accent,e.beginPath(),e.ellipse(0,-this.height*.2,this.width*.2,this.height*.15,0,0,Math.PI*2),e.fill()}drawFireEnemy(e,t,i){const s=Math.sin(this.animationTime*12)*.2,n=Math.sin(this.animationTime*15+1)*.15;e.fillStyle=t.accent,e.beginPath(),e.ellipse(0,this.height*.1,this.width*.3,this.height*.4,0,0,Math.PI*2),e.fill(),e.fillStyle=t.primary,e.beginPath(),e.moveTo(0,-this.height*.4+s*5),e.quadraticCurveTo(-this.width*.3+n*3,0,-this.width*.2,this.height*.3),e.quadraticCurveTo(0,this.height*.4,this.width*.2,this.height*.3),e.quadraticCurveTo(this.width*.3+s*3,0,0,-this.height*.4+s*5),e.fill(),e.fillStyle=t.secondary,e.beginPath(),e.ellipse(0,0,this.width*.25+n*2,this.height*.3+s*3,0,0,Math.PI*2),e.fill()}drawEarthEnemy(e,t,i){e.fillStyle=t.primary,e.strokeStyle=t.secondary,e.lineWidth=2,e.beginPath(),e.moveTo(0,-this.height*.4),e.lineTo(this.width*.3,-this.height*.2),e.lineTo(this.width*.4,this.height*.1),e.lineTo(this.width*.2,this.height*.4),e.lineTo(-this.width*.2,this.height*.4),e.lineTo(-this.width*.4,this.height*.1),e.lineTo(-this.width*.3,-this.height*.2),e.closePath(),e.fill(),e.stroke(),e.fillStyle=t.accent,e.beginPath(),e.arc(-this.width*.1,-this.height*.1,3,0,Math.PI*2),e.arc(this.width*.15,this.height*.1,2,0,Math.PI*2),e.arc(-this.width*.2,this.height*.2,2,0,Math.PI*2),e.fill()}drawCrystalEnemy(e,t,i){const s=.7+i*.3,n=e.createRadialGradient(0,0,0,0,0,this.width*.6);n.addColorStop(0,t.accent+"80"),n.addColorStop(1,"transparent"),e.fillStyle=n,e.beginPath(),e.arc(0,0,this.width*.6*s,0,Math.PI*2),e.fill(),e.fillStyle=t.primary,e.strokeStyle=t.secondary,e.lineWidth=1,e.beginPath(),e.moveTo(0,-this.height*.4),e.lineTo(this.width*.2,-this.height*.2),e.lineTo(this.width*.3,0),e.lineTo(this.width*.2,this.height*.3),e.lineTo(0,this.height*.4),e.lineTo(-this.width*.2,this.height*.3),e.lineTo(-this.width*.3,0),e.lineTo(-this.width*.2,-this.height*.2),e.closePath(),e.fill(),e.stroke(),e.strokeStyle=t.accent,e.beginPath(),e.moveTo(0,-this.height*.4),e.lineTo(0,this.height*.4),e.moveTo(-this.width*.3,0),e.lineTo(this.width*.3,0),e.stroke()}drawShadowEnemy(e,t,i){const s=Math.sin(this.animationTime*4)*.3;e.globalAlpha=.8+i*.2,e.fillStyle=t.primary,e.beginPath(),e.ellipse(0,0,this.width*.4+s*5,this.height*.4+s*3,0,0,Math.PI*2),e.fill(),e.strokeStyle=t.secondary,e.lineWidth=3,e.lineCap="round";for(let n=0;n<6;n++){const a=n/6*Math.PI*2,r=Math.sin(this.animationTime*5+n)*8,h=Math.cos(a)*this.width*.3,d=Math.sin(a)*this.height*.3;e.beginPath(),e.moveTo(h,d),e.lineTo(h+Math.cos(a)*(10+r),d+Math.sin(a)*(10+r)),e.stroke()}e.fillStyle=t.accent,e.beginPath(),e.arc(-6,-6,2,0,Math.PI*2),e.arc(6,-6,2,0,Math.PI*2),e.fill()}drawDefaultEnemy(e,t,i){e.fillStyle=t.primary,e.strokeStyle=t.secondary,e.lineWidth=2,e.beginPath(),e.arc(0,0,this.width*.4,0,Math.PI*2),e.fill(),e.stroke(),e.fillStyle=t.accent,e.beginPath(),e.arc(-4,-4,2,0,Math.PI*2),e.arc(4,-4,2,0,Math.PI*2),e.fill()}renderDeathAnimation(e){const t=this.deathAnimationTime/this.deathAnimationDuration,i=1-t,s=1+t*2;e.globalAlpha=i,e.scale(s,s);const n=this.getTypeColors(),a=8;for(let r=0;r<a;r++){const h=r/a*Math.PI*2,d=t*30,m=Math.cos(h)*d,p=Math.sin(h)*d;e.fillStyle=n.accent,e.beginPath(),e.arc(m,p,3*(1-t),0,Math.PI*2),e.fill()}e.globalAlpha=1}renderDebugInfo(e){if(e.strokeStyle="#FF0000",e.lineWidth=1,e.beginPath(),e.arc(0,0,this.collisionRadius,0,Math.PI*2),e.stroke(),this.velocity.magnitude()>1){e.strokeStyle="#00FF00",e.lineWidth=2,e.beginPath(),e.moveTo(0,0);const n=.1;e.lineTo(this.velocity.x*n,this.velocity.y*n),e.stroke()}const t=this.width,i=4,s=this.health/this.maxHealth;e.fillStyle="#FF0000",e.fillRect(-t/2,-this.height/2-10,t,i),e.fillStyle="#00FF00",e.fillRect(-t/2,-this.height/2-10,t*s,i),e.fillStyle="#FFFFFF",e.font="8px Arial",e.textAlign="center",e.fillText(`${this.type} (${this.environmentalEffectiveness.toFixed(1)}x)`,0,this.height/2+15)}setMovementPattern(e,t={}){switch(this.movementPattern=e,this.movementTimer=0,e){case"sine":this.amplitude=t.amplitude||50,this.frequency=t.frequency||2;break;case"spiral":this.amplitude=t.amplitude||80,this.frequency=t.frequency||3;break;case"formation":this.isInFormation=!0,this.formationOffset=t.offset||new l(0,0);break;case"zigzag":this.frequency=t.frequency||1.5;break;case"circle":this.amplitude=t.radius||60,this.frequency=t.frequency||2;break;case"predefined":this.predefinedPattern=t.predefinedPattern||"straight_down",this.predefinedState=0,this.predefinedTimer=0,this.initialPosition=this.position.clone();break}console.log(`Enemy ${this.id} movement pattern set to: ${e}`)}setFormationTarget(e,t=new l(0,0)){this.formationTarget=e.clone(),this.formationOffset=t.clone(),this.isInFormation=!0}applyEnvironmentalEffect(e,t=1){this.currentEnvironment=e,this.environmentalEffectiveness=y.clamp(t,.1,2),this.currentSpeed=this.baseSpeed*this.environmentalEffectiveness,this.currentAttackRate=this.baseAttackRate*this.environmentalEffectiveness,this.environmentalEffectiveness!==t&&console.log(`Enemy ${this.id} environmental effectiveness changed: ${this.environmentalEffectiveness} -> ${t} in ${e}`)}takeDamage(e){if(this.isDestroyed)return{damageTaken:0,health:this.health,destroyed:!0,scoreValue:0};const t=Math.min(e,this.health);return this.health-=t,console.log(`Enemy ${this.id} took ${t} damage. Health: ${this.health}/${this.maxHealth}`),this.health<=0?(this.startDeathAnimation(),{damageTaken:t,health:0,destroyed:!0,scoreValue:this.scoreValue}):{damageTaken:t,health:this.health,destroyed:!1,scoreValue:0}}startDeathAnimation(){this.isDestroyed=!0,this.deathAnimationTime=0,this.canAttack=!1,console.log(`Enemy ${this.id} destroyed! Score value: ${this.scoreValue}`)}canAttackPlayer(e){if(!this.canAttack||this.isDestroyed||!e)return!1;const t=this.getAttackRange();return this.position.distance(e)<=t}getAttackRange(){return{[o.AIR]:200,[o.WATER]:150,[o.FIRE]:180,[o.EARTH]:120,[o.CRYSTAL]:250,[o.SHADOW]:300}[this.type]||200}attack(e){if(!this.canAttackPlayer(e))return null;this.attackCooldown=1e3/this.currentAttackRate+y.random(-200,200),this.canAttack=!1;const t=e.subtract(this.position).normalize();return{position:this.position.clone(),direction:t,type:this.type,damage:this.getAttackDamage()}}getAttackDamage(){return{[o.AIR]:15,[o.WATER]:20,[o.FIRE]:25,[o.EARTH]:30,[o.CRYSTAL]:22,[o.SHADOW]:18}[this.type]||20}getStatus(){return{id:this.id,type:this.type,position:this.position.clone(),health:this.health,maxHealth:this.maxHealth,movementPattern:this.movementPattern,environmentalEffectiveness:this.environmentalEffectiveness,currentEnvironment:this.currentEnvironment,canAttack:this.canAttack,isDestroyed:this.isDestroyed,scoreValue:this.scoreValue}}}class H{constructor(e=T.CANVAS_WIDTH,t=T.CANVAS_HEIGHT,i=null){this.canvasWidth=e,this.canvasHeight=t,this.gameObjectManager=i,this.activeEnemies=[],this.enemyPool=[],this.maxEnemies=50,this.currentWave=0,this.waveInProgress=!1,this.waveStartTime=0,this.waveConfig=null,this.enemiesSpawnedInWave=0,this.enemiesKilledInWave=0,this.enemiesEscapedInWave=0,this.patternSpawnCounts={},this.lastSpawnTime=0,this.spawnCooldown=1e3,this.spawnTimer=0,this.formations=[],this.formationSpawnQueue=[],this.currentEnvironment="space",this.environmentalEffects=this.getDefaultEnvironmentalEffects(),this.totalEnemiesSpawned=0,this.totalEnemiesKilled=0,this.totalScore=0,this.collisionGrid=null,this.gridSize=64,this.gridWidth=Math.ceil(e/this.gridSize),this.gridHeight=Math.ceil(t/this.gridSize),console.log("EnemyManager initialized")}update(e,t=null){this.spawnTimer+=e,this.updateWaveManagement(e),this.updateEnemySpawning(e,t),this.updateActiveEnemies(e,t),this.cleanupDestroyedEnemies(),this.updateFormations(e),this.checkWaveCompletion()}updateWaveManagement(e){!this.waveInProgress&&this.activeEnemies.length===0&&this.startNextWave(),this.waveInProgress&&(this.waveStartTime+=e)}updateEnemySpawning(e,t){!this.waveInProgress||!this.waveConfig||(this.spawnTimer>=this.spawnCooldown&&this.activeEnemies.length<this.maxEnemies&&this.enemiesSpawnedInWave<this.waveConfig.totalEnemies&&(this.spawnEnemyFromWave(),this.spawnTimer=0),this.processFormationSpawnQueue(e))}updateActiveEnemies(e,t){for(let i=this.activeEnemies.length-1;i>=0;i--){const s=this.activeEnemies[i];s.active&&(s.update(e,t),this.applyEnvironmentalEffects(s))}}cleanupDestroyedEnemies(){var e,t;for(let i=this.activeEnemies.length-1;i>=0;i--){const s=this.activeEnemies[i];(s.destroyed||!s.active)&&(s.isDestroyed?(this.enemiesKilledInWave++,this.totalEnemiesKilled++,this.totalScore+=s.scoreValue,console.log(`Enemy ${s.id} killed. Wave progress: ${this.enemiesKilledInWave+this.enemiesEscapedInWave}/${((e=this.waveConfig)==null?void 0:e.totalEnemies)||0}`)):s.destroyed&&(this.enemiesEscapedInWave++,console.log(`Enemy ${s.id} escaped. Wave progress: ${this.enemiesKilledInWave+this.enemiesEscapedInWave}/${((t=this.waveConfig)==null?void 0:t.totalEnemies)||0}`),this.onEnemyEscaped(s)),this.returnEnemyToPool(s),this.activeEnemies.splice(i,1))}}startNextWave(){if(this.currentWave++,this.waveInProgress=!0,this.waveStartTime=0,this.enemiesSpawnedInWave=0,this.enemiesKilledInWave=0,this.enemiesEscapedInWave=0,this.patternSpawnCounts={},this.waveConfig=this.generateWaveConfig(this.currentWave),this.waveConfig.spawnPatterns)for(let e=0;e<this.waveConfig.spawnPatterns.length;e++)this.patternSpawnCounts[e]=0;this.spawnCooldown=Math.max(200,1500-this.currentWave*50),console.log(`Starting wave ${this.currentWave}:`,this.waveConfig)}generateWaveConfig(e){const s=5+Math.floor(e/2),n=this.getWaveEnemyTypes(e),a=this.generateSpawnPatterns(e,s);return{waveNumber:e,totalEnemies:s,enemyTypes:n,spawnPatterns:a,difficulty:Math.min(10,Math.floor(e/3)+1),hasFormation:e%4===0,hasBoss:e%10===0}}getWaveEnemyTypes(e){const t=[];return e<=3?(t.push({type:o.AIR,weight:.8}),t.push({type:o.WATER,weight:.2})):e<=7?(t.push({type:o.AIR,weight:.4}),t.push({type:o.WATER,weight:.3}),t.push({type:o.FIRE,weight:.2}),t.push({type:o.EARTH,weight:.1})):(t.push({type:o.AIR,weight:.2}),t.push({type:o.WATER,weight:.2}),t.push({type:o.FIRE,weight:.2}),t.push({type:o.EARTH,weight:.15}),t.push({type:o.CRYSTAL,weight:.15}),t.push({type:o.SHADOW,weight:.1})),t}generateSpawnPatterns(e,t){return this.getPredefinedWavePattern(e,t)}getPredefinedWavePattern(e,t){const i=[];switch(e){case 1:i.push({type:"formation",count:t,formation:"line",movementPattern:"predefined",predefinedPattern:"straight_down",spacing:1500});break;case 2:i.push({type:"formation",count:t,formation:"v-formation",movementPattern:"predefined",predefinedPattern:"triangle_left_right",spacing:1200});break;case 3:i.push({type:"formation",count:Math.floor(t*.7),formation:"triangle",movementPattern:"predefined",predefinedPattern:"triangle_zigzag"}),i.push({type:"scattered",count:Math.ceil(t*.3),movementPattern:"predefined",predefinedPattern:"side_sweep"});break;case 4:i.push({type:"formation",count:t,formation:"diamond",movementPattern:"predefined",predefinedPattern:"diamond_sine"});break;case 5:i.push({type:"formation",count:Math.floor(t*.5),formation:"line",movementPattern:"predefined",predefinedPattern:"coordinated_sweep_left"}),i.push({type:"formation",count:Math.ceil(t*.5),formation:"line",movementPattern:"predefined",predefinedPattern:"coordinated_sweep_right"});break;default:switch((e-6)%4){case 0:i.push({type:"formation",count:Math.floor(t*.6),formation:"v-formation",movementPattern:"predefined",predefinedPattern:"advanced_weave"}),i.push({type:"dive",count:Math.ceil(t*.4),movementPattern:"predefined",predefinedPattern:"dive_attack"});break;case 1:i.push({type:"formation",count:t,formation:"circle",movementPattern:"predefined",predefinedPattern:"spiral_descent"});break;case 2:i.push({type:"formation",count:Math.floor(t*.4),formation:"line",movementPattern:"predefined",predefinedPattern:"pincer_left"}),i.push({type:"formation",count:Math.floor(t*.4),formation:"line",movementPattern:"predefined",predefinedPattern:"pincer_right"}),i.push({type:"scattered",count:Math.ceil(t*.2),movementPattern:"predefined",predefinedPattern:"center_rush"});break;case 3:i.push({type:"formation",count:t,formation:"wedge",movementPattern:"predefined",predefinedPattern:"wedge_assault"});break}break}return i}spawnEnemyFromWave(){if(!this.waveConfig||this.enemiesSpawnedInWave>=this.waveConfig.totalEnemies)return;const e=this.selectEnemyType(this.waveConfig.enemyTypes),i=this.selectSpawnPattern(this.waveConfig.spawnPatterns).pattern,s=this.generateSpawnPosition(i),n=this.spawnEnemy(s.x,s.y,e);if(n){const a={amplitude:y.random(30,80),frequency:y.random(1,3)};i.predefinedPattern&&(a.predefinedPattern=i.predefinedPattern),n.setMovementPattern(i.movementPattern,a),i.type==="formation"&&this.addEnemyToFormation(n,i),this.enemiesSpawnedInWave++}}selectEnemyType(e){const t=Math.random();let i=0;for(const s of e)if(i+=s.weight,t<=i)return s.type;return e[0].type}selectSpawnPattern(e){const t=[];for(let s=0;s<e.length;s++){const n=e[s];(this.patternSpawnCounts[s]||0)<n.count&&t.push({pattern:n,index:s})}if(t.length===0)return console.warn("All spawn patterns exhausted but still trying to spawn enemies"),{pattern:e[0],index:0};const i=t[Math.floor(Math.random()*t.length)];return this.patternSpawnCounts[i.index]++,i}generateSpawnPosition(e){switch(e.type){case"linear":return new l(y.random(50,this.canvasWidth-50),-30);case"formation":return new l(this.canvasWidth/2,-50);case"scattered":return new l(y.random(30,this.canvasWidth-30),y.random(-50,-20));case"dive":const t=Math.random()<.5?"left":"right";return new l(t==="left"?-30:this.canvasWidth+30,y.random(50,150));default:return new l(y.random(50,this.canvasWidth-50),-30)}}spawnEnemy(e,t,i=o.AIR){let s=this.getEnemyFromPool();return s?(s.reset(),s.position.set(e,t),s.type=i,s.maxHealth=s.getTypeMaxHealth(i),s.health=s.maxHealth,s.baseSpeed=s.getTypeBaseSpeed(i),s.currentSpeed=s.baseSpeed,s.scoreValue=s.getTypeScoreValue(i)):s=new x(e,t,i,this.canvasWidth,this.canvasHeight),this.applyEnvironmentalEffects(s),this.activeEnemies.push(s),this.totalEnemiesSpawned++,this.gameObjectManager&&this.gameObjectManager.add(s),console.log(`Spawned ${i} enemy at (${e}, ${t}). Active enemies: ${this.activeEnemies.length}`),s}getEnemyFromPool(){return this.enemyPool.length>0?this.enemyPool.pop():null}returnEnemyToPool(e){this.enemyPool.length<this.maxEnemies&&(e.reset(),this.enemyPool.push(e)),this.gameObjectManager&&this.gameObjectManager.remove(e)}addEnemyToFormation(e,t){const i=this.getOrCreateFormation(t.formation),s=i.enemies.length,n=this.calculateFormationOffset(t.formation,s);e.setFormationTarget(i.center,n),i.enemies.push(e),console.log(`Added enemy to ${t.formation} formation. Formation size: ${i.enemies.length}`)}getOrCreateFormation(e){let t=this.formations.find(i=>i.type===e&&i.enemies.length<8);return t||(t={type:e,center:new l(this.canvasWidth/2,100),enemies:[],movementTimer:0},this.formations.push(t)),t}calculateFormationOffset(e,t){switch(e){case"line":return new l((t-2)*40,0);case"v-formation":const i=t%2===0?-1:1,s=Math.floor(t/2);return new l(i*(s+1)*30,s*25);case"triangle":const n=Math.floor((-1+Math.sqrt(1+8*t))/2),a=t-n*(n+1)/2,r=n+1;return new l((a-r/2+.5)*35,n*30);case"diamond":const h=3;if(t<h)return new l((t-1)*40,-30);if(t<h*2-1){const g=t-h;return new l((g-1)*60,0)}else{const g=t-(h*2-1);return new l((g-1)*40,30)}case"circle":const d=t/8*Math.PI*2,m=60;return new l(Math.cos(d)*m,Math.sin(d)*m);case"wedge":const p=Math.floor(t/3),f=t%3;return new l((f-1)*(40+p*10),p*25);default:return new l((t-2)*40,0)}}updateFormations(e){for(let t=this.formations.length-1;t>=0;t--){const i=this.formations[t];if(i.movementTimer+=e/1e3,i.enemies=i.enemies.filter(s=>s.active&&!s.destroyed),i.enemies.length===0){this.formations.splice(t,1);continue}i.center.y+=20*(e/1e3),i.center.x+=Math.sin(i.movementTimer*.5)*10*(e/1e3),i.center.x=y.clamp(i.center.x,100,this.canvasWidth-100)}}processFormationSpawnQueue(e){}checkWaveCompletion(){if(this.waveInProgress&&this.waveConfig){const e=this.enemiesKilledInWave+this.enemiesEscapedInWave,t=this.enemiesSpawnedInWave>=this.waveConfig.totalEnemies,i=e>=this.waveConfig.totalEnemies,s=this.activeEnemies.length===0;t&&(i||s)&&this.completeWave()}}completeWave(){this.waveInProgress=!1,console.log(`Wave ${this.currentWave} completed! Enemies killed: ${this.enemiesKilledInWave}/${this.waveConfig.totalEnemies}, Enemies escaped: ${this.enemiesEscapedInWave}/${this.waveConfig.totalEnemies}`);const e=this.calculateWaveBonus();this.totalScore+=e,this.formations=[],this.onWaveComplete(this.currentWave,e)}calculateWaveBonus(){const t=this.currentWave,i=this.enemiesKilledInWave/this.waveConfig.totalEnemies;return Math.floor(100*t*i)}onWaveComplete(e,t){console.log(`Wave ${e} complete with bonus: ${t}`)}onEnemyEscaped(e){console.log(`Enemy ${e.id} escaped`)}render(e,t=0){for(const i of this.activeEnemies)i.visible&&i.render(e,t);window.DEBUG_MODE&&this.renderDebugInfo(e)}renderDebugInfo(e){var i;e.fillStyle="#FFFFFF",e.font="12px Arial",e.textAlign="left";const t=[`Wave: ${this.currentWave}`,`Active Enemies: ${this.activeEnemies.length}`,`Spawned: ${this.enemiesSpawnedInWave}/${((i=this.waveConfig)==null?void 0:i.totalEnemies)||0}`,`Killed: ${this.enemiesKilledInWave}`,`Total Score: ${this.totalScore}`,`Formations: ${this.formations.length}`];for(let s=0;s<t.length;s++)e.fillText(t[s],10,20+s*15);e.strokeStyle="#FFFF00",e.lineWidth=2;for(const s of this.formations)e.beginPath(),e.arc(s.center.x,s.center.y,5,0,Math.PI*2),e.stroke()}checkPlayerCollisions(e){const t=[];if(!e.active||e.isInvulnerable)return t;for(const i of this.activeEnemies)i.active&&!i.isDestroyed&&i.collidesWith(e)&&t.push(i);return t}checkProjectileCollisions(e){const t=[];for(const i of e)if(!(!i.active||i.hasTag("enemyProjectile"))){for(const s of this.activeEnemies)if(s.active&&!s.isDestroyed&&s.collidesWith(i)){t.push({projectile:i,enemy:s,damage:i.damage||25});break}}return t}handlePlayerEnemyCollision(e,t){const i=e.takeDamage(t.getAttackDamage());return t.takeDamage(t.health),console.log(`Player-Enemy collision: Player took ${i.damageTaken} damage, Enemy destroyed`),{playerDamage:i.damageTaken,enemyDestroyed:!0,scoreGained:t.scoreValue}}handleProjectileEnemyCollision(e,t,i){const s=t.takeDamage(i);return e.destroy(),console.log(`Projectile-Enemy collision: Enemy took ${s.damageTaken} damage`),{enemyDestroyed:s.destroyed,scoreGained:s.scoreValue,damageDealt:s.damageTaken}}applyEnvironmentalEffects(e){const t=this.calculateEnvironmentalEffectiveness(e.type,this.currentEnvironment);e.applyEnvironmentalEffect(this.currentEnvironment,t)}calculateEnvironmentalEffectiveness(e,t){const i=this.environmentalEffects[t];return i&&i[e]||1}setEnvironment(e,t=null){this.currentEnvironment=e,t&&(this.environmentalEffects[e]=t);for(const i of this.activeEnemies)this.applyEnvironmentalEffects(i);console.log(`Environment changed to: ${e}`)}getDefaultEnvironmentalEffects(){return{space:{[o.AIR]:1.2,[o.WATER]:.8,[o.FIRE]:1,[o.EARTH]:.9,[o.CRYSTAL]:1.1,[o.SHADOW]:1},underwater:{[o.AIR]:.6,[o.WATER]:1.5,[o.FIRE]:.3,[o.EARTH]:.8,[o.CRYSTAL]:1,[o.SHADOW]:.9},volcanic:{[o.AIR]:.8,[o.WATER]:.4,[o.FIRE]:1.6,[o.EARTH]:1.3,[o.CRYSTAL]:.9,[o.SHADOW]:.7},crystal:{[o.AIR]:1,[o.WATER]:.9,[o.FIRE]:.8,[o.EARTH]:1.1,[o.CRYSTAL]:1.8,[o.SHADOW]:1.2},forest:{[o.AIR]:.7,[o.WATER]:1.1,[o.FIRE]:.6,[o.EARTH]:1.4,[o.CRYSTAL]:.8,[o.SHADOW]:1.3}}}updateEnemyAttacks(e,t){if(t)for(const i of this.activeEnemies)i.active&&!i.isDestroyed&&i.canAttackPlayer(t)&&i.attack(t)}getEnemiesInAttackRange(e){return this.activeEnemies.filter(t=>t.canAttackPlayer(e))}checkEnemyProjectileCollisions(e){return[]}handleEnemyProjectileCollision(e,t){return{damage:0,destroyed:!1}}triggerEnemyAttacks(e){const t=[],i=this.getEnemiesInAttackRange(e);for(const s of i){const n=s.attack(e);n&&t.push(n)}return t}getWaveStatus(){var e;return{currentWave:this.currentWave,waveInProgress:this.waveInProgress,enemiesSpawned:this.enemiesSpawnedInWave,enemiesKilled:this.enemiesKilledInWave,totalEnemies:((e=this.waveConfig)==null?void 0:e.totalEnemies)||0,activeEnemies:this.activeEnemies.length,totalScore:this.totalScore,waveProgress:this.waveConfig?this.enemiesSpawnedInWave/this.waveConfig.totalEnemies:0}}reset(){for(const e of this.activeEnemies)e.destroy();this.activeEnemies=[],this.currentWave=0,this.waveInProgress=!1,this.waveConfig=null,this.enemiesSpawnedInWave=0,this.enemiesKilledInWave=0,this.enemiesEscapedInWave=0,this.patternSpawnCounts={},this.spawnTimer=0,this.waveStartTime=0,this.formations=[],this.formationSpawnQueue=[],this.totalEnemiesSpawned=0,this.totalEnemiesKilled=0,this.totalScore=0,this.currentEnvironment="space",typeof GameObject<"u"&&GameObject.idCounter&&(GameObject.idCounter=0),console.log("EnemyManager reset")}updateCanvasDimensions(e,t){this.canvasWidth=e,this.canvasHeight=t,this.gridWidth=Math.ceil(e/this.gridSize),this.gridHeight=Math.ceil(t/this.gridSize);for(const i of this.activeEnemies)i.canvasWidth=e,i.canvasHeight=t}getStatistics(){return{currentWave:this.currentWave,totalEnemiesSpawned:this.totalEnemiesSpawned,totalEnemiesKilled:this.totalEnemiesKilled,totalScore:this.totalScore,activeEnemies:this.activeEnemies.length,formations:this.formations.length,killRatio:this.totalEnemiesSpawned>0?this.totalEnemiesKilled/this.totalEnemiesSpawned:0}}}class U{constructor(){this.playerBalance=0,this.totalEarned=0,this.totalSpent=0,this.transactionHistory=[],this.maxHistorySize=100,this.performanceMetrics={levelsCompleted:0,totalScore:0,averageCompletionTime:0,perfectCompletions:0,speedBonuses:0,accuracyBonuses:0},this.rewardMultipliers={base:1,speed:1.5,accuracy:1.3,perfect:2,difficulty:1},this.pendingRewards=[],this.rewardAnimations=[],this.onBalanceUpdateCallback=null,this.onTransactionCallback=null,this.onRewardEarnedCallback=null,console.log("TokenEconomyManager initialized")}calculateLevelReward(e){if(!e.completed)return{totalReward:0,breakdown:{reason:"level_not_completed"}};const t=e.levelNumber,i=e.completionTime,s=e.score,n=this.calculateBaseReward(t),a=this.calculateTimeBonus(i,t),r=this.calculateScoreBonus(s.totalScore,t),h=e.bonuses.speed?this.calculateSpeedBonus(n):0,d=e.bonuses.accuracy?this.calculateAccuracyBonus(n):0,m=e.bonuses.perfect?this.calculatePerfectBonus(n):0,p=this.calculateDifficultyMultiplier(t),f=n+a+r+h+d+m,g=Math.floor(f*p),v={baseReward:n,timeBonus:a,scoreBonus:r,speedBonus:h,accuracyBonus:d,perfectBonus:m,difficultyMultiplier:p,subtotal:f,totalReward:g};return console.log(`Level ${t} token reward calculated:`,v),{totalReward:g,breakdown:v}}calculateBaseReward(e){const t=T.BASE_LEVEL_REWARD,i=Math.floor((e-1)/5)+1;return t*i}calculateTimeBonus(e,t){const i=60+t*10,s=i*.5;return e<=s?Math.floor(30+t*5):e<=i*.75?Math.floor(20+t*3):e<=i?Math.floor(10+t):0}calculateScoreBonus(e,t){const s=Math.min(2,1+t*.05);return Math.floor(e*.01*s)}calculateSpeedBonus(e){return Math.floor(e*(this.rewardMultipliers.speed-1))}calculateAccuracyBonus(e){return Math.floor(e*(this.rewardMultipliers.accuracy-1))}calculatePerfectBonus(e){return Math.floor(e*(this.rewardMultipliers.perfect-1))}calculateDifficultyMultiplier(e){const n=1+Math.floor((e-1)/10)*.1;return Math.min(3,n)}awardTokens(e,t,i={}){if(e<=0)return console.warn("Attempted to award non-positive token amount:",e),{success:!1,reason:"invalid_amount"};this.playerBalance+=e,this.totalEarned+=e;const s={id:this.generateTransactionId(),type:"earned",amount:e,reason:t,timestamp:Date.now(),balanceAfter:this.playerBalance,metadata:i};return this.addTransaction(s),this.updatePerformanceMetrics(t,i),this.addPendingReward(e,t),console.log(`Awarded ${e} WISH tokens for ${t}. New balance: ${this.playerBalance}`),this.triggerBalanceUpdate(),this.triggerRewardEarned(e,t,i),{success:!0,transaction:s,newBalance:this.playerBalance}}spendTokens(e,t,i={}){if(e<=0)return console.warn("Attempted to spend non-positive token amount:",e),{success:!1,reason:"invalid_amount"};if(this.playerBalance<e)return console.warn(`Insufficient tokens. Required: ${e}, Available: ${this.playerBalance}`),{success:!1,reason:"insufficient_balance",required:e,available:this.playerBalance};this.playerBalance-=e,this.totalSpent+=e;const s={id:this.generateTransactionId(),type:"spent",amount:e,reason:t,timestamp:Date.now(),balanceAfter:this.playerBalance,metadata:i};return this.addTransaction(s),console.log(`Spent ${e} WISH tokens for ${t}. New balance: ${this.playerBalance}`),this.triggerBalanceUpdate(),this.triggerTransaction(s),{success:!0,transaction:s,newBalance:this.playerBalance}}canAfford(e){return this.playerBalance>=e}getBalance(){return this.playerBalance}getStatistics(){const e=this.totalEarned-this.totalSpent,t=this.performanceMetrics.levelsCompleted>0?this.totalEarned/this.performanceMetrics.levelsCompleted:0;return{currentBalance:this.playerBalance,totalEarned:this.totalEarned,totalSpent:this.totalSpent,netProfit:e,transactionCount:this.transactionHistory.length,averageEarningPerLevel:Math.floor(t),performanceMetrics:{...this.performanceMetrics}}}getRecentTransactions(e=10){return this.transactionHistory.slice(-e).reverse()}addTransaction(e){this.transactionHistory.push(e),this.transactionHistory.length>this.maxHistorySize&&this.transactionHistory.shift()}generateTransactionId(){return`tx_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}updatePerformanceMetrics(e,t){if(e==="level_completion"){if(this.performanceMetrics.levelsCompleted++,t.score&&(this.performanceMetrics.totalScore+=t.score),t.completionTime){const i=this.performanceMetrics.averageCompletionTime,s=this.performanceMetrics.levelsCompleted;this.performanceMetrics.averageCompletionTime=(i*(s-1)+t.completionTime)/s}t.bonuses&&(t.bonuses.perfect&&this.performanceMetrics.perfectCompletions++,t.bonuses.speed&&this.performanceMetrics.speedBonuses++,t.bonuses.accuracy&&this.performanceMetrics.accuracyBonuses++)}}addPendingReward(e,t){const i={id:this.generateTransactionId(),amount:e,reason:t,timestamp:Date.now(),displayed:!1};this.pendingRewards.push(i)}getPendingRewards(){return this.pendingRewards.filter(e=>!e.displayed)}markRewardDisplayed(e){const t=this.pendingRewards.find(s=>s.id===e);t&&(t.displayed=!0);const i=Date.now()-1e4;this.pendingRewards=this.pendingRewards.filter(s=>!s.displayed||s.timestamp>i)}updateRewardAnimations(e){for(let i=this.rewardAnimations.length-1;i>=0;i--){const s=this.rewardAnimations[i];s.elapsed+=e,s.elapsed>=s.duration&&this.rewardAnimations.splice(i,1)}const t=this.getPendingRewards();for(const i of t)this.rewardAnimations.push({id:i.id,amount:i.amount,reason:i.reason,startTime:Date.now(),elapsed:0,duration:3e3,startY:100,endY:50,alpha:1}),this.markRewardDisplayed(i.id)}render(e,t){this.updateRewardAnimations(t),this.renderTokenBalance(e),this.renderRewardAnimations(e)}renderTokenBalance(e){const t=e.canvas.width-20,i=80;e.fillStyle="rgba(0, 0, 0, 0.7)",e.fillRect(t-150,i-25,140,30),e.strokeStyle="#FFD700",e.lineWidth=2,e.strokeRect(t-150,i-25,140,30),e.fillStyle="#FFD700",e.font="16px Arial",e.textAlign="left",e.fillText("★",t-145,i-5),e.fillStyle="#FFFFFF",e.font="14px Arial",e.textAlign="right",e.fillText(`${this.playerBalance.toLocaleString()} WISH`,t-10,i-5)}renderRewardAnimations(e){for(const t of this.rewardAnimations){const i=t.elapsed/t.duration,s=t.startY+(t.endY-t.startY)*i,n=Math.max(0,1-i);e.save(),e.globalAlpha=n,e.fillStyle="#00FF00",e.font="bold 18px Arial",e.textAlign="center",e.fillText(`+${t.amount} WISH`,e.canvas.width/2,s),e.fillStyle="#FFFFFF",e.font="12px Arial",e.fillText(this.formatRewardReason(t.reason),e.canvas.width/2,s+20),e.restore()}}formatRewardReason(e){return{level_completion:"Level Complete",speed_bonus:"Speed Bonus",accuracy_bonus:"Accuracy Bonus",perfect_bonus:"Perfect Run",enemy_defeat:"Enemy Defeated",wave_completion:"Wave Complete"}[e]||e.replace(/_/g," ").toUpperCase()}reset(){this.playerBalance=0,this.totalEarned=0,this.totalSpent=0,this.transactionHistory=[],this.performanceMetrics={levelsCompleted:0,totalScore:0,averageCompletionTime:0,perfectCompletions:0,speedBonuses:0,accuracyBonuses:0},this.pendingRewards=[],this.rewardAnimations=[],console.log("TokenEconomyManager reset")}triggerBalanceUpdate(){this.onBalanceUpdateCallback&&this.onBalanceUpdateCallback(this.playerBalance,this.getStatistics())}triggerTransaction(e){this.onTransactionCallback&&this.onTransactionCallback(e)}triggerRewardEarned(e,t,i){this.onRewardEarnedCallback&&this.onRewardEarnedCallback(e,t,i)}setOnBalanceUpdate(e){this.onBalanceUpdateCallback=e}setOnTransaction(e){this.onTransactionCallback=e}setOnRewardEarned(e){this.onRewardEarnedCallback=e}}const M=class M{constructor(e,t,i=null,s=""){this.type=e,this.cost=t,this.duration=i,this.description=s,this.isActive=!1,this.timeRemaining=i,this.appliedAt=null,this.id=M.generateId(),this.icon=null,this.color="#00ffff",this.glowColor="#ffffff"}static generateId(){return`powerup_${++M.idCounter}`}apply(e){return this.isActive?(console.warn(`PowerUp ${this.type} is already active`),!1):(this.isActive=!0,this.appliedAt=Date.now(),this.timeRemaining=this.duration,console.log(`Applied power-up: ${this.type}`),this.applyEffect(e))}remove(e){return this.isActive?(this.isActive=!1,this.timeRemaining=0,console.log(`Removed power-up: ${this.type}`),this.removeEffect(e)):(console.warn(`PowerUp ${this.type} is not active`),!1)}update(e,t){return this.isActive?this.duration!==null&&this.timeRemaining>0&&(this.timeRemaining-=e,this.timeRemaining<=0)?(this.remove(t),!1):!0:!1}getTimeRemainingPercentage(){return this.duration===null?1:this.isActive?Math.max(0,this.timeRemaining/this.duration):0}getFormattedTimeRemaining(){return this.duration===null?"Permanent":this.isActive?`${Math.ceil(this.timeRemaining/1e3)}s`:"Inactive"}canPurchase(e,t){return{canPurchase:t>=this.cost&&!this.isActive,reason:t<this.cost?"insufficient_tokens":this.isActive?"already_active":"available"}}applyEffect(e){return!0}removeEffect(e){return!0}getDisplayInfo(){return{id:this.id,type:this.type,cost:this.cost,duration:this.duration,description:this.description,isActive:this.isActive,timeRemaining:this.timeRemaining,timeRemainingPercentage:this.getTimeRemainingPercentage(),formattedTimeRemaining:this.getFormattedTimeRemaining(),icon:this.icon,color:this.color,glowColor:this.glowColor}}};R(M,"idCounter",0);let k=M;class K extends k{constructor(){super("EXTRA_LIFE",T.POWER_UP_COSTS.EXTRA_LIFE,null,"Gain an extra life to continue your journey"),this.icon="❤️",this.color="#ff4444",this.glowColor="#ff8888"}applyEffect(e){return e.addLives(1),!0}canPurchase(e,t){return{canPurchase:t>=this.cost,reason:t<this.cost?"insufficient_tokens":"available"}}}class q extends k{constructor(){super("SPREAD_AMMO",T.POWER_UP_COSTS.SPREAD_AMMO,3e4,"Fire projectiles in a spread pattern for better coverage"),this.icon="🔥",this.color="#ffaa00",this.glowColor="#ffdd44"}applyEffect(e){return e.weaponSystem?(e.weaponSystem.enableSpreadPattern(!0),!0):!1}removeEffect(e){return e.weaponSystem?(e.weaponSystem.enableSpreadPattern(!1),!0):!1}}class V extends k{constructor(){super("EXTRA_WINGMAN",T.POWER_UP_COSTS.EXTRA_WINGMAN,45e3,"Deploy a wingman ship to provide covering fire"),this.icon="🚀",this.color="#00ff88",this.glowColor="#44ffaa",this.wingmanShip=null}applyEffect(e){return console.log("Wingman power-up applied (implementation pending)"),!0}removeEffect(e){return this.wingmanShip&&(this.wingmanShip.destroy(),this.wingmanShip=null),console.log("Wingman power-up removed (implementation pending)"),!0}}class Y{static createPowerUp(e){switch(e){case"EXTRA_LIFE":return new K;case"SPREAD_AMMO":return new q;case"EXTRA_WINGMAN":return new V;default:throw new Error(`Unknown power-up type: ${e}`)}}static getAllPowerUpTypes(){return["EXTRA_LIFE","SPREAD_AMMO","EXTRA_WINGMAN"]}static createAllPowerUps(){return this.getAllPowerUpTypes().map(e=>this.createPowerUp(e))}}class N{constructor(e,t){this.tokenManager=e,this.gameEngine=t,this.isVisible=!1,this.isInitialized=!1,this.container=null,this.availablePowerUps=Y.createAllPowerUps(),this.activePowerUps=new Map,this.onPowerUpPurchased=null,this.onWarpPurchased=null,this.onClose=null,this.animationFrame=null,this.glowAnimation=0,console.log("GenieInterface created")}async initialize(){if(!this.isInitialized)try{this.container=document.createElement("div"),this.container.id="genie-interface",this.container.className="genie-interface hidden",document.body.appendChild(this.container),this.setupEventListeners(),this.isInitialized=!0,console.log("GenieInterface initialized successfully")}catch(e){throw console.error("GenieInterface initialization error:",e),e}}show(){if(!this.isInitialized){console.error("GenieInterface not initialized");return}this.isVisible=!0,this.updatePowerUpAvailability(),this.render(),this.container.classList.remove("hidden"),this.container.classList.add("visible"),this.startGlowAnimation(),console.log("GenieInterface shown")}hide(){this.isVisible&&(this.isVisible=!1,this.container.classList.remove("visible"),this.container.classList.add("hidden"),this.stopGlowAnimation(),console.log("GenieInterface hidden"))}updatePowerUpAvailability(){const e=this.tokenManager.getBalance(),t=this.gameEngine.playerShip;this.availablePowerUps.forEach(i=>{const s=i.canPurchase(t,e);i.availability=s})}render(){if(!this.container)return;const e=this.tokenManager.getBalance();this.container.innerHTML=`
            <div class="genie-modal">
                <div class="genie-backdrop" id="genie-backdrop"></div>
                <div class="genie-content">
                    <div class="genie-header">
                        <div class="genie-character">
                            <div class="genie-lamp">🪔</div>
                            <div class="genie-smoke"></div>
                        </div>
                        <h1 class="genie-title">The Mystical Genie</h1>
                        <p class="genie-subtitle">Your wishes are my command, traveler...</p>
                        <div class="token-display">
                            <span class="token-icon">✨</span>
                            <span class="token-amount">${e}</span>
                            <span class="token-label">WISH Tokens</span>
                        </div>
                    </div>
                    
                    <div class="genie-body">
                        <div class="power-ups-section">
                            <h2 class="section-title">Power-Up Enchantments</h2>
                            <div class="power-ups-grid">
                                ${this.renderPowerUps()}
                            </div>
                        </div>
                        
                        <div class="reality-warp-section">
                            <h2 class="section-title">Reality Warp Magic</h2>
                            <div class="warp-options">
                                <div class="warp-card coming-soon">
                                    <div class="warp-icon">🌌</div>
                                    <h3>Reality Warp</h3>
                                    <p>Transform the battlefield to your advantage</p>
                                    <div class="warp-cost">Coming Soon</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="genie-footer">
                        <button id="genie-close-btn" class="genie-button secondary">
                            Continue Journey
                        </button>
                    </div>
                </div>
            </div>
        `,this.setupButtonEventListeners()}renderPowerUps(){return this.availablePowerUps.map(e=>{const t=e.availability||{canPurchase:!1,reason:"unknown"},i=this.activePowerUps.has(e.type),s=t.canPurchase&&!i;let n="",a="",r=`Purchase (${e.cost} ✨)`;return i?(n="active",a="Active",r="Already Active"):t.canPurchase||(n="unavailable",t.reason==="insufficient_tokens"?(a="Insufficient Tokens",r=`Need ${e.cost-this.tokenManager.getBalance()} more ✨`):(a="Unavailable",r="Cannot Purchase")),`
                <div class="power-up-card ${n}" data-power-up="${e.type}">
                    <div class="power-up-icon">${e.icon}</div>
                    <h3 class="power-up-name">${this.formatPowerUpName(e.type)}</h3>
                    <p class="power-up-description">${e.description}</p>
                    <div class="power-up-details">
                        <div class="power-up-cost">
                            <span class="cost-amount">${e.cost}</span>
                            <span class="cost-icon">✨</span>
                        </div>
                        <div class="power-up-duration">
                            ${e.duration?`${Math.ceil(e.duration/1e3)}s`:"Permanent"}
                        </div>
                    </div>
                    <div class="power-up-status">${a}</div>
                    <button class="power-up-button ${s?"primary":"disabled"}" 
                            data-power-up="${e.type}"
                            ${s?"":"disabled"}>
                        ${r}
                    </button>
                </div>
            `}).join("")}formatPowerUpName(e){switch(e){case"EXTRA_LIFE":return"Extra Life";case"SPREAD_AMMO":return"Spread Ammo";case"EXTRA_WINGMAN":return"Extra Wingman";default:return e.replace(/_/g," ").toLowerCase().replace(/\b\w/g,t=>t.toUpperCase())}}setupEventListeners(){document.addEventListener("keydown",e=>{e.key==="Escape"&&this.isVisible&&this.close()})}setupButtonEventListeners(){const e=this.container.querySelector("#genie-close-btn");e&&e.addEventListener("click",()=>this.close());const t=this.container.querySelector("#genie-backdrop");t&&t.addEventListener("click",()=>this.close()),this.container.querySelectorAll(".power-up-button:not(.disabled)").forEach(s=>{s.addEventListener("click",n=>{const a=n.target.dataset.powerUp;this.handlePowerUpPurchase(a)})})}handlePowerUpPurchase(e){const t=this.availablePowerUps.find(h=>h.type===e);if(!t){console.error("Power-up not found:",e);return}const i=this.tokenManager.getBalance(),s=this.gameEngine.playerShip,n=t.canPurchase(s,i);if(!n.canPurchase){console.warn("Cannot purchase power-up:",n.reason);return}const a=this.tokenManager.spendTokens(t.cost,`power_up_${e.toLowerCase()}`);if(!a.success){console.error("Failed to spend tokens:",a.reason);return}if(!t.apply(s)){console.error("Failed to apply power-up"),this.tokenManager.awardTokens(t.cost,"power_up_refund");return}this.activePowerUps.set(e,t),this.updatePowerUpAvailability(),this.render(),this.onPowerUpPurchased&&this.onPowerUpPurchased(t,a),console.log(`Power-up purchased: ${e} for ${t.cost} tokens`)}close(){this.hide(),this.onClose&&this.onClose()}startGlowAnimation(){if(this.animationFrame)return;const e=()=>{this.glowAnimation+=.05;const t=this.container.querySelector(".genie-lamp"),i=this.container.querySelector(".token-icon");if(t){const s=Math.sin(this.glowAnimation)*.5+.5;t.style.filter=`drop-shadow(0 0 ${10+s*10}px #ffd700)`}if(i){const s=Math.sin(this.glowAnimation+1)*.5+.5;i.style.filter=`drop-shadow(0 0 ${5+s*5}px #00ffff)`}this.isVisible&&(this.animationFrame=requestAnimationFrame(e))};e()}stopGlowAnimation(){this.animationFrame&&(cancelAnimationFrame(this.animationFrame),this.animationFrame=null)}updateActivePowerUps(e){const t=this.gameEngine.playerShip;for(const[i,s]of this.activePowerUps)s.update(e,t)||(this.activePowerUps.delete(i),console.log(`Power-up expired: ${i}`))}setOnPowerUpPurchased(e){this.onPowerUpPurchased=e}setOnWarpPurchased(e){this.onWarpPurchased=e}setOnClose(e){this.onClose=e}destroy(){this.stopGlowAnimation(),this.container&&this.container.parentNode&&this.container.parentNode.removeChild(this.container),this.container=null,this.isInitialized=!1,console.log("GenieInterface destroyed")}}var S={GG_GET_GAME_DATA:"GG_GET_GAME_DATA",GG_UPDATE_GAME_DATA:"GG_UPDATE_GAME_DATA",GG_SET_GAME_DATA:"GG_SET_GAME_DATA",GG_PAUSED_FROM_GAME:"GG_PAUSED_FROM_GAME",GG_PAUSED_FROM_PARENT:"GG_PAUSED_FROM_PARENT",GG_QUIT_FROM_PARENT:"GG_QUIT_FROM_PARENT",GG_GAME_OVER:"GG_GAME_OVER",GG_RESUMED_FROM_GAME:"GG_RESUMED_FROM_GAME",GG_RESUMED_FROM_PARENT:"GG_RESUMED_FROM_PARENT",GG_GAME_LOAD_FINISHED:"GG_GAME_LOAD_FINISHED"},X=function(){function c(){this.registeredListeners=[]}return c.prototype.getTargetWindow=function(){try{if(window.top&&window.top!==window)return window.top}catch(e){console.warn("window.top access failed:",e.message)}try{if(window.parent&&window.parent!==window)return window.parent}catch(e){console.warn("window.parent access failed:",e.message)}return null},c.getInstance=function(){return c.instance||(c.instance=new c),c.instance},c.prototype.checkRegisteredListenersAndAdd=function(e,t){this.registeredListeners.includes(e)||(window.addEventListener("message",t),this.registeredListeners.push(e))},c.prototype.registerListener=function(e,t){this.getTargetWindow()?this.checkRegisteredListenersAndAdd(e,function(i){i.data.event_type===e&&t()}):console.error("Functions should be called from inside an iframe")},c.prototype.getGameData=function(e,t){var i=this.getTargetWindow();if(i){var s=setTimeout(function(){t(e)},3e3);this.checkRegisteredListenersAndAdd(S.GG_SET_GAME_DATA,function(n){n.data.event_type===S.GG_SET_GAME_DATA&&(clearTimeout(s),t(n.data.payload.gameData))}),i.postMessage({event_type:S.GG_GET_GAME_DATA,payload:{defaultData:e}},"*")}else console.error("Functions should be called from inside an iframe")},c.prototype.saveGameData=function(e){var t=this.getTargetWindow();t?t.postMessage({event_type:S.GG_UPDATE_GAME_DATA,payload:{data:e}},"*"):console.error("Functions should be called from inside an iframe")},c.prototype.gameOver=function(e){var t=this.getTargetWindow();t?(console.log("sending game over to Goama",e),t.postMessage({event_type:S.GG_GAME_OVER,payload:{score:e}},"*")):console.error("Functions should be called from inside an iframe")},c.prototype.gamePaused=function(){var e=this.getTargetWindow();e?e.postMessage({event_type:S.GG_PAUSED_FROM_GAME},"*"):console.error("Functions should be called from inside an iframe")},c.prototype.gameResumed=function(){var e=this.getTargetWindow();e?e.postMessage({event_type:S.GG_RESUMED_FROM_GAME},"*"):console.error("Functions should be called from inside an iframe")},c.prototype.gameLoaded=function(){var e=this.getTargetWindow();e?e.postMessage({event_type:S.GG_GAME_LOAD_FINISHED},"*"):console.error("Functions should be called from inside an iframe")},c.prototype.listenPaused=function(e){this.registerListener(S.GG_PAUSED_FROM_PARENT,e)},c.prototype.listenResumed=function(e){this.registerListener(S.GG_RESUMED_FROM_PARENT,e)},c.prototype.listenQuit=function(e){this.registerListener(S.GG_QUIT_FROM_PARENT,e)},c}(),b=X.getInstance();typeof window<"u"&&(window.GGSDK=b);var J=b.getGameData,Q=b.saveGameData,F=b.gameOver,Z=b.gamePaused,ee=b.gameResumed,te=b.gameLoaded,ie=b.listenPaused,se=b.listenResumed,ne=b.listenQuit;class ae{constructor(){this.isSDKReady=!1,this.isInitialized=!1,this.playerData={totalScore:0,highestLevel:0,wishTokensEarned:0,wishTokensSpent:0,wishTokensBalance:0,levelsCompleted:[],levelBestScores:{},totalPlayTime:0,perfectCompletions:0,totalEnemiesDefeated:0,averageCompletionTime:0,specialStatuses:{loginStreak:0,lastLoginDate:null,bonusLivesEarned:0,tournamentParticipant:!1,achievementUnlocks:[]},currentSession:{startTime:Date.now(),levelsPlayedThisSession:0,tokensEarnedThisSession:0,scoreThisSession:0},lastSaveTime:Date.now(),gameVersion:"1.0.0",totalSessions:0},this.saveInProgress=!1,this.pendingSaveData=null,this.saveRetryCount=0,this.maxRetries=3,this.retryDelay=1e3,this.onDataSavedCallback=null,this.onSaveErrorCallback=null,this.onSpecialStatusCallback=null,this.autoSaveEnabled=!0,this.autoSaveInterval=3e4,this.lastAutoSave=Date.now(),console.log("OrangeSDKManager initialized")}async initialize(){if(!this.isInitialized)try{this.playerData.currentSession.startTime=Date.now(),this.playerData.totalSessions++,this.setupSDKEventListeners(),await this.checkSpecialStatuses(),te(),this.isSDKReady=!0,this.isInitialized=!0,console.log("OrangeSDKManager initialized successfully"),await this.savePlayerData("game_start")}catch(e){console.error("Failed to initialize OrangeSDKManager:",e),this.isSDKReady=!1}}setupSDKEventListeners(){ie(()=>{console.log("Game paused by parent window"),this.handleGamePause()}),se(()=>{console.log("Game resumed by parent window"),this.handleGameResume()}),ne(()=>{console.log("Game quit requested by parent window"),this.handleGameQuit()})}async checkSpecialStatuses(){try{const e={specialStatuses:this.playerData.specialStatuses};J(e,t=>{if(t&&t.specialStatuses){const i=t.specialStatuses,s=new Date().toDateString(),n=i.lastLoginDate;if(n){const a=new Date(n),r=Math.floor((Date.now()-a.getTime())/(1e3*60*60*24));r===1?this.playerData.specialStatuses.loginStreak=(i.loginStreak||0)+1:r>1?this.playerData.specialStatuses.loginStreak=1:this.playerData.specialStatuses.loginStreak=i.loginStreak||1}else this.playerData.specialStatuses.loginStreak=1;this.playerData.specialStatuses.lastLoginDate=s,this.playerData.specialStatuses.bonusLivesEarned=i.bonusLivesEarned||0,this.playerData.specialStatuses.tournamentParticipant=i.tournamentParticipant||!1,this.playerData.specialStatuses.achievementUnlocks=i.achievementUnlocks||[],this.processSpecialStatusBonuses()}})}catch(e){console.error("Error checking special statuses:",e)}}processSpecialStatusBonuses(){const e=this.playerData.specialStatuses.loginStreak;if(e>=2&&e<=7){const t=Math.min(e-1,3);this.playerData.specialStatuses.bonusLivesEarned=t,console.log(`Login streak bonus: ${t} extra lives for ${e} day streak`),this.onSpecialStatusCallback&&this.onSpecialStatusCallback("bonus_lives",t)}this.playerData.specialStatuses.tournamentParticipant&&(console.log("Tournament participant status active"),this.onSpecialStatusCallback&&this.onSpecialStatusCallback("tournament_participant",!0))}updatePlayerProgress(e){if(!e)return;if(e.score!==void 0&&(this.playerData.totalScore=Math.max(this.playerData.totalScore,e.score),this.playerData.currentSession.scoreThisSession+=e.scoreGained||0),e.level!==void 0&&(this.playerData.highestLevel=Math.max(this.playerData.highestLevel,e.level)),e.tokensEarned!==void 0&&(this.playerData.wishTokensEarned+=e.tokensEarned,this.playerData.wishTokensBalance+=e.tokensEarned,this.playerData.currentSession.tokensEarnedThisSession+=e.tokensEarned),e.tokensSpent!==void 0&&(this.playerData.wishTokensSpent+=e.tokensSpent,this.playerData.wishTokensBalance-=e.tokensSpent),e.levelCompleted!==void 0){const s=e.levelCompleted;if(this.playerData.levelsCompleted.includes(s)||(this.playerData.levelsCompleted.push(s),this.playerData.currentSession.levelsPlayedThisSession++),e.levelScore!==void 0){const n=this.playerData.levelBestScores[s]||0;this.playerData.levelBestScores[s]=Math.max(n,e.levelScore)}}if(e.perfectCompletion&&this.playerData.perfectCompletions++,e.enemiesDefeated!==void 0&&(this.playerData.totalEnemiesDefeated+=e.enemiesDefeated),e.completionTime!==void 0){const s=this.playerData.levelsCompleted.length;s>0&&(this.playerData.averageCompletionTime=(this.playerData.averageCompletionTime*(s-1)+e.completionTime)/s)}const t=Date.now(),i=t-this.playerData.currentSession.startTime;this.playerData.totalPlayTime+=i,this.playerData.currentSession.startTime=t,console.log("Player progress updated:",e)}async savePlayerData(e="manual",t=!1){if(!this.isSDKReady)return console.warn("SDK not ready, cannot save data"),!1;if(this.saveInProgress&&!t)return console.log("Save already in progress, queuing data"),this.pendingSaveData={...this.playerData},!1;this.saveInProgress=!0,this.playerData.lastSaveTime=Date.now();const i={...this.playerData,saveReason:e,saveTimestamp:Date.now()};try{if(await this.performSaveWithRetry(i),console.log(`Player data saved successfully (${e})`),this.onDataSavedCallback&&this.onDataSavedCallback(i,e),this.pendingSaveData){const s=this.pendingSaveData;this.pendingSaveData=null,setTimeout(()=>this.savePlayerData("pending_update"),100)}return!0}catch(s){return console.error("Failed to save player data:",s),this.onSaveErrorCallback&&this.onSaveErrorCallback(s,e),!1}finally{this.saveInProgress=!1,this.saveRetryCount=0}}async performSaveWithRetry(e){for(let t=0;t<=this.maxRetries;t++)try{await new Promise((i,s)=>{try{Q(e),setTimeout(i,100)}catch(n){s(n)}});return}catch(i){if(this.saveRetryCount=t+1,t<this.maxRetries)console.warn(`Save attempt ${t+1} failed, retrying in ${this.retryDelay}ms:`,i),await new Promise(s=>setTimeout(s,this.retryDelay)),this.retryDelay*=2;else throw i}}async onLevelCompleted(e){var i,s,n,a;if(!e)return;this.updatePlayerProgress({level:e.levelNumber,levelCompleted:e.levelNumber,levelScore:((i=e.score)==null?void 0:i.totalScore)||0,scoreGained:((s=e.score)==null?void 0:s.totalScore)||0,tokensEarned:((n=e.score)==null?void 0:n.tokenReward)||0,perfectCompletion:e.perfectCompletion,enemiesDefeated:e.enemiesDefeated,completionTime:e.completionTime});const t=((a=e.score)==null?void 0:a.totalScore)||0;F(t),await this.savePlayerData("level_completed"),console.log(`Level ${e.levelNumber} completion saved to Orange SDK`)}async onTokensChanged(e){if(!e)return;this.updatePlayerProgress({tokensEarned:e.earned||0,tokensSpent:e.spent||0}),(e.earned||0)+(e.spent||0)>=50&&await this.savePlayerData("token_change")}handleGamePause(){Z(),this.savePlayerData("game_paused")}handleGameResume(){ee(),this.playerData.currentSession.startTime=Date.now()}async handleGameQuit(){const e=Date.now()-this.playerData.currentSession.startTime;this.playerData.totalPlayTime+=e,F(this.playerData.totalScore),await this.savePlayerData("game_quit"),console.log("Game quit - final data saved to Orange SDK")}updateAutoSave(){if(!this.autoSaveEnabled||!this.isSDKReady)return;const e=Date.now();e-this.lastAutoSave>=this.autoSaveInterval&&(this.savePlayerData("auto_save"),this.lastAutoSave=e)}getPlayerData(){return{...this.playerData}}getSpecialStatuses(){return{...this.playerData.specialStatuses}}unlockAchievement(e){this.playerData.specialStatuses.achievementUnlocks.includes(e)||(this.playerData.specialStatuses.achievementUnlocks.push(e),this.savePlayerData("achievement_unlock"),console.log(`Achievement unlocked: ${e}`),this.onSpecialStatusCallback&&this.onSpecialStatusCallback("achievement_unlock",e))}setTournamentParticipant(e){this.playerData.specialStatuses.tournamentParticipant=e,this.savePlayerData("tournament_status"),this.onSpecialStatusCallback&&this.onSpecialStatusCallback("tournament_participant",e)}setOnDataSavedCallback(e){this.onDataSavedCallback=e}setOnSaveErrorCallback(e){this.onSaveErrorCallback=e}setOnSpecialStatusCallback(e){this.onSpecialStatusCallback=e}setAutoSave(e,t=3e4){this.autoSaveEnabled=e,this.autoSaveInterval=t,e&&(this.lastAutoSave=Date.now()),console.log(`Auto-save ${e?"enabled":"disabled"} with ${t}ms interval`)}getSaveStatistics(){return{isSDKReady:this.isSDKReady,saveInProgress:this.saveInProgress,saveRetryCount:this.saveRetryCount,lastSaveTime:this.playerData.lastSaveTime,autoSaveEnabled:this.autoSaveEnabled,autoSaveInterval:this.autoSaveInterval,lastAutoSave:this.lastAutoSave}}reset(){this.playerData={totalScore:0,highestLevel:0,wishTokensEarned:0,wishTokensSpent:0,wishTokensBalance:0,levelsCompleted:[],levelBestScores:{},totalPlayTime:0,perfectCompletions:0,totalEnemiesDefeated:0,averageCompletionTime:0,specialStatuses:{loginStreak:0,lastLoginDate:null,bonusLivesEarned:0,tournamentParticipant:!1,achievementUnlocks:[]},currentSession:{startTime:Date.now(),levelsPlayedThisSession:0,tokensEarnedThisSession:0,scoreThisSession:0},lastSaveTime:Date.now(),gameVersion:"1.0.0",totalSessions:0},this.saveInProgress=!1,this.pendingSaveData=null,this.saveRetryCount=0,this.retryDelay=1e3,console.log("OrangeSDKManager reset")}}class oe{constructor(e,t){this.canvas=e,this.ctx=e.getContext("2d"),this.uiElement=t,this.stars=[],this.isRunning=!1,this.isPaused=!1,this.targetFPS=60,this.fixedTimeStep=1e3/this.targetFPS,this.maxFrameTime=250,this.lastFrameTime=0,this.accumulator=0,this.currentTime=0,this.frameCount=0,this.fpsTimer=0,this.currentFPS=0,this.gameLoop=this.gameLoop.bind(this),this.handleResize=this.handleResize.bind(this)}async init(){return console.log("Initializing WarpSpace Game Engine..."),this.setupCanvas(),await this.initializeSystems(),this.start(),Promise.resolve()}setupCanvas(){this.ctx.imageSmoothingEnabled=!1,this.handleResize(),window.addEventListener("resize",this.handleResize),console.log(`Canvas initialized: ${this.canvas.width}x${this.canvas.height}`)}initializeStarfield(){for(let t=0;t<200;t++)this.stars.push({x:Math.random()*this.canvas.width,y:Math.random()*this.canvas.height,size:Math.random()*1.5+.5,speed:Math.random()*25+5})}async initializeSystems(){this.inputManager=new _(this.canvas),this.gameObjectManager=new z;const e=this.canvas.width/2,t=this.canvas.height-100;this.playerShip=new $(e,t,this.canvas.width,this.canvas.height,this.gameObjectManager),this.levelManager=new j(this.gameObjectManager),this.enemyManager=new H(this.canvas.width,this.canvas.height,this.gameObjectManager),this.tokenManager=new U,this.orangeSDKManager=new ae,this.genieInterface=new N(this.tokenManager,this),this.setupLevelManagerCallbacks(),this.setupEnemyManagerCallbacks(),this.setupOrangeSDKCallbacks(),this.setupTokenManagerCallbacks(),await this.genieInterface.initialize(),this.setupGenieInterfaceCallbacks(),this.initializeOrangeSDK(),this.initializeStarfield(),console.log("Game systems initialized")}start(){this.isRunning||(this.isRunning=!0,this.isPaused=!1,this.currentTime=performance.now(),this.lastFrameTime=this.currentTime,this.accumulator=0,this.frameCount=0,this.fpsTimer=0,requestAnimationFrame(this.gameLoop),console.log("Game loop started with fixed timestep"))}startGame(e=null){console.log("Starting gameplay for user:",e),this.levelManager&&this.levelManager.startLevel(1),console.log("Game started - Level 1 initialized")}pause(){this.isPaused=!0,console.log("Game paused"),this.orangeSDKManager&&this.orangeSDKManager.handleGamePause()}resume(){this.isPaused&&(this.isPaused=!1,this.currentTime=performance.now(),this.lastFrameTime=this.currentTime,this.accumulator=0,console.log("Game resumed"),this.orangeSDKManager&&this.orangeSDKManager.handleGameResume())}async destroy(){this.isRunning=!1,this.orangeSDKManager&&await this.orangeSDKManager.handleGameQuit(),this.inputManager&&this.inputManager.destroy(),console.log("Game engine destroyed")}gameLoop(e){if(!this.isRunning)return;let t=e-this.lastFrameTime;if(t>this.maxFrameTime&&(t=this.maxFrameTime),this.lastFrameTime=e,!this.isPaused){for(this.accumulator+=t;this.accumulator>=this.fixedTimeStep;)this.update(this.fixedTimeStep),this.accumulator-=this.fixedTimeStep;const i=this.accumulator/this.fixedTimeStep;this.render(i),this.updateFPSCounter(t)}requestAnimationFrame(this.gameLoop)}update(e){if(this.updateStarfield(e),this.inputManager&&this.inputManager.update(),this.playerShip&&this.inputManager){const t=this.inputManager.getMovementVector();this.playerShip.update(e,t),this.inputManager.isActionDown("fire")&&this.playerShip.fire(),this.inputManager.isKeyPressed("KeyD")&&this.playerShip.takeDamage(25),this.inputManager.isKeyPressed("KeyH")&&this.playerShip.heal(25),this.inputManager.isKeyPressed("KeyL")&&this.playerShip.addLives(1),this.inputManager.isKeyPressed("KeyT")&&this.tokenManager.awardTokens(500,"debug_tokens"),this.inputManager.isKeyPressed("KeyG")&&this.showGenieInterface({levelNumber:1,nextLevel:2})}if(this.genieInterface&&this.genieInterface.updateActivePowerUps(e),this.levelManager){const t={playerDestroyed:this.playerShip?this.playerShip.getHealthStatus().isDestroyed:!1,playerDamageTaken:!1,shotsFired:0,shotsHit:0};this.levelManager.update(e,t)}if(this.enemyManager&&this.playerShip&&this.levelManager&&this.levelManager.levelInProgress){const t=this.playerShip.position;this.enemyManager.update(e,t),this.handleCollisions()}this.gameObjectManager&&this.gameObjectManager.update(e),this.cleanupProjectiles(),this.orangeSDKManager&&this.orangeSDKManager.updateAutoSave()}cleanupProjectiles(){if(!this.gameObjectManager)return;const e={left:-50,right:this.canvas.width+50,top:-50,bottom:this.canvas.height+50},t=this.gameObjectManager.findByTag("projectile");for(const i of t)i.isOutOfBounds(e)&&this.gameObjectManager.returnToPool("projectile",i)}updateStarfield(e){const t=e/1e3;for(const i of this.stars)i.y+=i.speed*t,i.y>this.canvas.height&&(i.y=0,i.x=Math.random()*this.canvas.width)}render(e=0){this.ctx.fillStyle="#000011",this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height),this.renderStarField(),this.playerShip&&this.playerShip.render(this.ctx,e),this.enemyManager&&this.enemyManager.render(this.ctx,e),this.gameObjectManager&&this.gameObjectManager.render(this.ctx,e),this.ctx.fillStyle="#ffffff",this.ctx.font="20px Arial",this.ctx.textAlign="center",this.ctx.fillText("WarpSpace",this.canvas.width/2,30),this.inputManager&&(this.renderInputDebug(),this.inputManager.render(this.ctx)),this.ctx.font="12px Arial",this.ctx.textAlign="left",this.ctx.fillText(`FPS: ${this.currentFPS}`,10,20),this.renderHealthAndLivesUI(),this.renderLevelAndScoreUI(),this.tokenManager&&this.tokenManager.render(this.ctx,this.fixedTimeStep)}renderInputDebug(){if(!this.inputManager)return;const e=this.inputManager.getMovementVector();this.ctx.font="12px Arial",this.ctx.textAlign="left",this.ctx.fillStyle="#ffffff",this.ctx.fillText(`Movement: ${e.x.toFixed(2)}, ${e.y.toFixed(2)}`,10,40);const t=["fire","pause","interact"];let i=60;for(const s of t)this.inputManager.isActionDown(s)?(this.ctx.fillStyle="#00ff00",this.ctx.fillText(`${s.toUpperCase()}: ACTIVE`,10,i)):(this.ctx.fillStyle="#666666",this.ctx.fillText(`${s}: inactive`,10,i)),i+=15;if(this.ctx.fillStyle="#ffffff",this.ctx.fillText(`Mouse: ${this.inputManager.mousePosition.x.toFixed(0)}, ${this.inputManager.mousePosition.y.toFixed(0)}`,10,i+10),this.inputManager.isTouchDevice&&this.ctx.fillText(`Touch Device: ${this.inputManager.touches.size} touches`,10,i+25),this.gameObjectManager){const s=this.gameObjectManager.getStats();this.ctx.fillText(`Objects: ${s.totalObjects} (${s.activeObjects} active)`,10,i+40);const n=this.gameObjectManager.findByTag("projectile");this.ctx.fillText(`Projectiles: ${n.length}`,10,i+55)}}updateFPSCounter(e){this.frameCount++,this.fpsTimer+=e,this.fpsTimer>=1e3&&(this.currentFPS=Math.round(this.frameCount*1e3/this.fpsTimer),this.frameCount=0,this.fpsTimer=0)}renderStarField(){this.ctx.fillStyle="#ffffff";for(const e of this.stars)this.ctx.fillRect(e.x,e.y,e.size,e.size)}renderHealthAndLivesUI(){if(!this.playerShip)return;const e=this.playerShip.getHealthStatus(),t=this.canvas.width-220,i=15,s=200,n=20;this.ctx.fillStyle="#333333",this.ctx.fillRect(t,i,s,n),this.ctx.strokeStyle="#666666",this.ctx.lineWidth=2,this.ctx.strokeRect(t,i,s,n);const a=s*e.healthPercentage;let r="#00ff00";e.healthPercentage<.3?r="#ff0000":e.healthPercentage<.6&&(r="#ffaa00"),this.ctx.fillStyle=r,this.ctx.fillRect(t+2,i+2,a-4,n-4),this.ctx.fillStyle="#ffffff",this.ctx.font="12px Arial",this.ctx.textAlign="center",this.ctx.fillText(`${e.health}/${e.maxHealth}`,t+s/2,i+n/2+4);const h=i+n+25;this.ctx.textAlign="right",this.ctx.fillStyle="#ffffff",this.ctx.font="16px Arial",this.ctx.fillText(`Lives: ${e.lives}`,this.canvas.width-20,h);const d=16,m=20,p=this.canvas.width-20-e.lives*m;for(let f=0;f<e.lives;f++){const g=p+f*m,v=h+10;this.ctx.fillStyle="#4A90E2",this.ctx.beginPath(),this.ctx.moveTo(g,v-d/2),this.ctx.lineTo(g-d/3,v+d/3),this.ctx.lineTo(g+d/3,v+d/3),this.ctx.closePath(),this.ctx.fill()}if(e.isInvulnerable){this.ctx.fillStyle="#ffff00",this.ctx.font="12px Arial",this.ctx.textAlign="right";const f=(e.invulnerabilityTimeRemaining/1e3).toFixed(1);this.ctx.fillText(`Invulnerable: ${f}s`,this.canvas.width-20,h+50)}e.isDestroyed&&(this.ctx.fillStyle="rgba(0, 0, 0, 0.7)",this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height),this.ctx.fillStyle="#ff0000",this.ctx.font="48px Arial",this.ctx.textAlign="center",this.ctx.fillText("GAME OVER",this.canvas.width/2,this.canvas.height/2),this.ctx.fillStyle="#ffffff",this.ctx.font="24px Arial",this.ctx.fillText("No lives remaining",this.canvas.width/2,this.canvas.height/2+50))}renderLevelAndScoreUI(){if(!this.levelManager)return;const e=this.levelManager.getLevelStatus();if(this.ctx.fillStyle="#ffffff",this.ctx.font="16px Arial",this.ctx.textAlign="left",this.ctx.fillText(`Level: ${e.currentLevel}`,10,120),this.ctx.fillText(`Score: ${e.score.current.toLocaleString()}`,10,140),this.ctx.font="12px Arial",this.ctx.fillText(`Level Score: ${e.score.level.toLocaleString()}`,10,155),e.levelInProgress){const t=`Enemies: ${e.progress.enemiesDefeated}/${e.progress.requiredEnemies}`;this.ctx.fillText(t,10,175);const i=`Waves: ${e.progress.wavesCompleted}/${e.progress.requiredWaves}`;this.ctx.fillText(i,10,190);const s=(e.completionTime/1e3).toFixed(1);if(this.ctx.fillText(`Time: ${s}s`,10,205),e.levelConfig&&e.levelConfig.timeLimit){const n=e.levelConfig.timeLimit-e.completionTime/1e3;n<=30&&n>0?(this.ctx.fillStyle="#ff6600",this.ctx.fillText(`Time Remaining: ${n.toFixed(1)}s`,10,220)):n<=10&&n>0&&(this.ctx.fillStyle="#ff0000",this.ctx.fillText(`TIME CRITICAL: ${n.toFixed(1)}s`,10,220))}}if(e.performance.perfectCompletion&&(this.ctx.fillStyle="#00ff00",this.ctx.font="10px Arial",this.ctx.fillText("PERFECT RUN",10,240)),e.levelConfig&&e.levelConfig.environment){this.ctx.fillStyle="#cccccc",this.ctx.font="12px Arial";const t=`Environment: ${e.levelConfig.environment.toUpperCase()}`;this.ctx.fillText(t,10,260)}!e.levelInProgress&&e.levelConfig&&this.renderLevelCompletionOverlay(e)}renderLevelCompletionOverlay(e){this.ctx.fillStyle="rgba(0, 0, 0, 0.8)",this.ctx.fillRect(0,0,this.canvas.width,this.canvas.height),this.ctx.fillStyle="#00ff00",this.ctx.font="36px Arial",this.ctx.textAlign="center",this.ctx.fillText(`Level ${e.currentLevel} Complete!`,this.canvas.width/2,this.canvas.height/2-50),this.ctx.fillStyle="#ffffff",this.ctx.font="18px Arial",this.ctx.fillText(`Final Score: ${e.score.level.toLocaleString()}`,this.canvas.width/2,this.canvas.height/2),this.ctx.font="14px Arial",this.ctx.fillText(`Enemies Defeated: ${e.progress.enemiesDefeated}`,this.canvas.width/2,this.canvas.height/2+25);const t=`Completion Time: ${(e.completionTime/1e3).toFixed(2)}s`;this.ctx.fillText(t,this.canvas.width/2,this.canvas.height/2+45),this.ctx.fillStyle="#cccccc",this.ctx.font="12px Arial",this.ctx.fillText("Preparing next level...",this.canvas.width/2,this.canvas.height/2+80)}setupLevelManagerCallbacks(){this.levelManager.setOnLevelStart((e,t)=>{console.log(`Level ${e} started:`,t),this.enemyManager&&(this.enemyManager.reset(),console.log("Enemy manager reset for new level"))}),this.levelManager.setOnLevelComplete(e=>{if(console.log("Level completed:",e),e.completed){if(console.log(`Level ${e.levelNumber} completed in ${e.completionTime.toFixed(2)}s`),console.log(`Score: ${e.score.totalScore}, Enemies: ${e.enemiesDefeated}`),this.tokenManager){const t=this.tokenManager.calculateLevelReward(e);t.totalReward>0&&(this.tokenManager.awardTokens(t.totalReward,"level_completion",{levelNumber:e.levelNumber,completionTime:e.completionTime,score:e.score.totalScore,bonuses:e.bonuses,breakdown:t.breakdown}),console.log(`Awarded ${t.totalReward} WISH tokens for level completion`),console.log("Token reward breakdown:",t.breakdown),e.score.tokenReward=t.totalReward)}this.orangeSDKManager&&this.orangeSDKManager.onLevelCompleted(e),this.showGenieInterface(e)}else console.log(`Level ${e.levelNumber} failed: ${e.reason}`),setTimeout(()=>{e.canRetry&&this.levelManager.startLevel(e.levelNumber)},3e3)}),this.levelManager.setOnScoreUpdate(e=>{})}setupEnemyManagerCallbacks(){this.enemyManager.onWaveComplete=(e,t)=>{console.log(`Wave ${e} completed with bonus: ${t}`),this.levelManager&&this.levelManager.recordWaveCompletion(e,t)},this.enemyManager.onEnemyEscaped=e=>{console.log(`Enemy ${e.id} escaped off-screen`),this.levelManager&&this.levelManager.recordEnemyDefeat(e,0)}}handleCollisions(){if(!this.enemyManager||!this.playerShip)return;const e=this.enemyManager.checkPlayerCollisions(this.playerShip);for(const s of e){const n=this.enemyManager.handlePlayerEnemyCollision(this.playerShip,s);console.log("Player-Enemy collision:",n)}const t=this.gameObjectManager.findByTag("projectile"),i=this.enemyManager.checkProjectileCollisions(t);for(const s of i){const n=this.enemyManager.handleProjectileEnemyCollision(s.projectile,s.enemy,s.damage);n.enemyDestroyed&&this.levelManager&&this.levelManager.recordEnemyDefeat(s.enemy,n.scoreGained)}}setupTokenManagerCallbacks(){this.tokenManager.setOnBalanceUpdate((e,t)=>{console.log(`Token balance updated: ${e} WISH tokens`),this.orangeSDKManager&&this.orangeSDKManager.onTokensChanged({balance:e,statistics:t})}),this.tokenManager.setOnTransaction(e=>{if(console.log("Token transaction:",e),this.orangeSDKManager&&e.amount>=50){const t=e.type==="earned"?{earned:e.amount}:{spent:e.amount};this.orangeSDKManager.onTokensChanged(t)}}),this.tokenManager.setOnRewardEarned((e,t,i)=>{console.log(`Token reward earned: ${e} for ${t}`)})}setupGenieInterfaceCallbacks(){this.genieInterface.setOnPowerUpPurchased((e,t)=>{console.log(`Power-up purchased: ${e.type} for ${e.cost} tokens`)}),this.genieInterface.setOnWarpPurchased((e,t)=>{console.log(`Reality warp purchased: ${e.type} for ${e.cost} tokens`)}),this.genieInterface.setOnClose(()=>{console.log("Genie interface closed, resuming game"),this.continueToNextLevel()})}showGenieInterface(e){console.log("Showing Genie interface for level completion"),this.lastCompletionData=e,this.pause(),this.genieInterface.show()}continueToNextLevel(){if(this.lastCompletionData){const e=this.lastCompletionData;this.lastCompletionData=null,e.nextLevel<=this.levelManager.maxLevels?(console.log(`Starting level ${e.nextLevel}`),this.levelManager.startLevel(e.nextLevel)):console.log("Game completed! All levels finished.")}}async initializeOrangeSDK(){try{await this.orangeSDKManager.initialize(),console.log("Orange SDK initialized successfully")}catch(e){console.error("Failed to initialize Orange SDK:",e)}}setupOrangeSDKCallbacks(){this.orangeSDKManager.setOnSpecialStatusCallback((e,t)=>{this.handleSpecialStatus(e,t)}),this.orangeSDKManager.setOnDataSavedCallback((e,t)=>{console.log(`Game data saved to Orange SDK (${t})`)}),this.orangeSDKManager.setOnSaveErrorCallback((e,t)=>{console.error(`Failed to save game data (${t}):`,e)})}handleSpecialStatus(e,t){switch(e){case"bonus_lives":this.playerShip&&t>0&&(this.playerShip.addLives(t),console.log(`Bonus lives awarded: ${t} (login streak bonus)`));break;case"tournament_participant":t&&console.log("Tournament participant status active");break;case"achievement_unlock":console.log(`Achievement unlocked: ${t}`);break;default:console.log(`Unknown special status: ${e} = ${t}`)}}handleResize(){const e=document.getElementById("game-container");if(e){const{width:t,height:i}=e.getBoundingClientRect();this.canvas.width=t,this.canvas.height=i,console.log(`Canvas resized to: ${this.canvas.width}x${this.canvas.height}`)}}}class re{constructor(e={}){this.config={baseUrl:"https://api.bedrockpassport.com",authCallbackUrl:window.location.origin,tenantId:e.tenantId||"orange-abc123",subscriptionKey:e.subscriptionKey||"your_API_Key",debugMode:e.debugMode||this.isDebugEnvironment()},this.user=null,this.token=null,this.refreshToken=null,this.isAuthenticated=!1,this.authCallbacks=[]}isDebugEnvironment(){return window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1"||window.location.search.includes("debug=true")}async initializeOrangeID(){return this.config.debugMode?(console.log("Debug mode enabled - Orange ID initialization skipped"),!0):new Promise((e,t)=>{setTimeout(()=>{if(console.log("Checking required libraries:",{React:!!window.React,ReactDOM:!!window.ReactDOM,Bedrock:!!window.Bedrock}),!window.React||!window.ReactDOM||!window.Bedrock){console.error("Required libraries not loaded:",{React:!!window.React,ReactDOM:!!window.ReactDOM,Bedrock:!!window.Bedrock}),t(new Error("Required libraries failed to load. Please check your internet connection."));return}console.log("All libraries loaded, setting up Orange ID widget..."),this.setupOrangeIDWidget(e,t)},100)})}setupOrangeIDWidget(e,t){try{const i=document.getElementById("bedrock-login-widget");if(!i){t(new Error("Orange ID container not found"));return}const s=ReactDOM.createRoot(i),n=new URLSearchParams(window.location.search),a=n.get("token"),r=n.get("refreshToken");a&&r?s.render(React.createElement(window.Bedrock.BedrockPassportProvider,this.config,React.createElement(this.createCallbackProcessor(a,r)))):s.render(React.createElement(window.Bedrock.BedrockPassportProvider,this.config,React.createElement(window.Bedrock.LoginPanel,{title:"Sign in to",logo:"https://irp.cdn-website.com/e81c109a/dms3rep/multi/orange-web3-logo-v2a-20241018.svg",logoAlt:"Orange Web3",walletButtonText:"Connect Wallet",showConnectWallet:!1,separatorText:"OR",features:{enableWalletConnect:!1,enableAppleLogin:!0,enableGoogleLogin:!0,enableEmailLogin:!1},onLoginSuccess:(h,d)=>{console.log("Orange ID login success:",h),this.handleAuthSuccess(d.accessToken,d.refreshToken)},onLoginError:h=>{console.error("Orange ID login error:",h),this.handleAuthError(h)}}))),this.setupAuthEventListeners(),e(!0)}catch(i){console.error("Error setting up Orange ID widget:",i),t(i)}}createCallbackProcessor(e,t){const i=this;return function(){const{loginCallback:n}=window.Bedrock.useBedrockPassport();return React.useEffect(()=>{async function a(){try{await n(e,t)?console.log("Callback processing successful"):i.handleAuthError(new Error("Login callback failed"))}catch(r){console.error("Callback processing error:",r),i.handleAuthError(r)}}a()},[n]),React.createElement("div",{style:{textAlign:"center",padding:"20px",color:"#00ffff"}},"Processing authentication...")}}setupAuthEventListeners(){window.addEventListener("orangeAuthSuccess",e=>{this.handleAuthSuccess(e.detail.token,e.detail.refreshToken)}),window.addEventListener("orangeAuthError",e=>{this.handleAuthError(e.detail.error)})}async handleAuthSuccess(e,t){this.token=e,this.refreshToken=t;try{const i=await this.validateToken(e);this.user=i,this.isAuthenticated=!0;const s=new URL(window.location);s.searchParams.delete("token"),s.searchParams.delete("refreshToken"),window.history.replaceState({},document.title,s),this.notifyAuthCallbacks(!0,this.user)}catch(i){this.handleAuthError(i)}}handleAuthError(e){console.error("Authentication error:",e),this.isAuthenticated=!1,this.user=null,this.token=null,this.refreshToken=null,this.notifyAuthCallbacks(!1,e)}async validateToken(e){const t=await fetch("https://api.bedrockpassport.com/api/v1/auth/user",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(!t.ok)throw new Error("Token validation failed");return await t.json()}enableDebugMode(){this.config.debugMode=!0,this.user={id:"debug-user-123",email:"<EMAIL>",name:"Debug User",provider:"debug"},this.isAuthenticated=!0,this.notifyAuthCallbacks(!0,this.user)}async logout(){this.user=null,this.token=null,this.refreshToken=null,this.isAuthenticated=!1,this.notifyAuthCallbacks(!1,null)}getUser(){return this.user}onAuthStateChange(e){this.authCallbacks.push(e)}notifyAuthCallbacks(e,t){this.authCallbacks.forEach(i=>{i(e,t)})}}class le{constructor(e){this.gameEngine=e,this.authManager=new re({tenantId:"orange-abc123",subscriptionKey:"your_API_Key"}),this.container=null,this.isVisible=!1,this.setupEventListeners()}async initialize(){if(this.createMenuHTML(),console.log("MainMenu initializing, debug mode:",this.authManager.config.debugMode),this.authManager.config.debugMode)console.log("Debug mode enabled - Orange ID initialization skipped");else try{console.log("Initializing Orange ID..."),await this.authManager.initializeOrangeID(),console.log("Orange ID initialized successfully")}catch(e){console.error("Failed to initialize Orange ID:",e),this.showError(`Authentication system failed to load: ${e.message}. You can use debug login instead.`)}this.authManager.onAuthStateChange((e,t)=>{e?this.onAuthenticationSuccess(t):this.onAuthenticationFailure(t)})}createMenuHTML(){this.container=document.createElement("div"),this.container.id="main-menu",this.container.className="main-menu",this.container.innerHTML=`
      <div class="menu-background">
        <div class="stars"></div>
        <div class="menu-content">
          <h1 class="game-title">WARPSPACE</h1>
          <p class="game-subtitle">Reality Warping Shooter</p>
          
          <div class="auth-section" id="auth-section">
            <div class="orange-id-container">
              <div id="bedrock-login-widget"></div>
            </div>
            
            <div class="debug-section" id="debug-section">
              <button id="debug-login-btn" class="debug-button">
                🔧 Debug Login (Skip Auth)
              </button>
              <p style="color: #888; font-size: 12px; margin-top: 10px;">
                Debug mode: ${this.authManager.config.debugMode?"ON":"OFF"}
              </p>
            </div>
          </div>
          
          <div class="game-controls" id="game-controls" style="display: none;">
            <button id="start-game-btn" class="menu-button primary">
              Start Game
            </button>
            <button id="genie-shop-btn" class="menu-button">
              🪔 Genie Shop
            </button>
            <button id="instructions-btn" class="menu-button">
              How to Play
            </button>
            <button id="logout-btn" class="menu-button secondary">
              Logout
            </button>
          </div>
          
          <div class="user-info" id="user-info" style="display: none;">
            <p>Welcome, <span id="user-name"></span>!</p>
            <p>WISH Tokens: <span id="token-balance">0</span></p>
          </div>
          
          <div class="error-message" id="error-message" style="display: none;"></div>
        </div>
      </div>
    `,document.body.appendChild(this.container)}setupEventListeners(){document.addEventListener("click",e=>{e.target.id==="debug-login-btn"?this.authManager.enableDebugMode():e.target.id==="start-game-btn"?this.startGame():e.target.id==="genie-shop-btn"?this.showGenieShop():e.target.id==="instructions-btn"?this.showInstructions():e.target.id==="logout-btn"&&this.logout()})}onAuthenticationSuccess(e){console.log("Authentication successful:",e),document.getElementById("auth-section").style.display="none",document.getElementById("game-controls").style.display="block",document.getElementById("user-info").style.display="block",document.getElementById("user-name").textContent=e.name||e.email||"Player",this.loadUserTokenBalance(),this.hideError()}onAuthenticationFailure(e){console.error("Authentication failed:",e),document.getElementById("auth-section").style.display="block",document.getElementById("game-controls").style.display="none",document.getElementById("user-info").style.display="none",e&&typeof e=="object"&&this.showError("Authentication failed. Please try again.")}async loadUserTokenBalance(){document.getElementById("token-balance").textContent="100"}startGame(){if(!this.authManager.isAuthenticated){this.showError("Please authentication first");return}this.hide(),this.gameEngine.startGame(this.authManager.getUser())}showGenieShop(){if(!this.authManager.isAuthenticated){this.showError("Please authenticate first");return}console.log("Opening Genie Shop from main menu..."),this.gameEngine&&this.gameEngine.genieInterface?(this.gameEngine.tokenManager&&this.gameEngine.tokenManager.getBalance()<100&&(this.gameEngine.tokenManager.awardTokens(500,"debug_menu_tokens"),console.log("Awarded debug tokens for testing")),this.gameEngine.genieInterface.show()):this.showError("Genie Shop not available. Please start the game first.")}showInstructions(){alert(`
      WARPSPACE - How to Play
      
      🚀 CONTROLS:
      • Arrow Keys / WASD: Move ship
      • Spacebar: Fire weapons
      • P: Pause game
      
      💎 WISH TOKENS:
      • Earn tokens by completing levels quickly
      • Spend tokens on reality warps and power-ups
      • Visit the Genie between levels to make purchases
      
      🌟 REALITY WARPS:
      • Transform battlefields with AI-generated environments
      • Different environments affect enemy effectiveness
      • Strategic warping can give you tactical advantages
      
      🎯 POWER-UPS:
      • Extra Wingman: Additional firepower
      • Extra Life: Survive longer
      • Spread Ammo: Wider shot pattern
    `)}async logout(){await this.authManager.logout(),this.onAuthenticationFailure(null)}show(){this.container&&(this.container.style.display="block",this.isVisible=!0)}hide(){this.container&&(this.container.style.display="none",this.isVisible=!1)}showError(e){const t=document.getElementById("error-message");t&&(t.textContent=e,t.style.display="block")}hideError(){const e=document.getElementById("error-message");e&&(e.style.display="none")}destroy(){this.container&&(document.body.removeChild(this.container),this.container=null)}}class he{constructor(){this.gameEngine=null,this.mainMenu=null,this.isInitialized=!1}async initialize(){if(!this.isInitialized)try{const e=document.getElementById("gameCanvas");if(!e)throw new Error("Game canvas not found");this.gameEngine=new oe(e),await this.gameEngine.init(),this.mainMenu=new le(this.gameEngine),await this.mainMenu.initialize(),this.mainMenu.show(),this.isInitialized=!0,console.log("Game initialized successfully")}catch(e){console.error("Failed to initialize game:",e),this.showInitializationError(e)}}showInitializationError(e){document.body.innerHTML=`
      <div style="
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: #0a0a2e;
        color: white;
        font-family: Arial, sans-serif;
        text-align: center;
      ">
        <div>
          <h1>Game Initialization Failed</h1>
          <p>Error: ${e.message}</p>
          <button onclick="location.reload()" style="
            background: #00ffff;
            color: #0a0a2e;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
          ">
            Retry
          </button>
        </div>
      </div>
    `}}let w=null;document.addEventListener("DOMContentLoaded",async()=>{w=new he,await w.initialize()});document.addEventListener("visibilitychange",()=>{w&&w.gameEngine&&(document.hidden?w.gameEngine.pause():w.gameEngine.resume())});window.addEventListener("beforeunload",async c=>{w&&w.gameEngine&&await w.gameEngine.destroy()});document.addEventListener("pagehide",async c=>{w&&w.gameEngine&&await w.gameEngine.destroy()});
//# sourceMappingURL=index-B3UBkQnu.js.map
