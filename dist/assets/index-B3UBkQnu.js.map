{"version": 3, "file": "index-B3UBkQnu.js", "sources": ["../../src/utils/Vector2.js", "../../src/input/InputManager.js", "../../src/utils/GameObject.js", "../../src/entities/Projectile.js", "../../src/systems/WeaponSystem.js", "../../src/entities/PlayerShip.js", "../../src/utils/ObjectPool.js", "../../src/utils/GameObjectManager.js", "../../src/config/gameConfig.js", "../../src/managers/LevelManager.js", "../../src/utils/GameMath.js", "../../src/entities/Enemy.js", "../../src/managers/EnemyManager.js", "../../src/managers/TokenEconomyManager.js", "../../src/systems/PowerUp.js", "../../src/ui/GenieInterface.js", "../../node_modules/gg-game-sdk/dist/index.esm.js", "../../src/managers/OrangeSDKManager.js", "../../src/core/GameEngine.js", "../../src/auth/AuthManager.js", "../../src/ui/MainMenu.js", "../../src/main.js"], "sourcesContent": ["/**\n * Vector2 utility class for 2D math operations\n */\nexport class Vector2 {\n    constructor(x = 0, y = 0) {\n        this.x = x;\n        this.y = y;\n    }\n    \n    // Static factory methods\n    static zero() {\n        return new Vector2(0, 0);\n    }\n    \n    static one() {\n        return new Vector2(1, 1);\n    }\n    \n    static up() {\n        return new Vector2(0, -1);\n    }\n    \n    static down() {\n        return new Vector2(0, 1);\n    }\n    \n    static left() {\n        return new Vector2(-1, 0);\n    }\n    \n    static right() {\n        return new Vector2(1, 0);\n    }\n    \n    // Vector operations\n    add(other) {\n        return new Vector2(this.x + other.x, this.y + other.y);\n    }\n    \n    subtract(other) {\n        return new Vector2(this.x - other.x, this.y - other.y);\n    }\n    \n    multiply(scalar) {\n        return new Vector2(this.x * scalar, this.y * scalar);\n    }\n    \n    divide(scalar) {\n        if (scalar === 0) throw new Error('Division by zero');\n        return new Vector2(this.x / scalar, this.y / scalar);\n    }\n    \n    // Magnitude and normalization\n    magnitude() {\n        return Math.sqrt(this.x * this.x + this.y * this.y);\n    }\n    \n    normalize() {\n        const mag = this.magnitude();\n        if (mag === 0) return Vector2.zero();\n        return this.divide(mag);\n    }\n    \n    // Distance calculations\n    distance(other) {\n        return this.subtract(other).magnitude();\n    }\n    \n    // Dot product\n    dot(other) {\n        return this.x * other.x + this.y * other.y;\n    }\n    \n    // In-place operations (modify this vector)\n    addInPlace(other) {\n        this.x += other.x;\n        this.y += other.y;\n        return this;\n    }\n    \n    subtractInPlace(other) {\n        this.x -= other.x;\n        this.y -= other.y;\n        return this;\n    }\n    \n    multiplyInPlace(scalar) {\n        this.x *= scalar;\n        this.y *= scalar;\n        return this;\n    }\n    \n    normalizeInPlace() {\n        const mag = this.magnitude();\n        if (mag > 0) {\n            this.x /= mag;\n            this.y /= mag;\n        }\n        return this;\n    }\n    \n    // Set values\n    set(x, y) {\n        this.x = x;\n        this.y = y;\n        return this;\n    }\n    \n    setFromVector(other) {\n        this.x = other.x;\n        this.y = other.y;\n        return this;\n    }\n    \n    // Angle operations\n    angle() {\n        return Math.atan2(this.y, this.x);\n    }\n    \n    static fromAngle(angle, magnitude = 1) {\n        return new Vector2(\n            Math.cos(angle) * magnitude,\n            Math.sin(angle) * magnitude\n        );\n    }\n    \n    // Rotation\n    rotate(angle) {\n        const cos = Math.cos(angle);\n        const sin = Math.sin(angle);\n        const newX = this.x * cos - this.y * sin;\n        const newY = this.x * sin + this.y * cos;\n        return new Vector2(newX, newY);\n    }\n    \n    rotateInPlace(angle) {\n        const cos = Math.cos(angle);\n        const sin = Math.sin(angle);\n        const newX = this.x * cos - this.y * sin;\n        const newY = this.x * sin + this.y * cos;\n        this.x = newX;\n        this.y = newY;\n        return this;\n    }\n    \n    // Perpendicular vector\n    perpendicular() {\n        return new Vector2(-this.y, this.x);\n    }\n    \n    // Utility methods\n    clone() {\n        return new Vector2(this.x, this.y);\n    }\n    \n    equals(other, tolerance = 0) {\n        if (tolerance === 0) {\n            return this.x === other.x && this.y === other.y;\n        }\n        return Math.abs(this.x - other.x) <= tolerance && \n               Math.abs(this.y - other.y) <= tolerance;\n    }\n    \n    toString() {\n        return `Vector2(${this.x.toFixed(2)}, ${this.y.toFixed(2)})`;\n    }\n}", "import { Vector2 } from '../utils/Vector2.js';\n\n/**\n * InputManager - <PERSON><PERSON> keyboard, mouse, and touch input\n * Provides unified input interface for ship controls\n */\nexport class InputManager {\n    constructor(canvas) {\n        this.canvas = canvas;\n        \n        // Input state tracking\n        this.keys = new Map();\n        this.keysPressed = new Map();\n        this.keysReleased = new Map();\n        \n        this.mousePosition = new Vector2(0, 0);\n        this.mouseButtons = new Map();\n        this.mousePressed = new Map();\n        this.mouseReleased = new Map();\n        \n        this.touches = new Map();\n        this.touchStarted = new Map();\n        this.touchEnded = new Map();\n        \n        // Input mapping configuration\n        this.keyMappings = new Map();\n        this.setupDefaultMappings();\n        \n        // Mobile/touch support\n        this.isTouchDevice = 'ontouchstart' in window;\n        this.virtualJoystick = null;\n        \n        // Bind event handlers\n        this.handleKeyDown = this.handleKeyDown.bind(this);\n        this.handleKeyUp = this.handleKeyUp.bind(this);\n        this.handleMouseDown = this.handleMouseDown.bind(this);\n        this.handleMouseUp = this.handleMouseUp.bind(this);\n        this.handleMouseMove = this.handleMouseMove.bind(this);\n        this.handleTouchStart = this.handleTouchStart.bind(this);\n        this.handleTouchEnd = this.handleTouchEnd.bind(this);\n        this.handleTouchMove = this.handleTouchMove.bind(this);\n        \n        // Initialize event listeners\n        this.init();\n    }\n    \n    init() {\n        // Keyboard events\n        document.addEventListener('keydown', this.handleKeyDown);\n        document.addEventListener('keyup', this.handleKeyUp);\n        \n        // Mouse events\n        this.canvas.addEventListener('mousedown', this.handleMouseDown);\n        this.canvas.addEventListener('mouseup', this.handleMouseUp);\n        this.canvas.addEventListener('mousemove', this.handleMouseMove);\n        \n        // Touch events\n        this.canvas.addEventListener('touchstart', this.handleTouchStart, { passive: false });\n        this.canvas.addEventListener('touchend', this.handleTouchEnd, { passive: false });\n        this.canvas.addEventListener('touchmove', this.handleTouchMove, { passive: false });\n        \n        // Prevent context menu on canvas\n        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());\n        \n        // Initialize virtual joystick for touch devices\n        if (this.isTouchDevice) {\n            this.initVirtualJoystick();\n        }\n        \n        console.log('InputManager initialized');\n    }\n    \n    setupDefaultMappings() {\n        // Movement controls\n        this.keyMappings.set('moveUp', ['ArrowUp', 'KeyW']);\n        this.keyMappings.set('moveDown', ['ArrowDown', 'KeyS']);\n        this.keyMappings.set('moveLeft', ['ArrowLeft', 'KeyA']);\n        this.keyMappings.set('moveRight', ['ArrowRight', 'KeyD']);\n        \n        // Action controls\n        this.keyMappings.set('fire', ['Space', 'Enter']);\n        this.keyMappings.set('pause', ['Escape', 'KeyP']);\n        this.keyMappings.set('interact', ['KeyE', 'KeyF']);\n        \n        // Debug controls\n        this.keyMappings.set('debug', ['F1']);\n    }\n    \n    // Keyboard event handlers\n    handleKeyDown(event) {\n        const key = event.code;\n        \n        if (!this.keys.get(key)) {\n            this.keysPressed.set(key, true);\n        }\n        \n        this.keys.set(key, true);\n        \n        // Prevent default for game controls\n        if (this.isGameKey(key)) {\n            event.preventDefault();\n        }\n    }\n    \n    handleKeyUp(event) {\n        const key = event.code;\n        this.keys.set(key, false);\n        this.keysReleased.set(key, true);\n        \n        if (this.isGameKey(key)) {\n            event.preventDefault();\n        }\n    }\n    \n    // Mouse event handlers\n    handleMouseDown(event) {\n        const button = event.button;\n        \n        if (!this.mouseButtons.get(button)) {\n            this.mousePressed.set(button, true);\n        }\n        \n        this.mouseButtons.set(button, true);\n        this.updateMousePosition(event);\n        \n        event.preventDefault();\n    }\n    \n    handleMouseUp(event) {\n        const button = event.button;\n        this.mouseButtons.set(button, false);\n        this.mouseReleased.set(button, true);\n        this.updateMousePosition(event);\n        \n        event.preventDefault();\n    }\n    \n    handleMouseMove(event) {\n        this.updateMousePosition(event);\n    }\n    \n    updateMousePosition(event) {\n        const rect = this.canvas.getBoundingClientRect();\n        this.mousePosition.set(\n            event.clientX - rect.left,\n            event.clientY - rect.top\n        );\n    }\n    \n    // Touch event handlers\n    handleTouchStart(event) {\n        event.preventDefault();\n        \n        for (const touch of event.changedTouches) {\n            const touchPos = this.getTouchPosition(touch);\n            this.touches.set(touch.identifier, touchPos);\n            this.touchStarted.set(touch.identifier, touchPos.clone());\n            \n            // Update virtual joystick\n            if (this.virtualJoystick) {\n                this.virtualJoystick.handleTouchStart(touch.identifier, touchPos);\n            }\n        }\n    }\n    \n    handleTouchEnd(event) {\n        event.preventDefault();\n        \n        for (const touch of event.changedTouches) {\n            const touchPos = this.getTouchPosition(touch);\n            this.touchEnded.set(touch.identifier, touchPos);\n            this.touches.delete(touch.identifier);\n            \n            // Update virtual joystick\n            if (this.virtualJoystick) {\n                this.virtualJoystick.handleTouchEnd(touch.identifier);\n            }\n        }\n    }\n    \n    handleTouchMove(event) {\n        event.preventDefault();\n        \n        for (const touch of event.changedTouches) {\n            const touchPos = this.getTouchPosition(touch);\n            this.touches.set(touch.identifier, touchPos);\n            \n            // Update virtual joystick\n            if (this.virtualJoystick) {\n                this.virtualJoystick.handleTouchMove(touch.identifier, touchPos);\n            }\n        }\n    }\n    \n    getTouchPosition(touch) {\n        const rect = this.canvas.getBoundingClientRect();\n        return new Vector2(\n            touch.clientX - rect.left,\n            touch.clientY - rect.top\n        );\n    }\n    \n    // Input query methods\n    isKeyDown(key) {\n        return this.keys.get(key) || false;\n    }\n    \n    isKeyPressed(key) {\n        return this.keysPressed.get(key) || false;\n    }\n    \n    isKeyReleased(key) {\n        return this.keysReleased.get(key) || false;\n    }\n    \n    isMouseDown(button = 0) {\n        return this.mouseButtons.get(button) || false;\n    }\n    \n    isMousePressed(button = 0) {\n        return this.mousePressed.get(button) || false;\n    }\n    \n    isMouseReleased(button = 0) {\n        return this.mouseReleased.get(button) || false;\n    }\n    \n    // Action-based input queries\n    isActionDown(action) {\n        const keys = this.keyMappings.get(action);\n        if (!keys) return false;\n        \n        return keys.some(key => this.isKeyDown(key));\n    }\n    \n    isActionPressed(action) {\n        const keys = this.keyMappings.get(action);\n        if (!keys) return false;\n        \n        return keys.some(key => this.isKeyPressed(key));\n    }\n    \n    isActionReleased(action) {\n        const keys = this.keyMappings.get(action);\n        if (!keys) return false;\n        \n        return keys.some(key => this.isKeyReleased(key));\n    }\n    \n    // Movement input helpers\n    getMovementVector() {\n        const movement = new Vector2(0, 0);\n        \n        if (this.isActionDown('moveLeft')) movement.x -= 1;\n        if (this.isActionDown('moveRight')) movement.x += 1;\n        if (this.isActionDown('moveUp')) movement.y -= 1;\n        if (this.isActionDown('moveDown')) movement.y += 1;\n        \n        // Add virtual joystick input for touch devices\n        if (this.virtualJoystick && this.virtualJoystick.isActive()) {\n            const joystickInput = this.virtualJoystick.getInput();\n            movement.addInPlace(joystickInput);\n        }\n        \n        // Normalize diagonal movement\n        if (movement.magnitude() > 1) {\n            movement.normalizeInPlace();\n        }\n        \n        return movement;\n    }\n    \n    // Key mapping management\n    setKeyMapping(action, keys) {\n        this.keyMappings.set(action, Array.isArray(keys) ? keys : [keys]);\n    }\n    \n    addKeyMapping(action, key) {\n        const existing = this.keyMappings.get(action) || [];\n        existing.push(key);\n        this.keyMappings.set(action, existing);\n    }\n    \n    removeKeyMapping(action, key) {\n        const existing = this.keyMappings.get(action) || [];\n        const filtered = existing.filter(k => k !== key);\n        this.keyMappings.set(action, filtered);\n    }\n    \n    // Utility methods\n    isGameKey(key) {\n        for (const keys of this.keyMappings.values()) {\n            if (keys.includes(key)) {\n                return true;\n            }\n        }\n        return false;\n    }\n    \n    // Virtual joystick for touch devices\n    initVirtualJoystick() {\n        this.virtualJoystick = new VirtualJoystick(this.canvas);\n    }\n    \n    // Update method - call once per frame\n    update() {\n        // Clear frame-specific input states\n        this.keysPressed.clear();\n        this.keysReleased.clear();\n        this.mousePressed.clear();\n        this.mouseReleased.clear();\n        this.touchStarted.clear();\n        this.touchEnded.clear();\n        \n        // Update virtual joystick\n        if (this.virtualJoystick) {\n            this.virtualJoystick.update();\n        }\n    }\n    \n    // Render debug info and virtual controls\n    render(ctx) {\n        if (this.virtualJoystick) {\n            this.virtualJoystick.render(ctx);\n        }\n    }\n    \n    // Cleanup\n    destroy() {\n        document.removeEventListener('keydown', this.handleKeyDown);\n        document.removeEventListener('keyup', this.handleKeyUp);\n        \n        this.canvas.removeEventListener('mousedown', this.handleMouseDown);\n        this.canvas.removeEventListener('mouseup', this.handleMouseUp);\n        this.canvas.removeEventListener('mousemove', this.handleMouseMove);\n        \n        this.canvas.removeEventListener('touchstart', this.handleTouchStart);\n        this.canvas.removeEventListener('touchend', this.handleTouchEnd);\n        this.canvas.removeEventListener('touchmove', this.handleTouchMove);\n        \n        if (this.virtualJoystick) {\n            this.virtualJoystick.destroy();\n        }\n        \n        console.log('InputManager destroyed');\n    }\n}\n\n/**\n * Virtual Joystick for touch devices\n */\nclass VirtualJoystick {\n    constructor(canvas) {\n        this.canvas = canvas;\n        this.active = false;\n        this.touchId = null;\n        \n        // Joystick properties\n        this.center = new Vector2(100, canvas.height - 100);\n        this.knobPosition = new Vector2(100, canvas.height - 100);\n        this.maxDistance = 50;\n        this.deadZone = 0.1;\n        \n        // Visual properties\n        this.baseRadius = 60;\n        this.knobRadius = 25;\n        this.baseColor = 'rgba(255, 255, 255, 0.3)';\n        this.knobColor = 'rgba(255, 255, 255, 0.7)';\n    }\n    \n    handleTouchStart(touchId, position) {\n        // Check if touch is within joystick area\n        const distance = position.distance(this.center);\n        if (distance <= this.baseRadius) {\n            this.active = true;\n            this.touchId = touchId;\n            this.knobPosition.setFromVector(position);\n            this.clampKnobPosition();\n        }\n    }\n    \n    handleTouchMove(touchId, position) {\n        if (this.active && this.touchId === touchId) {\n            this.knobPosition.setFromVector(position);\n            this.clampKnobPosition();\n        }\n    }\n    \n    handleTouchEnd(touchId) {\n        if (this.active && this.touchId === touchId) {\n            this.active = false;\n            this.touchId = null;\n            this.knobPosition.setFromVector(this.center);\n        }\n    }\n    \n    clampKnobPosition() {\n        const offset = this.knobPosition.subtract(this.center);\n        if (offset.magnitude() > this.maxDistance) {\n            offset.normalizeInPlace().multiplyInPlace(this.maxDistance);\n            this.knobPosition = this.center.add(offset);\n        }\n    }\n    \n    getInput() {\n        if (!this.active) return new Vector2(0, 0);\n        \n        const offset = this.knobPosition.subtract(this.center);\n        const magnitude = offset.magnitude() / this.maxDistance;\n        \n        if (magnitude < this.deadZone) {\n            return new Vector2(0, 0);\n        }\n        \n        return offset.normalize().multiply(magnitude);\n    }\n    \n    isActive() {\n        return this.active;\n    }\n    \n    update() {\n        // Update joystick position based on screen size changes\n        this.center.set(100, this.canvas.height - 100);\n        if (!this.active) {\n            this.knobPosition.setFromVector(this.center);\n        }\n    }\n    \n    render(ctx) {\n        // Draw base circle\n        ctx.save();\n        ctx.fillStyle = this.baseColor;\n        ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';\n        ctx.lineWidth = 2;\n        \n        ctx.beginPath();\n        ctx.arc(this.center.x, this.center.y, this.baseRadius, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        \n        // Draw knob\n        ctx.fillStyle = this.knobColor;\n        ctx.beginPath();\n        ctx.arc(this.knobPosition.x, this.knobPosition.y, this.knobRadius, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        \n        ctx.restore();\n    }\n    \n    destroy() {\n        // Cleanup if needed\n    }\n}", "import { Vector2 } from './Vector2.js';\n\n/**\n * Base GameObject class for all game entities\n * Provides common functionality for position, movement, and lifecycle\n */\nexport class GameObject {\n    constructor(x = 0, y = 0) {\n        this.position = new Vector2(x, y);\n        this.velocity = new Vector2(0, 0);\n        this.acceleration = new Vector2(0, 0);\n        \n        // Transform properties\n        this.rotation = 0;\n        this.scale = new Vector2(1, 1);\n        \n        // State properties\n        this.active = true;\n        this.visible = true;\n        this.destroyed = false;\n        \n        // Collision properties\n        this.collisionRadius = 0;\n        this.collisionBounds = { x: 0, y: 0, width: 0, height: 0 };\n        \n        // Unique identifier\n        this.id = GameObject.generateId();\n        \n        // Tags for categorization\n        this.tags = new Set();\n    }\n    \n    // Static ID generator\n    static idCounter = 0;\n    static generateId() {\n        return ++GameObject.idCounter;\n    }\n    \n    // Update method - override in subclasses\n    update(deltaTime) {\n        if (!this.active) return;\n        \n        // Apply physics\n        this.velocity.addInPlace(this.acceleration.multiply(deltaTime / 1000));\n        this.position.addInPlace(this.velocity.multiply(deltaTime / 1000));\n        \n        // Update collision bounds\n        this.updateCollisionBounds();\n    }\n    \n    // Render method - override in subclasses\n    render(ctx, interpolation = 0) {\n        if (!this.visible) return;\n        \n        // Interpolated position for smooth rendering\n        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));\n        \n        // Basic debug rendering (override in subclasses)\n        ctx.save();\n        ctx.translate(renderPos.x, renderPos.y);\n        ctx.rotate(this.rotation);\n        ctx.scale(this.scale.x, this.scale.y);\n        \n        // Draw collision bounds for debugging\n        if (this.collisionRadius > 0) {\n            ctx.strokeStyle = '#ff0000';\n            ctx.beginPath();\n            ctx.arc(0, 0, this.collisionRadius, 0, Math.PI * 2);\n            ctx.stroke();\n        }\n        \n        ctx.restore();\n    }\n    \n    // Collision detection methods\n    updateCollisionBounds() {\n        this.collisionBounds.x = this.position.x - this.collisionRadius;\n        this.collisionBounds.y = this.position.y - this.collisionRadius;\n        this.collisionBounds.width = this.collisionRadius * 2;\n        this.collisionBounds.height = this.collisionRadius * 2;\n    }\n    \n    // Check collision with another GameObject\n    collidesWith(other) {\n        if (!this.active || !other.active) return false;\n        \n        // Circle collision detection\n        if (this.collisionRadius > 0 && other.collisionRadius > 0) {\n            const distance = this.position.distance(other.position);\n            return distance < (this.collisionRadius + other.collisionRadius);\n        }\n        \n        return false;\n    }\n    \n    // Lifecycle methods\n    destroy() {\n        this.destroyed = true;\n        this.active = false;\n        this.visible = false;\n    }\n    \n    reset() {\n        this.position.set(0, 0);\n        this.velocity.set(0, 0);\n        this.acceleration.set(0, 0);\n        this.rotation = 0;\n        this.scale.set(1, 1);\n        this.active = true;\n        this.visible = true;\n        this.destroyed = false;\n        this.tags.clear();\n    }\n    \n    // Tag management\n    addTag(tag) {\n        this.tags.add(tag);\n    }\n    \n    removeTag(tag) {\n        this.tags.delete(tag);\n    }\n    \n    hasTag(tag) {\n        return this.tags.has(tag);\n    }\n    \n    // Utility methods\n    distanceTo(other) {\n        return this.position.distance(other.position);\n    }\n    \n    directionTo(other) {\n        return other.position.subtract(this.position).normalize();\n    }\n    \n    lookAt(target) {\n        const direction = this.directionTo(target);\n        this.rotation = direction.angle();\n    }\n    \n    // Movement helpers\n    moveTowards(target, speed, deltaTime) {\n        const direction = this.directionTo(target);\n        const movement = direction.multiply(speed * deltaTime / 1000);\n        this.position.addInPlace(movement);\n    }\n    \n    applyForce(force) {\n        this.acceleration.addInPlace(force);\n    }\n    \n    // Boundary checking\n    isOutOfBounds(bounds) {\n        return this.position.x < bounds.left || \n               this.position.x > bounds.right ||\n               this.position.y < bounds.top || \n               this.position.y > bounds.bottom;\n    }\n    \n    wrapAroundBounds(bounds) {\n        if (this.position.x < bounds.left) this.position.x = bounds.right;\n        if (this.position.x > bounds.right) this.position.x = bounds.left;\n        if (this.position.y < bounds.top) this.position.y = bounds.bottom;\n        if (this.position.y > bounds.bottom) this.position.y = bounds.top;\n    }\n    \n    clampToBounds(bounds) {\n        this.position.x = Math.max(bounds.left, Math.min(bounds.right, this.position.x));\n        this.position.y = Math.max(bounds.top, Math.min(bounds.bottom, this.position.y));\n    }\n}", "import { GameObject } from '../utils/GameObject.js';\nimport { Vector2 } from '../utils/Vector2.js';\n\n/**\n * Projectile class - Represents bullets/projectiles fired by ships\n * Handles movement, collision detection, and visual effects\n */\nexport class Projectile extends GameObject {\n    constructor(x = 0, y = 0) {\n        super(x, y);\n        \n        // Projectile properties\n        this.speed = 600; // pixels per second\n        this.damage = 1;\n        this.lifetime = 3000; // milliseconds\n        this.age = 0;\n        \n        // Visual properties\n        this.width = 4;\n        this.height = 12;\n        this.collisionRadius = 3;\n        this.color = '#FFD700'; // Gold color\n        this.trailColor = '#FFA500'; // Orange trail\n        \n        // Trail effect properties\n        this.trailPositions = [];\n        this.maxTrailLength = 8;\n        this.trailFadeRate = 0.8;\n        \n        // Projectile type and owner\n        this.type = 'player'; // 'player' or 'enemy'\n        this.owner = null;\n        \n        // Add projectile tag\n        this.addTag('projectile');\n        \n        // Initialize as inactive\n        this.active = false;\n        this.visible = false;\n    }\n    \n    /**\n     * Initialize projectile with starting parameters\n     * @param {Vector2} position - Starting position\n     * @param {Vector2} direction - Direction vector (normalized)\n     * @param {number} speed - Projectile speed\n     * @param {string} type - Projectile type ('player' or 'enemy')\n     * @param {GameObject} owner - Object that fired this projectile\n     */\n    initialize(position, direction, speed = 600, type = 'player', owner = null) {\n        this.position.setFromVector(position);\n        this.velocity = direction.normalize().multiply(speed);\n        this.speed = speed;\n        this.type = type;\n        this.owner = owner;\n        this.age = 0;\n        \n        // Clear trail\n        this.trailPositions = [];\n        \n        // Set visual properties based on type\n        this.setupVisualsByType();\n        \n        // Activate projectile\n        this.active = true;\n        this.visible = true;\n        this.destroyed = false;\n        \n        // Add appropriate tags\n        this.tags.clear();\n        this.addTag('projectile');\n        this.addTag(type + 'Projectile');\n        \n        return this;\n    }\n    \n    /**\n     * Set up visual properties based on projectile type\n     */\n    setupVisualsByType() {\n        switch (this.type) {\n            case 'player':\n                this.color = '#FFD700'; // Gold\n                this.trailColor = '#FFA500'; // Orange\n                this.width = 4;\n                this.height = 12;\n                break;\n            case 'enemy':\n                this.color = '#FF4444'; // Red\n                this.trailColor = '#FF8888'; // Light red\n                this.width = 3;\n                this.height = 8;\n                break;\n            default:\n                this.color = '#FFFFFF'; // White\n                this.trailColor = '#CCCCCC'; // Light gray\n                break;\n        }\n    }\n    \n    /**\n     * Update projectile movement and lifetime\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    update(deltaTime) {\n        if (!this.active) return;\n        \n        // Update age\n        this.age += deltaTime;\n        \n        // Check lifetime\n        if (this.age >= this.lifetime) {\n            this.destroy();\n            return;\n        }\n        \n        // Store current position for trail effect\n        this.updateTrail();\n        \n        // Update position using parent class physics\n        super.update(deltaTime);\n        \n        // Update collision bounds\n        this.updateCollisionBounds();\n    }\n    \n    /**\n     * Update trail effect positions\n     */\n    updateTrail() {\n        // Add current position to trail\n        this.trailPositions.unshift(this.position.clone());\n        \n        // Limit trail length\n        if (this.trailPositions.length > this.maxTrailLength) {\n            this.trailPositions.pop();\n        }\n    }\n    \n    /**\n     * Render projectile with trail effects\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} interpolation - Interpolation factor for smooth rendering\n     */\n    render(ctx, interpolation = 0) {\n        if (!this.visible) return;\n        \n        // Calculate interpolated position\n        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));\n        \n        ctx.save();\n        \n        // Draw trail effect\n        this.renderTrail(ctx, interpolation);\n        \n        // Draw main projectile\n        this.renderProjectile(ctx, renderPos);\n        \n        // Draw debug info if enabled\n        if (window.DEBUG_MODE) {\n            this.renderDebugInfo(ctx, renderPos);\n        }\n        \n        ctx.restore();\n    }\n    \n    /**\n     * Render projectile trail effect\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} interpolation - Interpolation factor\n     */\n    renderTrail(ctx, interpolation) {\n        if (this.trailPositions.length < 2) return;\n        \n        ctx.strokeStyle = this.trailColor;\n        ctx.lineWidth = 2;\n        ctx.lineCap = 'round';\n        \n        // Draw trail segments with fading alpha\n        for (let i = 0; i < this.trailPositions.length - 1; i++) {\n            const alpha = Math.pow(this.trailFadeRate, i);\n            const currentPos = this.trailPositions[i];\n            const nextPos = this.trailPositions[i + 1];\n            \n            // Apply interpolation to the most recent trail segment\n            let renderCurrentPos = currentPos;\n            if (i === 0) {\n                const interpolatedOffset = this.velocity.multiply(interpolation / 1000);\n                renderCurrentPos = currentPos.add(interpolatedOffset);\n            }\n            \n            ctx.globalAlpha = alpha;\n            ctx.beginPath();\n            ctx.moveTo(renderCurrentPos.x, renderCurrentPos.y);\n            ctx.lineTo(nextPos.x, nextPos.y);\n            ctx.stroke();\n        }\n        \n        ctx.globalAlpha = 1.0;\n    }\n    \n    /**\n     * Render the main projectile body\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {Vector2} renderPos - Interpolated render position\n     */\n    renderProjectile(ctx, renderPos) {\n        ctx.translate(renderPos.x, renderPos.y);\n        \n        // Main projectile body - simple rectangle for now\n        ctx.fillStyle = this.color;\n        ctx.fillRect(-this.width / 2, -this.height / 2, this.width, this.height);\n        \n        // Add a bright center\n        ctx.fillStyle = '#FFFFFF';\n        ctx.fillRect(-1, -this.height / 2, 2, this.height);\n    }\n    \n    /**\n     * Draw player projectile shape\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    drawPlayerProjectile(ctx) {\n        // Elongated diamond shape\n        ctx.beginPath();\n        ctx.moveTo(0, -this.height / 2); // Top point\n        ctx.lineTo(this.width / 2, 0); // Right point\n        ctx.lineTo(0, this.height / 2); // Bottom point\n        ctx.lineTo(-this.width / 2, 0); // Left point\n        ctx.closePath();\n        ctx.fill();\n        ctx.stroke();\n        \n        // Inner glow effect\n        ctx.fillStyle = this.lightenColor(this.color, 0.3);\n        ctx.beginPath();\n        ctx.moveTo(0, -this.height / 4);\n        ctx.lineTo(this.width / 4, 0);\n        ctx.lineTo(0, this.height / 4);\n        ctx.lineTo(-this.width / 4, 0);\n        ctx.closePath();\n        ctx.fill();\n    }\n    \n    /**\n     * Draw enemy projectile shape\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    drawEnemyProjectile(ctx) {\n        // Simple oval shape\n        ctx.beginPath();\n        ctx.ellipse(0, 0, this.width / 2, this.height / 2, 0, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        \n        // Inner highlight\n        ctx.fillStyle = this.lightenColor(this.color, 0.2);\n        ctx.beginPath();\n        ctx.ellipse(0, -this.height / 6, this.width / 4, this.height / 4, 0, 0, Math.PI * 2);\n        ctx.fill();\n    }\n    \n    /**\n     * Render debug information\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {Vector2} renderPos - Render position\n     */\n    renderDebugInfo(ctx, renderPos) {\n        ctx.resetTransform();\n        \n        // Collision circle\n        ctx.strokeStyle = '#FF0000';\n        ctx.lineWidth = 1;\n        ctx.beginPath();\n        ctx.arc(renderPos.x, renderPos.y, this.collisionRadius, 0, Math.PI * 2);\n        ctx.stroke();\n        \n        // Velocity vector\n        ctx.strokeStyle = '#00FF00';\n        ctx.lineWidth = 1;\n        ctx.beginPath();\n        ctx.moveTo(renderPos.x, renderPos.y);\n        const velocityEnd = renderPos.add(this.velocity.multiply(0.05)); // Scale for visibility\n        ctx.lineTo(velocityEnd.x, velocityEnd.y);\n        ctx.stroke();\n        \n        // Age indicator\n        ctx.fillStyle = '#FFFF00';\n        ctx.font = '10px Arial';\n        ctx.fillText(`${Math.floor(this.age)}ms`, renderPos.x + 10, renderPos.y - 10);\n    }\n    \n    /**\n     * Check if projectile is out of bounds\n     * @param {object} bounds - Boundary object with left, right, top, bottom properties\n     * @returns {boolean} True if out of bounds\n     */\n    isOutOfBounds(bounds) {\n        const margin = Math.max(this.width, this.height);\n        return this.position.x < bounds.left - margin ||\n               this.position.x > bounds.right + margin ||\n               this.position.y < bounds.top - margin ||\n               this.position.y > bounds.bottom + margin;\n    }\n    \n    /**\n     * Handle collision with another object\n     * @param {GameObject} other - Object that was hit\n     */\n    onCollision(other) {\n        // Override in subclasses for specific collision behavior\n        this.destroy();\n    }\n    \n    /**\n     * Reset projectile to inactive state (for object pooling)\n     */\n    reset() {\n        super.reset();\n        this.age = 0;\n        this.trailPositions = [];\n        this.type = 'player';\n        this.owner = null;\n        this.speed = 600;\n        this.damage = 1;\n        this.lifetime = 3000;\n    }\n    \n    /**\n     * Utility function to darken a color\n     * @param {string} color - Hex color string\n     * @param {number} factor - Darkening factor (0-1)\n     * @returns {string} Darkened color\n     */\n    darkenColor(color, factor) {\n        // Simple darkening - in a real implementation you might want more sophisticated color manipulation\n        const hex = color.replace('#', '');\n        const r = Math.floor(parseInt(hex.substr(0, 2), 16) * (1 - factor));\n        const g = Math.floor(parseInt(hex.substr(2, 2), 16) * (1 - factor));\n        const b = Math.floor(parseInt(hex.substr(4, 2), 16) * (1 - factor));\n        return `rgb(${r}, ${g}, ${b})`;\n    }\n    \n    /**\n     * Utility function to lighten a color\n     * @param {string} color - Hex color string\n     * @param {number} factor - Lightening factor (0-1)\n     * @returns {string} Lightened color\n     */\n    lightenColor(color, factor) {\n        const hex = color.replace('#', '');\n        const r = Math.min(255, Math.floor(parseInt(hex.substr(0, 2), 16) * (1 + factor)));\n        const g = Math.min(255, Math.floor(parseInt(hex.substr(2, 2), 16) * (1 + factor)));\n        const b = Math.min(255, Math.floor(parseInt(hex.substr(4, 2), 16) * (1 + factor)));\n        return `rgb(${r}, ${g}, ${b})`;\n    }\n}", "import { Vector2 } from '../utils/Vector2.js';\nimport { Projectile } from '../entities/Projectile.js';\n\n/**\n * WeaponSystem class - Manages weapon firing, projectile spawning, and weapon types\n * Handles firing rates, projectile patterns, and visual effects\n */\nexport class WeaponSystem {\n    constructor(owner, gameObjectManager) {\n        this.owner = owner; // The ship that owns this weapon system\n        this.gameObjectManager = gameObjectManager;\n        \n        // Firing properties\n        this.fireRate = 300; // milliseconds between shots\n        this.lastFireTime = 0;\n        this.canFire = true;\n        \n        // Projectile properties\n        this.projectileSpeed = 600;\n        this.projectileDamage = 25; // Increased from 1 to make enemies killable\n        this.projectileLifetime = 3000;\n        this.projectileType = 'player';\n        \n        // Weapon patterns\n        this.currentPattern = 'single'; // 'single', 'double', 'triple', 'spread'\n        this.previousPattern = 'single'; // For restoring after power-up expires\n        this.spreadAngle = Math.PI / 6; // 30 degrees for spread pattern\n        \n        // Visual effects\n        this.muzzleFlashDuration = 100; // milliseconds\n        this.muzzleFlashTime = 0;\n        this.muzzleFlashPositions = [];\n        \n        // Audio properties (for future sound integration)\n        this.fireSound = null;\n        this.soundVolume = 0.5;\n        \n        // Initialize projectile pool\n        this.initializeProjectilePool();\n        \n        console.log('WeaponSystem initialized for owner:', owner.constructor.name);\n    }\n    \n    /**\n     * Initialize object pool for projectiles\n     */\n    initializeProjectilePool() {\n        // Create projectile pool if it doesn't exist\n        if (!this.gameObjectManager.pools.has('projectile')) {\n            this.gameObjectManager.createPool(\n                'projectile',\n                () => new Projectile(),\n                (projectile) => projectile.reset(),\n                20 // Initial pool size\n            );\n        }\n    }\n    \n    /**\n     * Update weapon system\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    update(deltaTime) {\n        // Update firing cooldown\n        if (!this.canFire) {\n            this.lastFireTime += deltaTime;\n            if (this.lastFireTime >= this.fireRate) {\n                this.canFire = true;\n                this.lastFireTime = 0;\n            }\n        }\n        \n        // Update muzzle flash effect\n        if (this.muzzleFlashTime > 0) {\n            this.muzzleFlashTime -= deltaTime;\n            if (this.muzzleFlashTime <= 0) {\n                this.muzzleFlashPositions = [];\n            }\n        }\n    }\n    \n    /**\n     * Attempt to fire weapon\n     * @param {Vector2} direction - Direction to fire (optional, defaults to up)\n     * @returns {boolean} True if weapon fired successfully\n     */\n    fire(direction = Vector2.up()) {\n        console.log('WeaponSystem.fire() called, canFire:', this.canFire);\n        if (!this.canFire) {\n            console.log('Cannot fire - weapon on cooldown');\n            return false;\n        }\n        \n        // Fire based on current pattern\n        switch (this.currentPattern) {\n            case 'single':\n                this.fireSingle(direction);\n                break;\n            case 'double':\n                this.fireDouble(direction);\n                break;\n            case 'triple':\n                this.fireTriple(direction);\n                break;\n            case 'spread':\n                this.fireSpread(direction);\n                break;\n            default:\n                this.fireSingle(direction);\n                break;\n        }\n        \n        // Set firing cooldown\n        this.canFire = false;\n        this.lastFireTime = 0;\n        \n        // Trigger muzzle flash effect\n        this.triggerMuzzleFlash();\n        \n        // Play fire sound (if available)\n        this.playFireSound();\n        \n        return true;\n    }\n    \n    /**\n     * Fire single projectile\n     * @param {Vector2} direction - Direction to fire\n     */\n    fireSingle(direction) {\n        const firePosition = this.getFirePosition();\n        this.createProjectile(firePosition, direction);\n    }\n    \n    /**\n     * Fire double projectiles (side by side)\n     * @param {Vector2} direction - Direction to fire\n     */\n    fireDouble(direction) {\n        const firePosition = this.getFirePosition();\n        const offset = direction.perpendicular().multiply(8); // 8 pixels apart\n        \n        // Left projectile\n        this.createProjectile(firePosition.subtract(offset), direction);\n        // Right projectile\n        this.createProjectile(firePosition.add(offset), direction);\n    }\n    \n    /**\n     * Fire triple projectiles (center + sides)\n     * @param {Vector2} direction - Direction to fire\n     */\n    fireTriple(direction) {\n        const firePosition = this.getFirePosition();\n        const offset = direction.perpendicular().multiply(12); // 12 pixels apart\n        \n        // Center projectile\n        this.createProjectile(firePosition, direction);\n        // Left projectile\n        this.createProjectile(firePosition.subtract(offset), direction);\n        // Right projectile\n        this.createProjectile(firePosition.add(offset), direction);\n    }\n    \n    /**\n     * Fire spread pattern projectiles\n     * @param {Vector2} direction - Base direction to fire\n     */\n    fireSpread(direction) {\n        const firePosition = this.getFirePosition();\n        const baseAngle = direction.angle();\n        const angleStep = this.spreadAngle / 2; // Spread across spreadAngle\n        \n        // Fire 5 projectiles in spread pattern\n        for (let i = -2; i <= 2; i++) {\n            const angle = baseAngle + (i * angleStep);\n            const spreadDirection = Vector2.fromAngle(angle);\n            this.createProjectile(firePosition, spreadDirection);\n        }\n    }\n    \n    /**\n     * Create and initialize a projectile\n     * @param {Vector2} position - Starting position\n     * @param {Vector2} direction - Direction to fire\n     */\n    createProjectile(position, direction) {\n        // Create projectile directly (bypass pool for now)\n        const projectile = new Projectile();\n        \n        // Initialize projectile\n        projectile.initialize(\n            position,\n            direction,\n            this.projectileSpeed,\n            this.projectileType,\n            this.owner\n        );\n        \n        // Set projectile properties\n        projectile.damage = this.projectileDamage;\n        projectile.lifetime = this.projectileLifetime;\n        \n        // Add to game object manager\n        this.gameObjectManager.add(projectile);\n        \n        console.log('Projectile created at:', projectile.position.toString(), 'with velocity:', projectile.velocity.toString());\n    }\n    \n    /**\n     * Get the position where projectiles should spawn\n     * @returns {Vector2} Fire position\n     */\n    getFirePosition() {\n        // Default to owner's position with slight forward offset\n        const ownerPos = this.owner.position.clone();\n        \n        // Offset based on owner's facing direction (assuming up for player)\n        const forwardOffset = new Vector2(0, -this.owner.height / 2 - 5);\n        \n        return ownerPos.add(forwardOffset);\n    }\n    \n    /**\n     * Trigger muzzle flash visual effect\n     */\n    triggerMuzzleFlash() {\n        this.muzzleFlashTime = this.muzzleFlashDuration;\n        \n        // Store muzzle flash positions based on current pattern\n        this.muzzleFlashPositions = [];\n        \n        switch (this.currentPattern) {\n            case 'single':\n                this.muzzleFlashPositions.push(this.getFirePosition());\n                break;\n            case 'double':\n                const firePos = this.getFirePosition();\n                const offset = new Vector2(8, 0);\n                this.muzzleFlashPositions.push(firePos.subtract(offset));\n                this.muzzleFlashPositions.push(firePos.add(offset));\n                break;\n            case 'triple':\n                const firePos3 = this.getFirePosition();\n                const offset3 = new Vector2(12, 0);\n                this.muzzleFlashPositions.push(firePos3);\n                this.muzzleFlashPositions.push(firePos3.subtract(offset3));\n                this.muzzleFlashPositions.push(firePos3.add(offset3));\n                break;\n            case 'spread':\n                // Single muzzle flash for spread pattern\n                this.muzzleFlashPositions.push(this.getFirePosition());\n                break;\n        }\n    }\n    \n    /**\n     * Play fire sound effect\n     */\n    playFireSound() {\n        // Placeholder for sound system integration\n        if (this.fireSound && typeof this.fireSound.play === 'function') {\n            this.fireSound.volume = this.soundVolume;\n            this.fireSound.currentTime = 0;\n            this.fireSound.play().catch(e => {\n                // Handle audio play errors silently\n                console.warn('Could not play fire sound:', e);\n            });\n        }\n    }\n    \n    /**\n     * Render weapon effects (muzzle flash, etc.)\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    render(ctx) {\n        if (this.muzzleFlashTime > 0) {\n            this.renderMuzzleFlash(ctx);\n        }\n    }\n    \n    /**\n     * Render muzzle flash effect\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    renderMuzzleFlash(ctx) {\n        const flashAlpha = this.muzzleFlashTime / this.muzzleFlashDuration;\n        \n        ctx.save();\n        ctx.globalAlpha = flashAlpha;\n        \n        for (const flashPos of this.muzzleFlashPositions) {\n            // Create radial gradient for muzzle flash\n            const gradient = ctx.createRadialGradient(\n                flashPos.x, flashPos.y, 0,\n                flashPos.x, flashPos.y, 15\n            );\n            gradient.addColorStop(0, '#FFFFFF');\n            gradient.addColorStop(0.3, '#FFD700');\n            gradient.addColorStop(0.6, '#FF6B35');\n            gradient.addColorStop(1, 'rgba(255, 107, 53, 0)');\n            \n            ctx.fillStyle = gradient;\n            ctx.beginPath();\n            ctx.arc(flashPos.x, flashPos.y, 15, 0, Math.PI * 2);\n            ctx.fill();\n            \n            // Add bright center flash\n            ctx.fillStyle = '#FFFFFF';\n            ctx.beginPath();\n            ctx.arc(flashPos.x, flashPos.y, 3, 0, Math.PI * 2);\n            ctx.fill();\n        }\n        \n        ctx.restore();\n    }\n    \n    /**\n     * Set weapon firing pattern\n     * @param {string} pattern - Pattern name ('single', 'double', 'triple', 'spread')\n     */\n    setPattern(pattern) {\n        const validPatterns = ['single', 'double', 'triple', 'spread'];\n        if (validPatterns.includes(pattern)) {\n            this.currentPattern = pattern;\n            console.log(`Weapon pattern changed to: ${pattern}`);\n        } else {\n            console.warn(`Invalid weapon pattern: ${pattern}`);\n        }\n    }\n    \n    /**\n     * Set firing rate\n     * @param {number} rateMs - Time between shots in milliseconds\n     */\n    setFireRate(rateMs) {\n        this.fireRate = Math.max(50, rateMs); // Minimum 50ms between shots\n    }\n    \n    /**\n     * Set projectile speed\n     * @param {number} speed - Projectile speed in pixels per second\n     */\n    setProjectileSpeed(speed) {\n        this.projectileSpeed = Math.max(100, speed); // Minimum speed\n    }\n    \n    /**\n     * Set projectile damage\n     * @param {number} damage - Damage per projectile\n     */\n    setProjectileDamage(damage) {\n        this.projectileDamage = Math.max(1, damage); // Minimum 1 damage\n    }\n    \n    /**\n     * Set spread angle for spread pattern\n     * @param {number} angleRadians - Spread angle in radians\n     */\n    setSpreadAngle(angleRadians) {\n        this.spreadAngle = Math.max(0, Math.min(Math.PI, angleRadians)); // 0 to 180 degrees\n    }\n    \n    /**\n     * Check if weapon can fire\n     * @returns {boolean} True if weapon is ready to fire\n     */\n    isReady() {\n        return this.canFire;\n    }\n    \n    /**\n     * Get firing cooldown progress (0-1)\n     * @returns {number} Cooldown progress\n     */\n    getCooldownProgress() {\n        if (this.canFire) return 1.0;\n        return this.lastFireTime / this.fireRate;\n    }\n    \n    /**\n     * Force weapon to be ready (for power-ups, etc.)\n     */\n    resetCooldown() {\n        this.canFire = true;\n        this.lastFireTime = 0;\n    }\n    \n    /**\n     * Enable or disable spread pattern\n     * @param {boolean} enabled - Whether to enable spread pattern\n     */\n    enableSpreadPattern(enabled) {\n        if (enabled) {\n            this.previousPattern = this.currentPattern;\n            this.setPattern('spread');\n        } else {\n            // Restore previous pattern or default to single\n            const restorePattern = this.previousPattern || 'single';\n            this.setPattern(restorePattern);\n        }\n    }\n\n    /**\n     * Get weapon statistics\n     * @returns {object} Weapon stats\n     */\n    getStats() {\n        return {\n            pattern: this.currentPattern,\n            fireRate: this.fireRate,\n            projectileSpeed: this.projectileSpeed,\n            projectileDamage: this.projectileDamage,\n            isReady: this.canFire,\n            cooldownProgress: this.getCooldownProgress()\n        };\n    }\n}", "import { GameObject } from '../utils/GameObject.js';\nimport { Vector2 } from '../utils/Vector2.js';\nimport { WeaponSystem } from '../systems/WeaponSystem.js';\n\n/**\n * PlayerShip class - Represents the player's ship with movement, rendering, and collision\n * Implements smooth movement with boundary checking and basic sprite rendering\n */\nexport class PlayerShip extends GameObject {\n    constructor(x, y, canvasWidth, canvasHeight, gameObjectManager = null) {\n        super(x, y);\n        \n        // Canvas boundaries for movement constraints\n        this.canvasWidth = canvasWidth;\n        this.canvasHeight = canvasHeight;\n        \n        // Ship properties\n        this.maxSpeed = 300; // pixels per second\n        this.acceleration = 800; // pixels per second squared\n        this.friction = 0.85; // velocity damping factor\n        \n        // Health and lives system\n        this.maxHealth = 100;\n        this.health = this.maxHealth;\n        this.maxLives = 3;\n        this.lives = this.maxLives;\n        this.isInvulnerable = false;\n        this.invulnerabilityDuration = 2000; // 2 seconds in milliseconds\n        this.invulnerabilityTimer = 0;\n        this.isDestroyed = false;\n        \n        // Damage feedback\n        this.damageFlashTimer = 0;\n        this.damageFlashDuration = 200; // 200ms flash duration\n        this.isFlashing = false;\n        \n        // Visual properties\n        this.width = 32;\n        this.height = 48;\n        this.collisionRadius = 16;\n        \n        // Ship boundaries (keep ship fully on screen)\n        this.boundaryPadding = Math.max(this.width, this.height) / 2;\n        \n        // Animation properties\n        this.animationTime = 0;\n        this.thrusterAnimationSpeed = 8; // cycles per second\n        \n        // Movement state\n        this.isMoving = false;\n        this.movementInput = new Vector2(0, 0);\n        \n        // Weapon system\n        this.weaponSystem = null;\n        if (gameObjectManager) {\n            this.weaponSystem = new WeaponSystem(this, gameObjectManager);\n        }\n        \n        // Add player tag\n        this.addTag('player');\n        \n        console.log('PlayerShip created at position:', this.position.toString());\n    }\n    \n    /**\n     * Update ship movement and animation\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     * @param {Vector2} movementInput - Normalized movement input vector\n     */\n    update(deltaTime, movementInput = new Vector2(0, 0)) {\n        if (!this.active) return;\n        \n        // Store movement input for animation\n        this.movementInput = movementInput.clone();\n        this.isMoving = movementInput.magnitude() > 0.1;\n        \n        // Apply movement with acceleration\n        if (this.isMoving) {\n            // Calculate target velocity\n            const targetVelocity = movementInput.multiply(this.maxSpeed);\n            \n            // Apply acceleration towards target velocity\n            const velocityDiff = targetVelocity.subtract(this.velocity);\n            const accelerationForce = velocityDiff.multiply(this.acceleration * deltaTime / 1000);\n            \n            this.velocity.addInPlace(accelerationForce);\n            \n            // Clamp velocity to max speed\n            if (this.velocity.magnitude() > this.maxSpeed) {\n                this.velocity = this.velocity.normalize().multiply(this.maxSpeed);\n            }\n        } else {\n            // Apply friction when not moving\n            this.velocity.multiplyInPlace(Math.pow(this.friction, deltaTime / 16.67));\n            \n            // Stop very small velocities to prevent jitter\n            if (this.velocity.magnitude() < 1) {\n                this.velocity.set(0, 0);\n            }\n        }\n        \n        // Update position\n        this.position.addInPlace(this.velocity.multiply(deltaTime / 1000));\n        \n        // Apply boundary checking\n        this.checkBoundaries();\n        \n        // Update animation timer\n        this.animationTime += deltaTime / 1000;\n        \n        // Update health and lives timers\n        this.updateHealthSystem(deltaTime);\n        \n        // Update collision bounds\n        this.updateCollisionBounds();\n        \n        // Update weapon system\n        if (this.weaponSystem) {\n            this.weaponSystem.update(deltaTime);\n        }\n    }\n    \n    /**\n     * Check and enforce canvas boundaries\n     * Keeps the ship fully visible on screen\n     */\n    checkBoundaries() {\n        const leftBound = this.boundaryPadding;\n        const rightBound = this.canvasWidth - this.boundaryPadding;\n        const topBound = this.boundaryPadding;\n        const bottomBound = this.canvasHeight - this.boundaryPadding;\n        \n        let hitBoundary = false;\n        \n        // Check horizontal boundaries\n        if (this.position.x < leftBound) {\n            this.position.x = leftBound;\n            this.velocity.x = Math.max(0, this.velocity.x); // Stop leftward velocity\n            hitBoundary = true;\n        } else if (this.position.x > rightBound) {\n            this.position.x = rightBound;\n            this.velocity.x = Math.min(0, this.velocity.x); // Stop rightward velocity\n            hitBoundary = true;\n        }\n        \n        // Check vertical boundaries\n        if (this.position.y < topBound) {\n            this.position.y = topBound;\n            this.velocity.y = Math.max(0, this.velocity.y); // Stop upward velocity\n            hitBoundary = true;\n        } else if (this.position.y > bottomBound) {\n            this.position.y = bottomBound;\n            this.velocity.y = Math.min(0, this.velocity.y); // Stop downward velocity\n            hitBoundary = true;\n        }\n        \n        // Optional: Add screen shake or sound effect when hitting boundary\n        if (hitBoundary) {\n            // Could trigger boundary hit effect here\n        }\n    }\n    \n    /**\n     * Render the player ship with sprite graphics and animations\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} interpolation - Interpolation factor for smooth rendering\n     */\n    render(ctx, interpolation = 0) {\n        if (!this.visible) return;\n        \n        // Calculate interpolated position for smooth rendering\n        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));\n        \n        ctx.save();\n        ctx.translate(renderPos.x, renderPos.y);\n        ctx.rotate(this.rotation);\n        \n        // Draw ship body\n        this.drawShipBody(ctx);\n        \n        // Draw thruster effects when moving\n        if (this.isMoving) {\n            this.drawThrusterEffects(ctx);\n        }\n        \n        // Draw collision bounds in debug mode (optional)\n        if (window.DEBUG_MODE) {\n            this.drawDebugInfo(ctx);\n        }\n        \n        ctx.restore();\n        \n        // Render weapon effects (muzzle flash, etc.)\n        if (this.weaponSystem) {\n            this.weaponSystem.render(ctx);\n        }\n    }\n    \n    /**\n     * Draw the main ship body\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    drawShipBody(ctx) {\n        // Apply visual effects based on ship state\n        let shipAlpha = 1.0;\n        let shipColor = '#4A90E2';\n        let strokeColor = '#2E5C8A';\n        \n        // Invulnerability effect - flashing\n        if (this.isInvulnerable) {\n            const flashSpeed = 8; // flashes per second\n            const flashPhase = Math.sin(this.invulnerabilityTimer * flashSpeed * Math.PI / 1000);\n            shipAlpha = 0.3 + 0.7 * Math.abs(flashPhase);\n        }\n        \n        // Damage flash effect - red tint\n        if (this.isFlashing) {\n            const flashIntensity = this.damageFlashTimer / this.damageFlashDuration;\n            shipColor = this.interpolateColor('#4A90E2', '#FF4444', flashIntensity);\n            strokeColor = this.interpolateColor('#2E5C8A', '#CC2222', flashIntensity);\n        }\n        \n        // Apply alpha for transparency effects\n        ctx.globalAlpha = shipAlpha;\n        \n        // Ship body - triangular design\n        ctx.fillStyle = shipColor;\n        ctx.strokeStyle = strokeColor;\n        ctx.lineWidth = 2;\n        \n        ctx.beginPath();\n        // Main body (triangle pointing up)\n        ctx.moveTo(0, -this.height / 2); // Top point\n        ctx.lineTo(-this.width / 3, this.height / 3); // Bottom left\n        ctx.lineTo(this.width / 3, this.height / 3); // Bottom right\n        ctx.closePath();\n        ctx.fill();\n        ctx.stroke();\n        \n        // Ship details - cockpit\n        ctx.fillStyle = '#7BB3F0';\n        ctx.beginPath();\n        ctx.ellipse(0, -this.height / 4, this.width / 6, this.height / 8, 0, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Wing details\n        ctx.fillStyle = '#2E5C8A';\n        ctx.fillRect(-this.width / 4, this.height / 6, this.width / 8, this.height / 4);\n        ctx.fillRect(this.width / 8, this.height / 6, this.width / 8, this.height / 4);\n        \n        // Engine glow (subtle)\n        ctx.fillStyle = '#87CEEB';\n        ctx.beginPath();\n        ctx.ellipse(-this.width / 6, this.height / 3, 3, 6, 0, 0, Math.PI * 2);\n        ctx.ellipse(this.width / 6, this.height / 3, 3, 6, 0, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Restore global alpha\n        ctx.globalAlpha = 1.0;\n    }\n    \n    /**\n     * Draw thruster effects when ship is moving\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    drawThrusterEffects(ctx) {\n        // Animated thruster flames\n        const thrusterIntensity = this.velocity.magnitude() / this.maxSpeed;\n        const animationPhase = Math.sin(this.animationTime * this.thrusterAnimationSpeed * Math.PI * 2);\n        \n        // Base flame length based on movement intensity\n        const baseFlameLength = 15 * thrusterIntensity;\n        const flameVariation = 5 * animationPhase * thrusterIntensity;\n        const flameLength = baseFlameLength + flameVariation;\n        \n        if (flameLength > 2) {\n            // Left thruster\n            this.drawThrusterFlame(ctx, -this.width / 6, this.height / 3, flameLength);\n            \n            // Right thruster\n            this.drawThrusterFlame(ctx, this.width / 6, this.height / 3, flameLength);\n        }\n        \n        // Additional directional thrusters based on movement\n        this.drawDirectionalThrusters(ctx, thrusterIntensity, animationPhase);\n    }\n    \n    /**\n     * Draw individual thruster flame\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} x - X position relative to ship center\n     * @param {number} y - Y position relative to ship center\n     * @param {number} length - Flame length\n     */\n    drawThrusterFlame(ctx, x, y, length) {\n        const gradient = ctx.createLinearGradient(x, y, x, y + length);\n        gradient.addColorStop(0, '#FFD700'); // Gold at base\n        gradient.addColorStop(0.5, '#FF6B35'); // Orange in middle\n        gradient.addColorStop(1, 'rgba(255, 0, 0, 0)'); // Transparent red at tip\n        \n        ctx.fillStyle = gradient;\n        ctx.beginPath();\n        ctx.moveTo(x - 3, y);\n        ctx.lineTo(x + 3, y);\n        ctx.lineTo(x + 1, y + length);\n        ctx.lineTo(x - 1, y + length);\n        ctx.closePath();\n        ctx.fill();\n    }\n    \n    /**\n     * Draw directional thrusters based on movement direction\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} intensity - Movement intensity (0-1)\n     * @param {number} animationPhase - Animation phase (-1 to 1)\n     */\n    drawDirectionalThrusters(ctx, intensity, animationPhase) {\n        const thrusterSize = 3 * intensity;\n        const alpha = 0.7 * intensity;\n        \n        // Side thrusters for horizontal movement\n        if (Math.abs(this.movementInput.x) > 0.1) {\n            ctx.fillStyle = `rgba(135, 206, 235, ${alpha})`;\n            \n            if (this.movementInput.x > 0) {\n                // Moving right, show left thruster\n                ctx.fillRect(-this.width / 2 - thrusterSize, -2, thrusterSize, 4);\n            } else {\n                // Moving left, show right thruster\n                ctx.fillRect(this.width / 2, -2, thrusterSize, 4);\n            }\n        }\n        \n        // Forward thrusters for upward movement\n        if (this.movementInput.y < -0.1) {\n            ctx.fillStyle = `rgba(255, 215, 0, ${alpha})`;\n            const forwardThrusterLength = 8 * intensity * (1 + 0.3 * animationPhase);\n            ctx.fillRect(-2, -this.height / 2 - forwardThrusterLength, 4, forwardThrusterLength);\n        }\n    }\n    \n    /**\n     * Draw debug information (collision bounds, velocity, etc.)\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    drawDebugInfo(ctx) {\n        // Collision circle\n        ctx.strokeStyle = '#FF0000';\n        ctx.lineWidth = 1;\n        ctx.beginPath();\n        ctx.arc(0, 0, this.collisionRadius, 0, Math.PI * 2);\n        ctx.stroke();\n        \n        // Velocity vector\n        if (this.velocity.magnitude() > 1) {\n            ctx.strokeStyle = '#00FF00';\n            ctx.lineWidth = 2;\n            ctx.beginPath();\n            ctx.moveTo(0, 0);\n            const velocityScale = 0.1; // Scale down for visibility\n            ctx.lineTo(this.velocity.x * velocityScale, this.velocity.y * velocityScale);\n            ctx.stroke();\n        }\n        \n        // Ship center point\n        ctx.fillStyle = '#FFFF00';\n        ctx.fillRect(-1, -1, 2, 2);\n    }\n    \n    /**\n     * Get the ship's current speed\n     * @returns {number} Current speed in pixels per second\n     */\n    getCurrentSpeed() {\n        return this.velocity.magnitude();\n    }\n    \n    /**\n     * Check if ship is at the boundary\n     * @returns {object} Object indicating which boundaries are being touched\n     */\n    getBoundaryStatus() {\n        const leftBound = this.boundaryPadding;\n        const rightBound = this.canvasWidth - this.boundaryPadding;\n        const topBound = this.boundaryPadding;\n        const bottomBound = this.canvasHeight - this.boundaryPadding;\n        \n        return {\n            left: this.position.x <= leftBound,\n            right: this.position.x >= rightBound,\n            top: this.position.y <= topBound,\n            bottom: this.position.y >= bottomBound\n        };\n    }\n    \n    /**\n     * Reset ship to starting position and state\n     * @param {number} x - Starting X position\n     * @param {number} y - Starting Y position\n     */\n    resetToPosition(x, y) {\n        this.position.set(x, y);\n        this.velocity.set(0, 0);\n        this.rotation = 0;\n        this.animationTime = 0;\n        this.isMoving = false;\n        this.movementInput.set(0, 0);\n        this.active = true;\n        this.visible = true;\n        this.destroyed = false;\n        \n        // Don't reset health/lives here - that's handled by respawn() or resetHealthAndLives()\n    }\n    \n    /**\n     * Update canvas dimensions (for window resize)\n     * @param {number} width - New canvas width\n     * @param {number} height - New canvas height\n     */\n    updateCanvasDimensions(width, height) {\n        this.canvasWidth = width;\n        this.canvasHeight = height;\n        \n        // Re-check boundaries with new dimensions\n        this.checkBoundaries();\n    }\n    \n    /**\n     * Fire weapon in specified direction\n     * @param {Vector2} direction - Direction to fire (optional, defaults to up)\n     * @returns {boolean} True if weapon fired successfully\n     */\n    fire(direction = Vector2.up()) {\n        console.log('PlayerShip.fire() called, weaponSystem exists:', !!this.weaponSystem);\n        if (this.weaponSystem) {\n            const result = this.weaponSystem.fire(direction);\n            console.log('WeaponSystem.fire() returned:', result);\n            return result;\n        }\n        return false;\n    }\n    \n    /**\n     * Set weapon system (for initialization after construction)\n     * @param {WeaponSystem} weaponSystem - Weapon system instance\n     */\n    setWeaponSystem(weaponSystem) {\n        this.weaponSystem = weaponSystem;\n    }\n    \n    /**\n     * Get weapon system\n     * @returns {WeaponSystem|null} Current weapon system\n     */\n    getWeaponSystem() {\n        return this.weaponSystem;\n    }\n    \n    /**\n     * Check if weapon is ready to fire\n     * @returns {boolean} True if weapon can fire\n     */\n    canFire() {\n        return this.weaponSystem ? this.weaponSystem.isReady() : false;\n    }\n    \n    /**\n     * Set weapon pattern\n     * @param {string} pattern - Weapon pattern ('single', 'double', 'triple', 'spread')\n     */\n    setWeaponPattern(pattern) {\n        if (this.weaponSystem) {\n            this.weaponSystem.setPattern(pattern);\n        }\n    }\n    \n    /**\n     * Get weapon statistics\n     * @returns {object|null} Weapon stats or null if no weapon system\n     */\n    getWeaponStats() {\n        return this.weaponSystem ? this.weaponSystem.getStats() : null;\n    }\n    \n    /**\n     * Update health system timers (invulnerability and damage flash)\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    updateHealthSystem(deltaTime) {\n        // Update invulnerability timer\n        if (this.isInvulnerable) {\n            this.invulnerabilityTimer -= deltaTime;\n            if (this.invulnerabilityTimer <= 0) {\n                this.isInvulnerable = false;\n                this.invulnerabilityTimer = 0;\n            }\n        }\n        \n        // Update damage flash timer\n        if (this.isFlashing) {\n            this.damageFlashTimer -= deltaTime;\n            if (this.damageFlashTimer <= 0) {\n                this.isFlashing = false;\n                this.damageFlashTimer = 0;\n            }\n        }\n    }\n    \n    /**\n     * Take damage and handle health/lives logic\n     * @param {number} damage - Amount of damage to take\n     * @returns {object} Result object with damage taken, health remaining, lives remaining, and destroyed status\n     */\n    takeDamage(damage) {\n        // Can't take damage if invulnerable or already destroyed\n        if (this.isInvulnerable || this.isDestroyed) {\n            return {\n                damageTaken: 0,\n                health: this.health,\n                lives: this.lives,\n                destroyed: this.isDestroyed\n            };\n        }\n        \n        // Apply damage\n        const actualDamage = Math.min(damage, this.health);\n        this.health -= actualDamage;\n        \n        // Trigger damage flash effect\n        this.isFlashing = true;\n        this.damageFlashTimer = this.damageFlashDuration;\n        \n        console.log(`PlayerShip took ${actualDamage} damage. Health: ${this.health}/${this.maxHealth}, Lives: ${this.lives}`);\n        \n        // Check if ship is destroyed\n        if (this.health <= 0) {\n            this.destroyShip();\n        } else {\n            // Grant brief invulnerability after taking damage\n            this.isInvulnerable = true;\n            this.invulnerabilityTimer = this.invulnerabilityDuration;\n        }\n        \n        return {\n            damageTaken: actualDamage,\n            health: this.health,\n            lives: this.lives,\n            destroyed: this.isDestroyed\n        };\n    }\n    \n    /**\n     * Destroy the ship and handle respawn logic\n     */\n    destroyShip() {\n        this.health = 0;\n        this.lives--;\n        \n        console.log(`PlayerShip destroyed! Lives remaining: ${this.lives}`);\n        \n        if (this.lives <= 0) {\n            // Game over - no more lives\n            this.isDestroyed = true;\n            this.active = false;\n            console.log('Game Over - No lives remaining');\n        } else {\n            // Respawn with full health and invulnerability\n            this.respawn();\n        }\n    }\n    \n    /**\n     * Respawn the ship with full health and temporary invulnerability\n     */\n    respawn() {\n        // Reset health\n        this.health = this.maxHealth;\n        \n        // Reset position to starting location\n        const startX = this.canvasWidth / 2;\n        const startY = this.canvasHeight - 100;\n        this.resetToPosition(startX, startY);\n        \n        // Grant extended invulnerability after respawn\n        this.isInvulnerable = true;\n        this.invulnerabilityTimer = this.invulnerabilityDuration * 2; // Double duration after respawn\n        \n        // Reset damage flash\n        this.isFlashing = false;\n        this.damageFlashTimer = 0;\n        \n        console.log(`PlayerShip respawned with full health. Lives: ${this.lives}`);\n    }\n    \n    /**\n     * Heal the ship by a specified amount\n     * @param {number} healAmount - Amount of health to restore\n     * @returns {number} Actual amount healed\n     */\n    heal(healAmount) {\n        if (this.isDestroyed) return 0;\n        \n        const actualHeal = Math.min(healAmount, this.maxHealth - this.health);\n        this.health += actualHeal;\n        \n        console.log(`PlayerShip healed for ${actualHeal}. Health: ${this.health}/${this.maxHealth}`);\n        return actualHeal;\n    }\n    \n    /**\n     * Add extra lives\n     * @param {number} extraLives - Number of lives to add\n     */\n    addLives(extraLives) {\n        this.lives += extraLives;\n        console.log(`PlayerShip gained ${extraLives} lives. Total lives: ${this.lives}`);\n    }\n    \n    /**\n     * Get current health status\n     * @returns {object} Health status object\n     */\n    getHealthStatus() {\n        return {\n            health: this.health,\n            maxHealth: this.maxHealth,\n            healthPercentage: this.health / this.maxHealth,\n            lives: this.lives,\n            maxLives: this.maxLives,\n            isInvulnerable: this.isInvulnerable,\n            invulnerabilityTimeRemaining: this.invulnerabilityTimer,\n            isDestroyed: this.isDestroyed,\n            isFlashing: this.isFlashing\n        };\n    }\n    \n    /**\n     * Reset health and lives to maximum (for new game)\n     */\n    resetHealthAndLives() {\n        this.health = this.maxHealth;\n        this.lives = this.maxLives;\n        this.isInvulnerable = false;\n        this.invulnerabilityTimer = 0;\n        this.isDestroyed = false;\n        this.isFlashing = false;\n        this.damageFlashTimer = 0;\n        \n        console.log('PlayerShip health and lives reset to maximum');\n    }\n    \n    /**\n     * Interpolate between two hex colors\n     * @param {string} color1 - Starting color (hex format)\n     * @param {string} color2 - Ending color (hex format)\n     * @param {number} factor - Interpolation factor (0-1)\n     * @returns {string} Interpolated color in hex format\n     */\n    interpolateColor(color1, color2, factor) {\n        // Clamp factor between 0 and 1\n        factor = Math.max(0, Math.min(1, factor));\n        \n        // Parse hex colors\n        const hex1 = color1.replace('#', '');\n        const hex2 = color2.replace('#', '');\n        \n        const r1 = parseInt(hex1.substr(0, 2), 16);\n        const g1 = parseInt(hex1.substr(2, 2), 16);\n        const b1 = parseInt(hex1.substr(4, 2), 16);\n        \n        const r2 = parseInt(hex2.substr(0, 2), 16);\n        const g2 = parseInt(hex2.substr(2, 2), 16);\n        const b2 = parseInt(hex2.substr(4, 2), 16);\n        \n        // Interpolate each channel\n        const r = Math.round(r1 + (r2 - r1) * factor);\n        const g = Math.round(g1 + (g2 - g1) * factor);\n        const b = Math.round(b1 + (b2 - b1) * factor);\n        \n        // Convert back to hex\n        const toHex = (n) => {\n            const hex = n.toString(16);\n            return hex.length === 1 ? '0' + hex : hex;\n        };\n        \n        return `#${toHex(r)}${toHex(g)}${toHex(b)}`;\n    }\n}", "/**\n * Object Pool for efficient memory management\n * Reuses objects to avoid frequent allocation/deallocation\n */\nexport class ObjectPool {\n    constructor(createFn, resetFn, initialSize = 10) {\n        this.createFn = createFn;\n        this.resetFn = resetFn;\n        this.pool = [];\n        this.active = [];\n        \n        // Pre-populate pool\n        for (let i = 0; i < initialSize; i++) {\n            this.pool.push(this.createFn());\n        }\n    }\n    \n    // Get an object from the pool\n    get() {\n        let obj;\n        if (this.pool.length > 0) {\n            obj = this.pool.pop();\n        } else {\n            obj = this.createFn();\n        }\n        \n        this.active.push(obj);\n        return obj;\n    }\n    \n    // Return an object to the pool\n    release(obj) {\n        const index = this.active.indexOf(obj);\n        if (index !== -1) {\n            this.active.splice(index, 1);\n            this.resetFn(obj);\n            this.pool.push(obj);\n        }\n    }\n    \n    // Release all active objects\n    releaseAll() {\n        while (this.active.length > 0) {\n            const obj = this.active.pop();\n            this.resetFn(obj);\n            this.pool.push(obj);\n        }\n    }\n    \n    // Get pool statistics\n    getStats() {\n        return {\n            pooled: this.pool.length,\n            active: this.active.length,\n            total: this.pool.length + this.active.length\n        };\n    }\n}", "import { ObjectPool } from './ObjectPool.js';\n\n/**\n * GameObjectManager - Manages collections of game objects\n * Provides efficient update, render, and collision detection\n */\nexport class GameObjectManager {\n    constructor() {\n        this.objects = [];\n        this.objectsToAdd = [];\n        this.objectsToRemove = [];\n        this.pools = new Map();\n    }\n    \n    // Add object to manager\n    add(object) {\n        this.objectsToAdd.push(object);\n    }\n    \n    // Remove object from manager\n    remove(object) {\n        this.objectsToRemove.push(object);\n    }\n    \n    // Create object pool for a specific type\n    createPool(type, createFn, resetFn, initialSize = 10) {\n        this.pools.set(type, new ObjectPool(createFn, resetFn, initialSize));\n    }\n    \n    // Get object from pool\n    getFromPool(type) {\n        const pool = this.pools.get(type);\n        if (pool) {\n            return pool.get();\n        }\n        throw new Error(`Pool for type '${type}' not found`);\n    }\n    \n    // Return object to pool\n    returnToPool(type, object) {\n        const pool = this.pools.get(type);\n        if (pool) {\n            pool.release(object);\n            this.remove(object);\n        }\n    }\n    \n    // Update all objects\n    update(deltaTime) {\n        // Process additions and removals\n        this.processAdditions();\n        this.processRemovals();\n        \n        // Update all active objects\n        for (let i = this.objects.length - 1; i >= 0; i--) {\n            const object = this.objects[i];\n            \n            if (object.destroyed) {\n                this.objectsToRemove.push(object);\n                continue;\n            }\n            \n            if (object.active) {\n                object.update(deltaTime);\n            }\n        }\n    }\n    \n    // Render all objects\n    render(ctx, interpolation = 0) {\n        for (const object of this.objects) {\n            if (object.visible && !object.destroyed) {\n                object.render(ctx, interpolation);\n            }\n        }\n    }\n    \n    // Process pending additions\n    processAdditions() {\n        if (this.objectsToAdd.length > 0) {\n            this.objects.push(...this.objectsToAdd);\n            this.objectsToAdd.length = 0;\n        }\n    }\n    \n    // Process pending removals\n    processRemovals() {\n        if (this.objectsToRemove.length > 0) {\n            for (const objectToRemove of this.objectsToRemove) {\n                const index = this.objects.indexOf(objectToRemove);\n                if (index !== -1) {\n                    this.objects.splice(index, 1);\n                }\n            }\n            this.objectsToRemove.length = 0;\n        }\n    }\n    \n    // Find objects by tag\n    findByTag(tag) {\n        return this.objects.filter(obj => obj.hasTag(tag));\n    }\n    \n    // Find object by ID\n    findById(id) {\n        return this.objects.find(obj => obj.id === id);\n    }\n    \n    // Get all active objects\n    getActive() {\n        return this.objects.filter(obj => obj.active && !obj.destroyed);\n    }\n    \n    // Get all visible objects\n    getVisible() {\n        return this.objects.filter(obj => obj.visible && !obj.destroyed);\n    }\n    \n    // Collision detection between groups\n    checkCollisions(group1Tag, group2Tag, callback) {\n        const group1 = this.findByTag(group1Tag);\n        const group2 = this.findByTag(group2Tag);\n        \n        for (const obj1 of group1) {\n            if (!obj1.active) continue;\n            \n            for (const obj2 of group2) {\n                if (!obj2.active) continue;\n                \n                if (obj1.collidesWith(obj2)) {\n                    callback(obj1, obj2);\n                }\n            }\n        }\n    }\n    \n    // Broad phase collision detection using spatial partitioning\n    checkCollisionsOptimized(group1Tag, group2Tag, callback, cellSize = 64) {\n        const group1 = this.findByTag(group1Tag);\n        const group2 = this.findByTag(group2Tag);\n        \n        // Simple spatial hash for broad phase\n        const spatialHash = new Map();\n        \n        // Hash group2 objects\n        for (const obj of group2) {\n            if (!obj.active) continue;\n            \n            const cellX = Math.floor(obj.position.x / cellSize);\n            const cellY = Math.floor(obj.position.y / cellSize);\n            const key = `${cellX},${cellY}`;\n            \n            if (!spatialHash.has(key)) {\n                spatialHash.set(key, []);\n            }\n            spatialHash.get(key).push(obj);\n        }\n        \n        // Check group1 objects against nearby cells\n        for (const obj1 of group1) {\n            if (!obj1.active) continue;\n            \n            const cellX = Math.floor(obj1.position.x / cellSize);\n            const cellY = Math.floor(obj1.position.y / cellSize);\n            \n            // Check surrounding cells\n            for (let dx = -1; dx <= 1; dx++) {\n                for (let dy = -1; dy <= 1; dy++) {\n                    const key = `${cellX + dx},${cellY + dy}`;\n                    const nearbyObjects = spatialHash.get(key);\n                    \n                    if (nearbyObjects) {\n                        for (const obj2 of nearbyObjects) {\n                            if (obj1.collidesWith(obj2)) {\n                                callback(obj1, obj2);\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n    \n    // Clear all objects\n    clear() {\n        this.objects.length = 0;\n        this.objectsToAdd.length = 0;\n        this.objectsToRemove.length = 0;\n        \n        // Clear all pools\n        for (const pool of this.pools.values()) {\n            pool.releaseAll();\n        }\n    }\n    \n    // Get statistics\n    getStats() {\n        const poolStats = {};\n        for (const [type, pool] of this.pools.entries()) {\n            poolStats[type] = pool.getStats();\n        }\n        \n        return {\n            totalObjects: this.objects.length,\n            activeObjects: this.getActive().length,\n            visibleObjects: this.getVisible().length,\n            pendingAdditions: this.objectsToAdd.length,\n            pendingRemovals: this.objectsToRemove.length,\n            pools: poolStats\n        };\n    }\n}", "/**\n * Game configuration constants\n */\nexport const GAME_CONFIG = {\n    // Canvas settings\n    CANVAS_WIDTH: 800,\n    CANVAS_HEIGHT: 600,\n    TARGET_FPS: 60,\n    \n    // Game mechanics\n    PLAYER_LIVES: 3,\n    PLAYER_HEALTH: 100,\n    \n    // Token economy\n    BASE_LEVEL_REWARD: 50,\n    POWER_UP_COSTS: {\n        EXTRA_WINGMAN: 100,\n        EXTRA_LIFE: 150,\n        SPREAD_AMMO: 75\n    },\n    \n    // Reality warp costs\n    WARP_BASE_COST: 200,\n    \n    // Raffle system\n    RAFFLE_PRIZE_POOL_PERCENTAGE: 0.5,\n    RAFFLE_PRIZES: {\n        GOLD: 250,\n        SILVER: 150,\n        BRONZE: 100\n    },\n    \n    // Development settings\n    DEBUG_MODE: false,\n    ENABLE_CONSOLE_LOGS: true\n};\n\nexport const ENVIRONMENT_TYPES = {\n    SPACE: 'space',\n    UNDERWATER: 'underwater',\n    VOLCANIC: 'volcanic',\n    CRYSTAL: 'crystal',\n    FOREST: 'forest',\n    DESERT: 'desert',\n    ICE: 'ice'\n};\n\nexport const ENEMY_TYPES = {\n    WATER: 'water',\n    FIRE: 'fire',\n    AIR: 'air',\n    EARTH: 'earth',\n    CRYSTAL: 'crystal',\n    SHADOW: 'shadow'\n};", "import { GAME_CONFIG, ENVIRONMENT_TYPES } from '../config/gameConfig.js';\nimport { Vector2 } from '../utils/Vector2.js';\n\n/**\n * LevelManager handles level progression, configuration, and completion detection\n * Manages scoring system based on enemies defeated and completion time\n */\nexport class LevelManager {\n    constructor(gameObjectManager = null) {\n        this.gameObjectManager = gameObjectManager;\n        \n        // Level state\n        this.currentLevel = 1;\n        this.levelInProgress = false;\n        this.levelStartTime = 0;\n        this.levelCompletionTime = 0;\n        \n        // Level configuration\n        this.levelConfig = null;\n        this.maxLevels = 50; // Can be expanded\n        \n        // Scoring system\n        this.currentScore = 0;\n        this.levelScore = 0;\n        this.enemiesDefeated = 0;\n        this.levelEnemiesDefeated = 0;\n        this.totalScore = 0;\n        \n        // Performance tracking for token rewards\n        this.levelStartScore = 0;\n        this.perfectCompletion = true; // No damage taken\n        this.speedBonus = 0;\n        this.accuracyBonus = 0;\n        \n        // Level completion criteria\n        this.requiredEnemiesDefeated = 0;\n        this.wavesCompleted = 0;\n        this.requiredWaves = 0;\n        \n        // Callbacks for level events\n        this.onLevelStartCallback = null;\n        this.onLevelCompleteCallback = null;\n        this.onScoreUpdateCallback = null;\n        \n        console.log('LevelManager initialized');\n    }\n    \n    /**\n     * Start a specific level\n     * @param {number} levelNumber - Level number to start\n     * @returns {object} Level configuration\n     */\n    startLevel(levelNumber = null) {\n        if (levelNumber !== null) {\n            this.currentLevel = levelNumber;\n        }\n        \n        // Generate level configuration\n        this.levelConfig = this.generateLevelConfig(this.currentLevel);\n        \n        // Reset level state\n        this.levelInProgress = true;\n        this.levelStartTime = performance.now();\n        this.levelCompletionTime = 0;\n        this.levelScore = 0;\n        this.levelEnemiesDefeated = 0;\n        this.levelStartScore = this.currentScore;\n        this.perfectCompletion = true;\n        this.speedBonus = 0;\n        this.accuracyBonus = 0;\n        \n        // Set completion criteria\n        this.requiredEnemiesDefeated = this.levelConfig.totalEnemies;\n        this.requiredWaves = this.levelConfig.totalWaves;\n        this.wavesCompleted = 0;\n        \n        console.log(`Starting Level ${this.currentLevel}:`, this.levelConfig);\n        \n        // Trigger level start callback\n        if (this.onLevelStartCallback) {\n            this.onLevelStartCallback(this.currentLevel, this.levelConfig);\n        }\n        \n        return this.levelConfig;\n    }\n    \n    /**\n     * Generate level configuration based on level number\n     * @param {number} levelNumber - Level number\n     * @returns {object} Level configuration\n     */\n    generateLevelConfig(levelNumber) {\n        // Base difficulty scaling\n        const difficultyMultiplier = Math.min(3.0, 1.0 + (levelNumber - 1) * 0.1);\n        \n        // Enemy count scaling\n        const baseEnemyCount = 8;\n        const enemyCountIncrease = Math.floor((levelNumber - 1) / 2);\n        const totalEnemies = Math.floor((baseEnemyCount + enemyCountIncrease) * difficultyMultiplier);\n        \n        // Wave configuration\n        const baseWaveCount = 3;\n        const waveIncrease = Math.floor((levelNumber - 1) / 3);\n        const totalWaves = Math.min(8, baseWaveCount + waveIncrease);\n        \n        // Environment selection\n        const environment = this.selectLevelEnvironment(levelNumber);\n        \n        // Boss levels (every 10th level)\n        const hasBoss = levelNumber % 10 === 0;\n        \n        // Special mechanics (every 5th level)\n        const hasSpecialMechanics = levelNumber % 5 === 0;\n        \n        // Time limits for scoring\n        const baseTimeLimit = 120; // 2 minutes base\n        const timeLimitAdjustment = Math.min(60, levelNumber * 2);\n        const timeLimit = baseTimeLimit + timeLimitAdjustment;\n        \n        // Score targets\n        const baseScoreTarget = 1000;\n        const scoreTarget = Math.floor(baseScoreTarget * difficultyMultiplier * levelNumber);\n        \n        return {\n            levelNumber: levelNumber,\n            difficulty: Math.min(10, Math.floor(levelNumber / 5) + 1),\n            totalEnemies: totalEnemies,\n            totalWaves: totalWaves,\n            environment: environment,\n            hasBoss: hasBoss,\n            hasSpecialMechanics: hasSpecialMechanics,\n            timeLimit: timeLimit,\n            scoreTarget: scoreTarget,\n            completionReward: this.calculateBaseLevelReward(levelNumber),\n            difficultyMultiplier: difficultyMultiplier,\n            \n            // Enemy distribution\n            enemyDistribution: this.generateEnemyDistribution(levelNumber, environment),\n            \n            // Special conditions\n            conditions: this.generateLevelConditions(levelNumber),\n            \n            // Visual/Audio settings\n            backgroundMusic: this.selectBackgroundMusic(levelNumber, environment),\n            visualEffects: this.selectVisualEffects(levelNumber, environment)\n        };\n    }\n    \n    /**\n     * Select environment for the level\n     * @param {number} levelNumber - Level number\n     * @returns {string} Environment type\n     */\n    selectLevelEnvironment(levelNumber) {\n        // Early levels in space\n        if (levelNumber <= 5) {\n            return ENVIRONMENT_TYPES.SPACE;\n        }\n        \n        // Introduce variety gradually\n        const environments = [\n            ENVIRONMENT_TYPES.SPACE,\n            ENVIRONMENT_TYPES.UNDERWATER,\n            ENVIRONMENT_TYPES.VOLCANIC,\n            ENVIRONMENT_TYPES.CRYSTAL,\n            ENVIRONMENT_TYPES.FOREST,\n            ENVIRONMENT_TYPES.DESERT,\n            ENVIRONMENT_TYPES.ICE\n        ];\n        \n        // Boss levels have special environments\n        if (levelNumber % 10 === 0) {\n            const bossEnvironments = [\n                ENVIRONMENT_TYPES.VOLCANIC,\n                ENVIRONMENT_TYPES.CRYSTAL,\n                ENVIRONMENT_TYPES.ICE\n            ];\n            return bossEnvironments[Math.floor((levelNumber / 10 - 1) % bossEnvironments.length)];\n        }\n        \n        // Cycle through environments with some randomness\n        const baseIndex = Math.floor((levelNumber - 6) / 3) % environments.length;\n        const variation = (levelNumber + 7) % 3; // Add some variation\n        const finalIndex = (baseIndex + variation) % environments.length;\n        \n        return environments[finalIndex];\n    }\n    \n    /**\n     * Generate enemy distribution for the level\n     * @param {number} levelNumber - Level number\n     * @param {string} environment - Level environment\n     * @returns {object} Enemy distribution configuration\n     */\n    generateEnemyDistribution(levelNumber, environment) {\n        const distribution = {};\n        \n        // Base distribution varies by level progression\n        if (levelNumber <= 3) {\n            // Early levels - simple enemies\n            distribution.basic = 0.7;\n            distribution.advanced = 0.3;\n            distribution.elite = 0.0;\n        } else if (levelNumber <= 10) {\n            // Mid levels - introduce variety\n            distribution.basic = 0.5;\n            distribution.advanced = 0.4;\n            distribution.elite = 0.1;\n        } else {\n            // Later levels - more challenging\n            distribution.basic = 0.3;\n            distribution.advanced = 0.5;\n            distribution.elite = 0.2;\n        }\n        \n        // Environment affects enemy types\n        distribution.environmentalBonus = this.getEnvironmentalEnemyBonus(environment);\n        \n        return distribution;\n    }\n    \n    /**\n     * Get environmental enemy bonus\n     * @param {string} environment - Environment type\n     * @returns {object} Environmental bonus configuration\n     */\n    getEnvironmentalEnemyBonus(environment) {\n        const bonuses = {\n            [ENVIRONMENT_TYPES.SPACE]: { air: 1.2, crystal: 1.1 },\n            [ENVIRONMENT_TYPES.UNDERWATER]: { water: 1.5, earth: 0.8 },\n            [ENVIRONMENT_TYPES.VOLCANIC]: { fire: 1.6, earth: 1.3 },\n            [ENVIRONMENT_TYPES.CRYSTAL]: { crystal: 1.8, shadow: 1.2 },\n            [ENVIRONMENT_TYPES.FOREST]: { earth: 1.4, shadow: 1.3 },\n            [ENVIRONMENT_TYPES.DESERT]: { fire: 1.2, earth: 1.1 },\n            [ENVIRONMENT_TYPES.ICE]: { water: 1.3, air: 0.7 }\n        };\n        \n        return bonuses[environment] || {};\n    }\n    \n    /**\n     * Generate level conditions and objectives\n     * @param {number} levelNumber - Level number\n     * @returns {object} Level conditions\n     */\n    generateLevelConditions(levelNumber) {\n        const conditions = {\n            primary: 'defeat_all_enemies',\n            secondary: [],\n            bonus: []\n        };\n        \n        // Add secondary objectives based on level\n        if (levelNumber >= 3) {\n            conditions.secondary.push('complete_under_time_limit');\n        }\n        \n        if (levelNumber >= 5) {\n            conditions.secondary.push('maintain_accuracy_above_70');\n        }\n        \n        if (levelNumber >= 7) {\n            conditions.secondary.push('take_minimal_damage');\n        }\n        \n        // Bonus objectives for extra rewards\n        conditions.bonus.push('perfect_accuracy');\n        conditions.bonus.push('speed_completion');\n        conditions.bonus.push('no_damage_taken');\n        \n        return conditions;\n    }\n    \n    /**\n     * Select background music for level\n     * @param {number} levelNumber - Level number\n     * @param {string} environment - Environment type\n     * @returns {string} Music track identifier\n     */\n    selectBackgroundMusic(levelNumber, environment) {\n        // Boss levels have special music\n        if (levelNumber % 10 === 0) {\n            return 'boss_theme';\n        }\n        \n        // Environment-based music\n        const musicMap = {\n            [ENVIRONMENT_TYPES.SPACE]: 'space_ambient',\n            [ENVIRONMENT_TYPES.UNDERWATER]: 'underwater_theme',\n            [ENVIRONMENT_TYPES.VOLCANIC]: 'volcanic_intensity',\n            [ENVIRONMENT_TYPES.CRYSTAL]: 'crystal_harmony',\n            [ENVIRONMENT_TYPES.FOREST]: 'forest_mystery',\n            [ENVIRONMENT_TYPES.DESERT]: 'desert_winds',\n            [ENVIRONMENT_TYPES.ICE]: 'ice_caverns'\n        };\n        \n        return musicMap[environment] || 'default_theme';\n    }\n    \n    /**\n     * Select visual effects for level\n     * @param {number} levelNumber - Level number\n     * @param {string} environment - Environment type\n     * @returns {Array} Visual effects configuration\n     */\n    selectVisualEffects(levelNumber, environment) {\n        const effects = [];\n        \n        // Environment-based effects\n        switch (environment) {\n            case ENVIRONMENT_TYPES.UNDERWATER:\n                effects.push('water_bubbles', 'light_rays');\n                break;\n            case ENVIRONMENT_TYPES.VOLCANIC:\n                effects.push('lava_particles', 'heat_distortion');\n                break;\n            case ENVIRONMENT_TYPES.CRYSTAL:\n                effects.push('crystal_reflections', 'energy_pulses');\n                break;\n            case ENVIRONMENT_TYPES.FOREST:\n                effects.push('floating_spores', 'dappled_light');\n                break;\n            case ENVIRONMENT_TYPES.DESERT:\n                effects.push('sand_particles', 'heat_shimmer');\n                break;\n            case ENVIRONMENT_TYPES.ICE:\n                effects.push('snow_particles', 'ice_crystals');\n                break;\n            default:\n                effects.push('star_field', 'nebula_clouds');\n        }\n        \n        // Add intensity effects for higher levels\n        if (levelNumber >= 10) {\n            effects.push('intensity_overlay');\n        }\n        \n        return effects;\n    }\n    \n    /**\n     * Update level progress and check completion\n     * @param {number} deltaTime - Time elapsed since last update\n     * @param {object} gameState - Current game state\n     */\n    update(deltaTime, gameState = {}) {\n        if (!this.levelInProgress) return;\n        \n        // Update level timer\n        this.levelCompletionTime = performance.now() - this.levelStartTime;\n        \n        // Check for level completion\n        this.checkLevelCompletion(gameState);\n        \n        // Update performance metrics\n        this.updatePerformanceMetrics(gameState);\n    }\n    \n    /**\n     * Check if level completion criteria are met\n     * @param {object} gameState - Current game state\n     */\n    checkLevelCompletion(gameState) {\n        if (!this.levelInProgress || !this.levelConfig) return;\n        \n        // Primary completion criteria\n        const enemiesCompleted = this.levelEnemiesDefeated >= this.requiredEnemiesDefeated;\n        const wavesCompleted = this.wavesCompleted >= this.requiredWaves;\n        \n        // Check if level is complete\n        if (enemiesCompleted && wavesCompleted) {\n            this.completeLevel();\n        }\n        \n        // Check for failure conditions\n        const timeExpired = this.levelCompletionTime > (this.levelConfig.timeLimit * 1000);\n        const playerDestroyed = gameState.playerDestroyed || false;\n        \n        if (timeExpired || playerDestroyed) {\n            this.failLevel(timeExpired ? 'time_expired' : 'player_destroyed');\n        }\n    }\n    \n    /**\n     * Complete the current level\n     */\n    completeLevel() {\n        if (!this.levelInProgress) return;\n        \n        this.levelInProgress = false;\n        const completionTimeSeconds = this.levelCompletionTime / 1000;\n        \n        // Calculate final score and bonuses\n        const scoreData = this.calculateLevelScore(completionTimeSeconds);\n        \n        console.log(`Level ${this.currentLevel} completed!`, {\n            time: completionTimeSeconds.toFixed(2) + 's',\n            score: scoreData.totalScore,\n            enemies: this.levelEnemiesDefeated\n        });\n        \n        // Update total score\n        this.currentScore += scoreData.totalScore;\n        this.totalScore = this.currentScore;\n        \n        // Prepare completion data\n        const completionData = {\n            levelNumber: this.currentLevel,\n            completed: true,\n            completionTime: completionTimeSeconds,\n            score: scoreData,\n            enemiesDefeated: this.levelEnemiesDefeated,\n            perfectCompletion: this.perfectCompletion,\n            bonuses: scoreData.bonuses,\n            nextLevel: this.currentLevel + 1\n        };\n        \n        // Trigger completion callback\n        if (this.onLevelCompleteCallback) {\n            this.onLevelCompleteCallback(completionData);\n        }\n        \n        return completionData;\n    }\n    \n    /**\n     * Fail the current level\n     * @param {string} reason - Reason for failure\n     */\n    failLevel(reason) {\n        if (!this.levelInProgress) return;\n        \n        this.levelInProgress = false;\n        const completionTimeSeconds = this.levelCompletionTime / 1000;\n        \n        console.log(`Level ${this.currentLevel} failed: ${reason}`);\n        \n        // Prepare failure data\n        const failureData = {\n            levelNumber: this.currentLevel,\n            completed: false,\n            reason: reason,\n            completionTime: completionTimeSeconds,\n            score: this.levelScore,\n            enemiesDefeated: this.levelEnemiesDefeated,\n            canRetry: true\n        };\n        \n        // Trigger completion callback with failure data\n        if (this.onLevelCompleteCallback) {\n            this.onLevelCompleteCallback(failureData);\n        }\n        \n        return failureData;\n    }\n    \n    /**\n     * Calculate level score based on performance\n     * @param {number} completionTime - Time taken to complete level in seconds\n     * @returns {object} Score breakdown\n     */\n    calculateLevelScore(completionTime) {\n        const config = this.levelConfig;\n        \n        // Base score from enemies defeated\n        const enemyScore = this.levelScore;\n        \n        // Time bonus (faster completion = higher bonus)\n        const timeBonus = this.calculateTimeBonus(completionTime, config.timeLimit);\n        \n        // Accuracy bonus\n        const accuracyBonus = this.calculateAccuracyBonus();\n        \n        // Perfect completion bonus\n        const perfectBonus = this.perfectCompletion ? Math.floor(enemyScore * 0.5) : 0;\n        \n        // Level completion bonus\n        const completionBonus = config.completionReward;\n        \n        // Difficulty multiplier\n        const difficultyMultiplier = config.difficultyMultiplier;\n        \n        // Calculate total before multiplier\n        const baseTotal = enemyScore + timeBonus + accuracyBonus + perfectBonus + completionBonus;\n        \n        // Apply difficulty multiplier\n        const totalScore = Math.floor(baseTotal * difficultyMultiplier);\n        \n        return {\n            enemyScore: enemyScore,\n            timeBonus: timeBonus,\n            accuracyBonus: accuracyBonus,\n            perfectBonus: perfectBonus,\n            completionBonus: completionBonus,\n            difficultyMultiplier: difficultyMultiplier,\n            totalScore: totalScore,\n            bonuses: {\n                speed: timeBonus > 0,\n                accuracy: accuracyBonus > 0,\n                perfect: perfectBonus > 0\n            }\n        };\n    }\n    \n    /**\n     * Calculate time bonus based on completion speed\n     * @param {number} completionTime - Actual completion time in seconds\n     * @param {number} timeLimit - Time limit in seconds\n     * @returns {number} Time bonus score\n     */\n    calculateTimeBonus(completionTime, timeLimit) {\n        const targetTime = timeLimit * 0.6; // Target is 60% of time limit\n        \n        if (completionTime <= targetTime) {\n            // Excellent time - full bonus\n            const maxBonus = 500;\n            const timeRatio = completionTime / targetTime;\n            return Math.floor(maxBonus * (2 - timeRatio)); // Bonus decreases as time increases\n        } else if (completionTime <= timeLimit * 0.8) {\n            // Good time - partial bonus\n            return 200;\n        } else if (completionTime <= timeLimit) {\n            // Acceptable time - small bonus\n            return 50;\n        }\n        \n        return 0; // No bonus for slow completion\n    }\n    \n    /**\n     * Calculate accuracy bonus (placeholder - would need projectile tracking)\n     * @returns {number} Accuracy bonus score\n     */\n    calculateAccuracyBonus() {\n        // This would be calculated based on shots fired vs hits\n        // For now, return a placeholder value\n        return this.accuracyBonus;\n    }\n    \n    /**\n     * Update performance metrics during gameplay\n     * @param {object} gameState - Current game state\n     */\n    updatePerformanceMetrics(gameState) {\n        // Track if player has taken damage\n        if (gameState.playerDamageTaken) {\n            this.perfectCompletion = false;\n        }\n        \n        // Update accuracy tracking (would need more detailed implementation)\n        if (gameState.shotsFired && gameState.shotsHit) {\n            const accuracy = gameState.shotsHit / gameState.shotsFired;\n            if (accuracy >= 0.9) {\n                this.accuracyBonus = 300;\n            } else if (accuracy >= 0.7) {\n                this.accuracyBonus = 150;\n            } else if (accuracy >= 0.5) {\n                this.accuracyBonus = 50;\n            }\n        }\n    }\n    \n    /**\n     * Record enemy defeat for scoring\n     * @param {object} enemy - Defeated enemy\n     * @param {number} scoreValue - Score value of the enemy\n     */\n    recordEnemyDefeat(enemy, scoreValue) {\n        this.enemiesDefeated++;\n        this.levelEnemiesDefeated++;\n        this.levelScore += scoreValue;\n        this.currentScore += scoreValue;\n        \n        // Trigger score update callback\n        if (this.onScoreUpdateCallback) {\n            this.onScoreUpdateCallback({\n                enemiesDefeated: this.enemiesDefeated,\n                levelEnemiesDefeated: this.levelEnemiesDefeated,\n                currentScore: this.currentScore,\n                levelScore: this.levelScore,\n                scoreGained: scoreValue\n            });\n        }\n    }\n    \n    /**\n     * Record wave completion\n     * @param {number} waveNumber - Completed wave number\n     * @param {number} waveBonus - Bonus score for wave completion\n     */\n    recordWaveCompletion(waveNumber, waveBonus = 0) {\n        this.wavesCompleted++;\n        \n        if (waveBonus > 0) {\n            this.levelScore += waveBonus;\n            this.currentScore += waveBonus;\n        }\n        \n        console.log(`Wave ${waveNumber} completed. Waves: ${this.wavesCompleted}/${this.requiredWaves}`);\n        \n        // Trigger score update callback\n        if (this.onScoreUpdateCallback) {\n            this.onScoreUpdateCallback({\n                wavesCompleted: this.wavesCompleted,\n                currentScore: this.currentScore,\n                levelScore: this.levelScore,\n                scoreGained: waveBonus\n            });\n        }\n    }\n    \n    /**\n     * Calculate base level reward for token economy\n     * @param {number} levelNumber - Level number\n     * @returns {number} Base reward amount\n     */\n    calculateBaseLevelReward(levelNumber) {\n        const baseReward = GAME_CONFIG.BASE_LEVEL_REWARD;\n        const levelMultiplier = Math.floor(levelNumber / 5) + 1;\n        return baseReward * levelMultiplier;\n    }\n    \n    /**\n     * Get current level status\n     * @returns {object} Level status information\n     */\n    getLevelStatus() {\n        return {\n            currentLevel: this.currentLevel,\n            levelInProgress: this.levelInProgress,\n            levelConfig: this.levelConfig,\n            completionTime: this.levelCompletionTime,\n            score: {\n                current: this.currentScore,\n                level: this.levelScore,\n                total: this.totalScore\n            },\n            progress: {\n                enemiesDefeated: this.levelEnemiesDefeated,\n                requiredEnemies: this.requiredEnemiesDefeated,\n                wavesCompleted: this.wavesCompleted,\n                requiredWaves: this.requiredWaves\n            },\n            performance: {\n                perfectCompletion: this.perfectCompletion,\n                speedBonus: this.speedBonus,\n                accuracyBonus: this.accuracyBonus\n            }\n        };\n    }\n    \n    /**\n     * Reset level manager state\n     */\n    reset() {\n        this.currentLevel = 1;\n        this.levelInProgress = false;\n        this.levelStartTime = 0;\n        this.levelCompletionTime = 0;\n        this.currentScore = 0;\n        this.levelScore = 0;\n        this.enemiesDefeated = 0;\n        this.levelEnemiesDefeated = 0;\n        this.totalScore = 0;\n        this.perfectCompletion = true;\n        this.speedBonus = 0;\n        this.accuracyBonus = 0;\n        this.wavesCompleted = 0;\n        \n        console.log('LevelManager reset');\n    }\n    \n    /**\n     * Set callback for level start events\n     * @param {Function} callback - Callback function\n     */\n    setOnLevelStart(callback) {\n        this.onLevelStartCallback = callback;\n    }\n    \n    /**\n     * Set callback for level completion events\n     * @param {Function} callback - Callback function\n     */\n    setOnLevelComplete(callback) {\n        this.onLevelCompleteCallback = callback;\n    }\n    \n    /**\n     * Set callback for score update events\n     * @param {Function} callback - Callback function\n     */\n    setOnScoreUpdate(callback) {\n        this.onScoreUpdateCallback = callback;\n    }\n}", "/**\n * Game math utilities\n */\nexport class GameMath {\n    // Clamp value between min and max\n    static clamp(value, min, max) {\n        return Math.min(Math.max(value, min), max);\n    }\n    \n    // Linear interpolation\n    static lerp(start, end, t) {\n        return start + (end - start) * t;\n    }\n    \n    // Check if point is within rectangle bounds\n    static pointInRect(point, rect) {\n        return point.x >= rect.x && \n               point.x <= rect.x + rect.width &&\n               point.y >= rect.y && \n               point.y <= rect.y + rect.height;\n    }\n    \n    // Check collision between two rectangles\n    static rectCollision(rect1, rect2) {\n        return rect1.x < rect2.x + rect2.width &&\n               rect1.x + rect1.width > rect2.x &&\n               rect1.y < rect2.y + rect2.height &&\n               rect1.y + rect1.height > rect2.y;\n    }\n    \n    // Check collision between two circles\n    static circleCollision(circle1, circle2) {\n        const dx = circle1.x - circle2.x;\n        const dy = circle1.y - circle2.y;\n        const distance = Math.sqrt(dx * dx + dy * dy);\n        return distance < circle1.radius + circle2.radius;\n    }\n    \n    // Random number between min and max\n    static random(min, max) {\n        return Math.random() * (max - min) + min;\n    }\n    \n    // Random integer between min and max (inclusive)\n    static randomInt(min, max) {\n        return Math.floor(Math.random() * (max - min + 1)) + min;\n    }\n    \n    // Convert degrees to radians\n    static degToRad(degrees) {\n        return degrees * (Math.PI / 180);\n    }\n    \n    // Convert radians to degrees\n    static radToDeg(radians) {\n        return radians * (180 / Math.PI);\n    }\n    \n    // Advanced collision detection\n    static circleRectCollision(circle, rect) {\n        // Find the closest point on the rectangle to the circle center\n        const closestX = GameMath.clamp(circle.x, rect.x, rect.x + rect.width);\n        const closestY = GameMath.clamp(circle.y, rect.y, rect.y + rect.height);\n        \n        // Calculate distance from circle center to closest point\n        const dx = circle.x - closestX;\n        const dy = circle.y - closestY;\n        const distance = Math.sqrt(dx * dx + dy * dy);\n        \n        return distance < circle.radius;\n    }\n    \n    // Line-circle intersection\n    static lineCircleCollision(lineStart, lineEnd, circle) {\n        const dx = lineEnd.x - lineStart.x;\n        const dy = lineEnd.y - lineStart.y;\n        const fx = lineStart.x - circle.x;\n        const fy = lineStart.y - circle.y;\n        \n        const a = dx * dx + dy * dy;\n        const b = 2 * (fx * dx + fy * dy);\n        const c = (fx * fx + fy * fy) - circle.radius * circle.radius;\n        \n        const discriminant = b * b - 4 * a * c;\n        \n        if (discriminant < 0) return false;\n        \n        const discriminantSqrt = Math.sqrt(discriminant);\n        const t1 = (-b - discriminantSqrt) / (2 * a);\n        const t2 = (-b + discriminantSqrt) / (2 * a);\n        \n        return (t1 >= 0 && t1 <= 1) || (t2 >= 0 && t2 <= 1);\n    }\n    \n    // Smooth step interpolation\n    static smoothStep(edge0, edge1, x) {\n        const t = GameMath.clamp((x - edge0) / (edge1 - edge0), 0, 1);\n        return t * t * (3 - 2 * t);\n    }\n    \n    // Ease in/out functions\n    static easeInQuad(t) {\n        return t * t;\n    }\n    \n    static easeOutQuad(t) {\n        return t * (2 - t);\n    }\n    \n    static easeInOutQuad(t) {\n        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n    }\n    \n    // Wrap angle to [-PI, PI]\n    static wrapAngle(angle) {\n        while (angle > Math.PI) angle -= 2 * Math.PI;\n        while (angle < -Math.PI) angle += 2 * Math.PI;\n        return angle;\n    }\n    \n    // Angle difference (shortest path)\n    static angleDifference(angle1, angle2) {\n        let diff = angle2 - angle1;\n        return GameMath.wrapAngle(diff);\n    }\n    \n    // Move towards target with maximum step\n    static moveTowards(current, target, maxStep) {\n        const diff = target - current;\n        if (Math.abs(diff) <= maxStep) {\n            return target;\n        }\n        return current + Math.sign(diff) * maxStep;\n    }\n    \n    // Check if value is approximately equal (with tolerance)\n    static approximately(a, b, tolerance = 0.0001) {\n        return Math.abs(a - b) < tolerance;\n    }\n    \n    // Map value from one range to another\n    static map(value, fromMin, fromMax, toMin, toMax) {\n        return (value - fromMin) * (toMax - toMin) / (fromMax - fromMin) + toMin;\n    }\n}", "import { GameObject } from '../utils/GameObject.js';\nimport { Vector2 } from '../utils/Vector2.js';\nimport { GameMath } from '../utils/GameMath.js';\nimport { ENEMY_TYPES } from '../config/gameConfig.js';\n\n/**\n * Enemy base class with various movement patterns and behaviors\n * Supports different enemy types with environmental effectiveness modifiers\n */\nexport class Enemy extends GameObject {\n    constructor(x, y, type = ENEMY_TYPES.AIR, canvasWidth = 800, canvasHeight = 600) {\n        super(x, y);\n        \n        // Canvas boundaries\n        this.canvasWidth = canvasWidth;\n        this.canvasHeight = canvasHeight;\n        \n        // Enemy properties\n        this.type = type;\n        this.maxHealth = this.getTypeMaxHealth(type);\n        this.health = this.maxHealth;\n        this.baseSpeed = this.getTypeBaseSpeed(type);\n        this.currentSpeed = this.baseSpeed;\n        \n        // Visual properties\n        this.width = 24;\n        this.height = 24;\n        this.collisionRadius = 12;\n        \n        // Movement behavior\n        this.movementPattern = 'linear';\n        this.movementTimer = 0;\n        this.movementPhase = Math.random() * Math.PI * 2; // Random starting phase\n        this.amplitude = 50; // For sine wave patterns\n        this.frequency = 2; // For oscillating patterns\n\n        // Predefined movement pattern properties\n        this.predefinedPattern = null;\n        this.predefinedState = 0; // Current state in predefined pattern\n        this.predefinedTimer = 0; // Timer for predefined pattern phases\n        this.initialPosition = new Vector2(x, y); // Store initial spawn position\n        \n        // Formation flying properties\n        this.formationOffset = new Vector2(0, 0);\n        this.formationTarget = new Vector2(x, y);\n        this.isInFormation = false;\n        \n        // Attack behavior\n        this.attackCooldown = 0;\n        this.baseAttackRate = this.getTypeAttackRate(type);\n        this.currentAttackRate = this.baseAttackRate;\n        this.canAttack = true;\n        \n        // Environmental effectiveness\n        this.environmentalEffectiveness = 1.0;\n        this.currentEnvironment = 'space';\n        \n        // Animation properties\n        this.animationTime = 0;\n        this.animationSpeed = 4; // cycles per second\n        this.spriteFrame = 0;\n        this.totalFrames = 4;\n        \n        // State properties\n        this.isDestroyed = false;\n        this.deathAnimationTime = 0;\n        this.deathAnimationDuration = 500; // 500ms death animation\n        \n        // Scoring\n        this.scoreValue = this.getTypeScoreValue(type);\n        \n        // Add enemy tag\n        this.addTag('enemy');\n        this.addTag(type);\n        \n        // Set initial movement direction\n        this.setMovementPattern('linear');\n        \n        console.log(`Enemy created: type=${type}, health=${this.health}, position=${this.position.toString()}`);\n    }\n    \n    /**\n     * Get maximum health based on enemy type\n     * @param {string} type - Enemy type\n     * @returns {number} Maximum health\n     */\n    getTypeMaxHealth(type) {\n        const healthMap = {\n            [ENEMY_TYPES.AIR]: 20,\n            [ENEMY_TYPES.WATER]: 30,\n            [ENEMY_TYPES.FIRE]: 25,\n            [ENEMY_TYPES.EARTH]: 40,\n            [ENEMY_TYPES.CRYSTAL]: 35,\n            [ENEMY_TYPES.SHADOW]: 15\n        };\n        return healthMap[type] || 20;\n    }\n    \n    /**\n     * Get base movement speed based on enemy type\n     * @param {string} type - Enemy type\n     * @returns {number} Base speed in pixels per second\n     */\n    getTypeBaseSpeed(type) {\n        const speedMap = {\n            [ENEMY_TYPES.AIR]: 120,\n            [ENEMY_TYPES.WATER]: 80,\n            [ENEMY_TYPES.FIRE]: 100,\n            [ENEMY_TYPES.EARTH]: 60,\n            [ENEMY_TYPES.CRYSTAL]: 90,\n            [ENEMY_TYPES.SHADOW]: 140\n        };\n        return speedMap[type] || 100;\n    }\n    \n    /**\n     * Get base attack rate based on enemy type (attacks per second)\n     * @param {string} type - Enemy type\n     * @returns {number} Base attack rate\n     */\n    getTypeAttackRate(type) {\n        const attackRateMap = {\n            [ENEMY_TYPES.AIR]: 1.5,\n            [ENEMY_TYPES.WATER]: 1.0,\n            [ENEMY_TYPES.FIRE]: 2.0,\n            [ENEMY_TYPES.EARTH]: 0.8,\n            [ENEMY_TYPES.CRYSTAL]: 1.2,\n            [ENEMY_TYPES.SHADOW]: 2.5\n        };\n        return attackRateMap[type] || 1.0;\n    }\n    \n    /**\n     * Get score value based on enemy type\n     * @param {string} type - Enemy type\n     * @returns {number} Score value\n     */\n    getTypeScoreValue(type) {\n        const scoreMap = {\n            [ENEMY_TYPES.AIR]: 100,\n            [ENEMY_TYPES.WATER]: 150,\n            [ENEMY_TYPES.FIRE]: 125,\n            [ENEMY_TYPES.EARTH]: 200,\n            [ENEMY_TYPES.CRYSTAL]: 175,\n            [ENEMY_TYPES.SHADOW]: 300\n        };\n        return scoreMap[type] || 100;\n    }\n    \n    /**\n     * Update enemy behavior, movement, and animation\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     * @param {Vector2} playerPosition - Player's current position for targeting\n     */\n    update(deltaTime, playerPosition = null) {\n        if (!this.active) return;\n        \n        // Update timers\n        this.movementTimer += deltaTime / 1000;\n        this.animationTime += deltaTime / 1000;\n        \n        // Handle death animation\n        if (this.isDestroyed) {\n            this.updateDeathAnimation(deltaTime);\n            return;\n        }\n        \n        // Update movement based on pattern\n        this.updateMovement(deltaTime, playerPosition);\n        \n        // Update attack cooldown\n        this.updateAttackCooldown(deltaTime);\n        \n        // Update animation frame\n        this.updateAnimation(deltaTime);\n        \n        // Check if enemy has moved off screen (for cleanup)\n        this.checkOffScreen();\n        \n        // Update collision bounds\n        this.updateCollisionBounds();\n    }\n    \n    /**\n     * Update movement based on current movement pattern\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     * @param {Vector2} playerPosition - Player's current position\n     */\n    updateMovement(deltaTime, playerPosition) {\n        const dt = deltaTime / 1000;\n        \n        switch (this.movementPattern) {\n            case 'linear':\n                this.updateLinearMovement(dt);\n                break;\n            case 'sine':\n                this.updateSineMovement(dt);\n                break;\n            case 'spiral':\n                this.updateSpiralMovement(dt);\n                break;\n            case 'dive':\n                this.updateDiveMovement(dt, playerPosition);\n                break;\n            case 'formation':\n                this.updateFormationMovement(dt);\n                break;\n            case 'zigzag':\n                this.updateZigzagMovement(dt);\n                break;\n            case 'circle':\n                this.updateCircleMovement(dt);\n                break;\n            case 'hover':\n                this.updateHoverMovement(dt, playerPosition);\n                break;\n            case 'predefined':\n                this.updatePredefinedMovement(dt, playerPosition);\n                break;\n        }\n        \n        // Apply environmental speed modifier\n        const effectiveSpeed = this.currentSpeed * this.environmentalEffectiveness;\n        this.velocity.multiplyInPlace(effectiveSpeed / this.velocity.magnitude());\n        \n        // Update position\n        this.position.addInPlace(this.velocity.multiply(dt));\n    }\n    \n    /**\n     * Linear downward movement (classic Space Invaders style)\n     * @param {number} dt - Delta time in seconds\n     */\n    updateLinearMovement(dt) {\n        this.velocity.set(0, this.currentSpeed);\n    }\n    \n    /**\n     * Sine wave movement (classic Galaga style)\n     * @param {number} dt - Delta time in seconds\n     */\n    updateSineMovement(dt) {\n        const sineOffset = Math.sin(this.movementTimer * this.frequency + this.movementPhase) * this.amplitude;\n        const targetX = this.formationTarget.x + sineOffset;\n        \n        // Move towards target X position while moving down\n        const xDirection = Math.sign(targetX - this.position.x);\n        this.velocity.set(xDirection * this.currentSpeed * 0.5, this.currentSpeed * 0.8);\n    }\n    \n    /**\n     * Spiral movement pattern\n     * @param {number} dt - Delta time in seconds\n     */\n    updateSpiralMovement(dt) {\n        const radius = this.amplitude * (1 - this.movementTimer * 0.1);\n        const angle = this.movementTimer * this.frequency + this.movementPhase;\n        \n        const targetX = this.formationTarget.x + Math.cos(angle) * radius;\n        const targetY = this.formationTarget.y + this.movementTimer * this.currentSpeed * 0.3;\n        \n        const direction = new Vector2(targetX - this.position.x, targetY - this.position.y).normalize();\n        this.velocity = direction.multiply(this.currentSpeed);\n    }\n    \n    /**\n     * Dive attack movement (Galaga-style dive bomb)\n     * @param {number} dt - Delta time in seconds\n     * @param {Vector2} playerPosition - Player's position\n     */\n    updateDiveMovement(dt, playerPosition) {\n        if (!playerPosition) {\n            this.updateLinearMovement(dt);\n            return;\n        }\n        \n        if (this.movementTimer < 1.0) {\n            // Initial curve out\n            const curveDirection = this.movementPhase > Math.PI ? -1 : 1;\n            this.velocity.set(curveDirection * this.currentSpeed, this.currentSpeed * 0.5);\n        } else if (this.movementTimer < 2.0) {\n            // Dive towards player\n            const direction = playerPosition.subtract(this.position).normalize();\n            this.velocity = direction.multiply(this.currentSpeed * 1.5);\n        } else {\n            // Return to formation or continue off screen\n            this.velocity.set(0, this.currentSpeed);\n        }\n    }\n    \n    /**\n     * Formation flying movement\n     * @param {number} dt - Delta time in seconds\n     */\n    updateFormationMovement(dt) {\n        const targetPos = this.formationTarget.add(this.formationOffset);\n        const direction = targetPos.subtract(this.position);\n        \n        if (direction.magnitude() > 5) {\n            this.velocity = direction.normalize().multiply(this.currentSpeed);\n        } else {\n            this.velocity.set(0, this.currentSpeed * 0.2); // Slow drift when in formation\n        }\n    }\n    \n    /**\n     * Zigzag movement pattern\n     * @param {number} dt - Delta time in seconds\n     */\n    updateZigzagMovement(dt) {\n        const zigzagPhase = Math.floor(this.movementTimer * this.frequency) % 2;\n        const direction = zigzagPhase === 0 ? 1 : -1;\n        \n        this.velocity.set(direction * this.currentSpeed * 0.7, this.currentSpeed * 0.8);\n    }\n    \n    /**\n     * Circular movement pattern\n     * @param {number} dt - Delta time in seconds\n     */\n    updateCircleMovement(dt) {\n        const angle = this.movementTimer * this.frequency + this.movementPhase;\n        const centerX = this.formationTarget.x;\n        const centerY = this.formationTarget.y + this.movementTimer * this.currentSpeed * 0.2;\n        \n        const targetX = centerX + Math.cos(angle) * this.amplitude;\n        const targetY = centerY + Math.sin(angle) * this.amplitude * 0.5;\n        \n        const direction = new Vector2(targetX - this.position.x, targetY - this.position.y).normalize();\n        this.velocity = direction.multiply(this.currentSpeed);\n    }\n    \n    /**\n     * Hover and strafe movement (stays on screen longer)\n     * @param {number} dt - Delta time in seconds\n     * @param {Vector2} playerPosition - Player's position\n     */\n    updateHoverMovement(dt, playerPosition) {\n        if (!playerPosition) {\n            this.updateLinearMovement(dt);\n            return;\n        }\n\n        // Hover at a certain distance from player\n        const hoverDistance = 150;\n        const distanceToPlayer = this.position.distance(playerPosition);\n\n        if (distanceToPlayer > hoverDistance + 20) {\n            // Move closer to player\n            const direction = playerPosition.subtract(this.position).normalize();\n            this.velocity = direction.multiply(this.currentSpeed * 0.6);\n        } else if (distanceToPlayer < hoverDistance - 20) {\n            // Move away from player\n            const direction = this.position.subtract(playerPosition).normalize();\n            this.velocity = direction.multiply(this.currentSpeed * 0.6);\n        } else {\n            // Strafe around player\n            const perpendicular = playerPosition.subtract(this.position).perpendicular().normalize();\n            this.velocity = perpendicular.multiply(this.currentSpeed * 0.8);\n        }\n    }\n\n    /**\n     * Update predefined movement patterns\n     * @param {number} dt - Delta time in seconds\n     * @param {Vector2} playerPosition - Player's position\n     */\n    updatePredefinedMovement(dt, playerPosition) {\n        this.predefinedTimer += dt;\n\n        if (!this.predefinedPattern) {\n            this.updateLinearMovement(dt);\n            return;\n        }\n\n        switch (this.predefinedPattern) {\n            case 'straight_down':\n                this.updateStraightDownPattern(dt);\n                break;\n            case 'triangle_left_right':\n                this.updateTriangleLeftRightPattern(dt);\n                break;\n            case 'triangle_zigzag':\n                this.updateTriangleZigzagPattern(dt);\n                break;\n            case 'diamond_sine':\n                this.updateDiamondSinePattern(dt);\n                break;\n            case 'coordinated_sweep_left':\n                this.updateCoordinatedSweepPattern(dt, -1);\n                break;\n            case 'coordinated_sweep_right':\n                this.updateCoordinatedSweepPattern(dt, 1);\n                break;\n            case 'advanced_weave':\n                this.updateAdvancedWeavePattern(dt);\n                break;\n            case 'dive_attack':\n                this.updateDiveAttackPattern(dt, playerPosition);\n                break;\n            case 'spiral_descent':\n                this.updateSpiralDescentPattern(dt);\n                break;\n            case 'pincer_left':\n                this.updatePincerPattern(dt, -1);\n                break;\n            case 'pincer_right':\n                this.updatePincerPattern(dt, 1);\n                break;\n            case 'center_rush':\n                this.updateCenterRushPattern(dt, playerPosition);\n                break;\n            case 'wedge_assault':\n                this.updateWedgeAssaultPattern(dt);\n                break;\n            case 'side_sweep':\n                this.updateSideSweepPattern(dt);\n                break;\n            default:\n                this.updateLinearMovement(dt);\n                break;\n        }\n    }\n    \n    /**\n     * Update attack cooldown timer\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    updateAttackCooldown(deltaTime) {\n        if (this.attackCooldown > 0) {\n            this.attackCooldown -= deltaTime;\n            this.canAttack = this.attackCooldown <= 0;\n        }\n    }\n    \n    /**\n     * Update animation frame\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    updateAnimation(deltaTime) {\n        const frameTime = 1000 / (this.animationSpeed * this.totalFrames);\n        if (this.animationTime * 1000 % frameTime < deltaTime) {\n            this.spriteFrame = (this.spriteFrame + 1) % this.totalFrames;\n        }\n    }\n    \n    /**\n     * Straight down movement pattern\n     * @param {number} dt - Delta time in seconds\n     */\n    updateStraightDownPattern(dt) {\n        this.velocity.set(0, this.currentSpeed);\n    }\n\n    /**\n     * Triangle formation moving left 2 spaces, right 3 spaces, left 1 space\n     * @param {number} dt - Delta time in seconds\n     */\n    updateTriangleLeftRightPattern(dt) {\n        const phaseTime = 2.0; // 2 seconds per phase\n        const currentPhase = Math.floor(this.predefinedTimer / phaseTime) % 3;\n\n        switch (currentPhase) {\n            case 0: // Move left 2 spaces\n                this.velocity.set(-this.currentSpeed * 0.8, this.currentSpeed * 0.6);\n                break;\n            case 1: // Move right 3 spaces\n                this.velocity.set(this.currentSpeed * 1.2, this.currentSpeed * 0.6);\n                break;\n            case 2: // Move left 1 space\n                this.velocity.set(-this.currentSpeed * 0.4, this.currentSpeed * 0.8);\n                break;\n        }\n    }\n\n    /**\n     * Triangle formation with zigzag pattern\n     * @param {number} dt - Delta time in seconds\n     */\n    updateTriangleZigzagPattern(dt) {\n        const zigzagFreq = 1.5;\n        const direction = Math.sin(this.predefinedTimer * zigzagFreq) > 0 ? 1 : -1;\n        this.velocity.set(direction * this.currentSpeed * 0.6, this.currentSpeed * 0.8);\n    }\n\n    /**\n     * Diamond formation with sine wave movement\n     * @param {number} dt - Delta time in seconds\n     */\n    updateDiamondSinePattern(dt) {\n        const sineOffset = Math.sin(this.predefinedTimer * 2) * 60;\n        const targetX = this.initialPosition.x + sineOffset;\n        const xDirection = Math.sign(targetX - this.position.x);\n        this.velocity.set(xDirection * this.currentSpeed * 0.4, this.currentSpeed * 0.9);\n    }\n\n    /**\n     * Coordinated sweep pattern\n     * @param {number} dt - Delta time in seconds\n     * @param {number} direction - Direction multiplier (-1 for left, 1 for right)\n     */\n    updateCoordinatedSweepPattern(dt, direction) {\n        const sweepPhase = Math.sin(this.predefinedTimer * 0.8) * direction;\n        this.velocity.set(sweepPhase * this.currentSpeed * 0.7, this.currentSpeed * 0.7);\n    }\n\n    /**\n     * Advanced weave pattern\n     * @param {number} dt - Delta time in seconds\n     */\n    updateAdvancedWeavePattern(dt) {\n        const weaveX = Math.sin(this.predefinedTimer * 2.5) * this.currentSpeed * 0.8;\n        const weaveY = Math.cos(this.predefinedTimer * 1.2) * this.currentSpeed * 0.3;\n        this.velocity.set(weaveX, this.currentSpeed * 0.6 + weaveY);\n    }\n\n    /**\n     * Dive attack pattern\n     * @param {number} dt - Delta time in seconds\n     * @param {Vector2} playerPosition - Player's position\n     */\n    updateDiveAttackPattern(dt, playerPosition) {\n        if (!playerPosition) {\n            this.updateLinearMovement(dt);\n            return;\n        }\n\n        if (this.predefinedTimer < 1.5) {\n            // Initial approach\n            this.velocity.set(0, this.currentSpeed * 0.5);\n        } else if (this.predefinedTimer < 3.0) {\n            // Dive towards player\n            const direction = playerPosition.subtract(this.position).normalize();\n            this.velocity = direction.multiply(this.currentSpeed * 1.8);\n        } else {\n            // Continue off screen\n            this.velocity.set(0, this.currentSpeed * 1.2);\n        }\n    }\n\n    /**\n     * Spiral descent pattern\n     * @param {number} dt - Delta time in seconds\n     */\n    updateSpiralDescentPattern(dt) {\n        const radius = 80 - (this.predefinedTimer * 10);\n        const angle = this.predefinedTimer * 3;\n        const spiralX = Math.cos(angle) * Math.max(0, radius);\n        const spiralY = Math.sin(angle) * Math.max(0, radius) * 0.3;\n\n        const targetX = this.initialPosition.x + spiralX;\n        const targetY = this.initialPosition.y + this.predefinedTimer * this.currentSpeed * 0.4 + spiralY;\n\n        const direction = new Vector2(targetX - this.position.x, targetY - this.position.y).normalize();\n        this.velocity = direction.multiply(this.currentSpeed);\n    }\n\n    /**\n     * Pincer movement pattern\n     * @param {number} dt - Delta time in seconds\n     * @param {number} side - Side multiplier (-1 for left, 1 for right)\n     */\n    updatePincerPattern(dt, side) {\n        const curveIntensity = Math.sin(this.predefinedTimer * 1.5) * side;\n        this.velocity.set(curveIntensity * this.currentSpeed * 0.9, this.currentSpeed * 0.8);\n    }\n\n    /**\n     * Center rush pattern\n     * @param {number} dt - Delta time in seconds\n     * @param {Vector2} playerPosition - Player's position\n     */\n    updateCenterRushPattern(dt, playerPosition) {\n        if (!playerPosition) {\n            this.velocity.set(0, this.currentSpeed);\n            return;\n        }\n\n        // Rush towards center of screen, then towards player\n        const screenCenter = new Vector2(this.canvasWidth / 2, this.canvasHeight / 2);\n        const target = this.predefinedTimer < 2.0 ? screenCenter : playerPosition;\n        const direction = target.subtract(this.position).normalize();\n        this.velocity = direction.multiply(this.currentSpeed * 1.3);\n    }\n\n    /**\n     * Wedge assault pattern\n     * @param {number} dt - Delta time in seconds\n     */\n    updateWedgeAssaultPattern(dt) {\n        // Accelerating downward movement with slight weaving\n        const weave = Math.sin(this.predefinedTimer * 3) * 20;\n        const acceleration = 1 + (this.predefinedTimer * 0.3);\n        this.velocity.set(weave, this.currentSpeed * acceleration);\n    }\n\n    /**\n     * Side sweep pattern\n     * @param {number} dt - Delta time in seconds\n     */\n    updateSideSweepPattern(dt) {\n        const sweepDirection = this.position.x < this.canvasWidth / 2 ? 1 : -1;\n        this.velocity.set(sweepDirection * this.currentSpeed * 1.1, this.currentSpeed * 0.5);\n    }\n\n    /**\n     * Update death animation\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    updateDeathAnimation(deltaTime) {\n        this.deathAnimationTime += deltaTime;\n\n        // Slow down and fade during death animation\n        this.velocity.multiplyInPlace(0.95);\n\n        if (this.deathAnimationTime >= this.deathAnimationDuration) {\n            this.destroy();\n        }\n    }\n    \n    /**\n     * Check if enemy has moved off screen for cleanup\n     */\n    checkOffScreen() {\n        const margin = 50;\n        if (this.position.x < -margin || \n            this.position.x > this.canvasWidth + margin ||\n            this.position.y > this.canvasHeight + margin) {\n            this.destroy();\n        }\n    }\n    \n    /**\n     * Render the enemy with sprite graphics and animations\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} interpolation - Interpolation factor for smooth rendering\n     */\n    render(ctx, interpolation = 0) {\n        if (!this.visible) return;\n        \n        // Calculate interpolated position for smooth rendering\n        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));\n        \n        ctx.save();\n        ctx.translate(renderPos.x, renderPos.y);\n        ctx.rotate(this.rotation);\n        \n        // Apply death animation effects\n        if (this.isDestroyed) {\n            this.renderDeathAnimation(ctx);\n        } else {\n            this.renderEnemySprite(ctx);\n        }\n        \n        // Draw debug info if enabled\n        if (window.DEBUG_MODE) {\n            this.renderDebugInfo(ctx);\n        }\n        \n        ctx.restore();\n    }\n    \n    /**\n     * Render the enemy sprite based on type and animation frame\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    renderEnemySprite(ctx) {\n        // Apply environmental effects to appearance\n        const effectivenessAlpha = 0.7 + (this.environmentalEffectiveness * 0.3);\n        ctx.globalAlpha = effectivenessAlpha;\n        \n        // Get type-specific colors\n        const colors = this.getTypeColors();\n        \n        // Animate sprite based on frame\n        const pulsePhase = Math.sin(this.animationTime * this.animationSpeed * Math.PI * 2);\n        const scale = 1 + pulsePhase * 0.1;\n        \n        ctx.scale(scale, scale);\n        \n        // Draw enemy based on type\n        switch (this.type) {\n            case ENEMY_TYPES.AIR:\n                this.drawAirEnemy(ctx, colors, pulsePhase);\n                break;\n            case ENEMY_TYPES.WATER:\n                this.drawWaterEnemy(ctx, colors, pulsePhase);\n                break;\n            case ENEMY_TYPES.FIRE:\n                this.drawFireEnemy(ctx, colors, pulsePhase);\n                break;\n            case ENEMY_TYPES.EARTH:\n                this.drawEarthEnemy(ctx, colors, pulsePhase);\n                break;\n            case ENEMY_TYPES.CRYSTAL:\n                this.drawCrystalEnemy(ctx, colors, pulsePhase);\n                break;\n            case ENEMY_TYPES.SHADOW:\n                this.drawShadowEnemy(ctx, colors, pulsePhase);\n                break;\n            default:\n                this.drawDefaultEnemy(ctx, colors, pulsePhase);\n                break;\n        }\n        \n        ctx.globalAlpha = 1.0;\n    }\n    \n    /**\n     * Get type-specific color scheme\n     * @returns {object} Color scheme for the enemy type\n     */\n    getTypeColors() {\n        const colorSchemes = {\n            [ENEMY_TYPES.AIR]: {\n                primary: '#87CEEB',\n                secondary: '#4682B4',\n                accent: '#B0E0E6'\n            },\n            [ENEMY_TYPES.WATER]: {\n                primary: '#4169E1',\n                secondary: '#1E90FF',\n                accent: '#00BFFF'\n            },\n            [ENEMY_TYPES.FIRE]: {\n                primary: '#FF4500',\n                secondary: '#FF6347',\n                accent: '#FFD700'\n            },\n            [ENEMY_TYPES.EARTH]: {\n                primary: '#8B4513',\n                secondary: '#A0522D',\n                accent: '#DEB887'\n            },\n            [ENEMY_TYPES.CRYSTAL]: {\n                primary: '#9370DB',\n                secondary: '#8A2BE2',\n                accent: '#DDA0DD'\n            },\n            [ENEMY_TYPES.SHADOW]: {\n                primary: '#2F2F2F',\n                secondary: '#4A4A4A',\n                accent: '#696969'\n            }\n        };\n        \n        return colorSchemes[this.type] || colorSchemes[ENEMY_TYPES.AIR];\n    }\n    \n    /**\n     * Draw air-type enemy (bird-like)\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {object} colors - Color scheme\n     * @param {number} pulsePhase - Animation pulse phase\n     */\n    drawAirEnemy(ctx, colors, pulsePhase) {\n        // Wing flapping animation\n        const wingFlap = Math.sin(this.animationTime * 8) * 0.3;\n        \n        // Body\n        ctx.fillStyle = colors.primary;\n        ctx.beginPath();\n        ctx.ellipse(0, 0, this.width * 0.3, this.height * 0.4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Wings\n        ctx.fillStyle = colors.secondary;\n        ctx.beginPath();\n        ctx.ellipse(-this.width * 0.4, wingFlap * 5, this.width * 0.3, this.height * 0.2, -0.3, 0, Math.PI * 2);\n        ctx.ellipse(this.width * 0.4, wingFlap * 5, this.width * 0.3, this.height * 0.2, 0.3, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Eyes\n        ctx.fillStyle = colors.accent;\n        ctx.beginPath();\n        ctx.arc(-4, -4, 2, 0, Math.PI * 2);\n        ctx.arc(4, -4, 2, 0, Math.PI * 2);\n        ctx.fill();\n    }\n    \n    /**\n     * Draw water-type enemy (jellyfish-like)\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {object} colors - Color scheme\n     * @param {number} pulsePhase - Animation pulse phase\n     */\n    drawWaterEnemy(ctx, colors, pulsePhase) {\n        // Pulsing bell\n        const bellScale = 1 + pulsePhase * 0.2;\n        \n        // Bell/dome\n        ctx.fillStyle = colors.primary;\n        ctx.beginPath();\n        ctx.ellipse(0, -this.height * 0.2, this.width * 0.4 * bellScale, this.height * 0.3 * bellScale, 0, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Tentacles\n        ctx.strokeStyle = colors.secondary;\n        ctx.lineWidth = 2;\n        for (let i = 0; i < 4; i++) {\n            const angle = (i / 4) * Math.PI * 2;\n            const tentacleWave = Math.sin(this.animationTime * 6 + i) * 3;\n            \n            ctx.beginPath();\n            ctx.moveTo(Math.cos(angle) * 6, this.height * 0.1);\n            ctx.quadraticCurveTo(\n                Math.cos(angle) * 8 + tentacleWave,\n                this.height * 0.3,\n                Math.cos(angle) * 4 + tentacleWave * 2,\n                this.height * 0.4\n            );\n            ctx.stroke();\n        }\n        \n        // Inner glow\n        ctx.fillStyle = colors.accent;\n        ctx.beginPath();\n        ctx.ellipse(0, -this.height * 0.2, this.width * 0.2, this.height * 0.15, 0, 0, Math.PI * 2);\n        ctx.fill();\n    }\n    \n    /**\n     * Draw fire-type enemy (flame-like)\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {object} colors - Color scheme\n     * @param {number} pulsePhase - Animation pulse phase\n     */\n    drawFireEnemy(ctx, colors, pulsePhase) {\n        // Flickering flame effect\n        const flicker1 = Math.sin(this.animationTime * 12) * 0.2;\n        const flicker2 = Math.sin(this.animationTime * 15 + 1) * 0.15;\n        \n        // Core flame\n        ctx.fillStyle = colors.accent;\n        ctx.beginPath();\n        ctx.ellipse(0, this.height * 0.1, this.width * 0.3, this.height * 0.4, 0, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Outer flame\n        ctx.fillStyle = colors.primary;\n        ctx.beginPath();\n        ctx.moveTo(0, -this.height * 0.4 + flicker1 * 5);\n        ctx.quadraticCurveTo(-this.width * 0.3 + flicker2 * 3, 0, -this.width * 0.2, this.height * 0.3);\n        ctx.quadraticCurveTo(0, this.height * 0.4, this.width * 0.2, this.height * 0.3);\n        ctx.quadraticCurveTo(this.width * 0.3 + flicker1 * 3, 0, 0, -this.height * 0.4 + flicker1 * 5);\n        ctx.fill();\n        \n        // Inner flame\n        ctx.fillStyle = colors.secondary;\n        ctx.beginPath();\n        ctx.ellipse(0, 0, this.width * 0.25 + flicker2 * 2, this.height * 0.3 + flicker1 * 3, 0, 0, Math.PI * 2);\n        ctx.fill();\n    }\n    \n    /**\n     * Draw earth-type enemy (rock-like)\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {object} colors - Color scheme\n     * @param {number} pulsePhase - Animation pulse phase\n     */\n    drawEarthEnemy(ctx, colors, pulsePhase) {\n        // Rocky, angular shape\n        ctx.fillStyle = colors.primary;\n        ctx.strokeStyle = colors.secondary;\n        ctx.lineWidth = 2;\n        \n        ctx.beginPath();\n        ctx.moveTo(0, -this.height * 0.4);\n        ctx.lineTo(this.width * 0.3, -this.height * 0.2);\n        ctx.lineTo(this.width * 0.4, this.height * 0.1);\n        ctx.lineTo(this.width * 0.2, this.height * 0.4);\n        ctx.lineTo(-this.width * 0.2, this.height * 0.4);\n        ctx.lineTo(-this.width * 0.4, this.height * 0.1);\n        ctx.lineTo(-this.width * 0.3, -this.height * 0.2);\n        ctx.closePath();\n        ctx.fill();\n        ctx.stroke();\n        \n        // Rock texture details\n        ctx.fillStyle = colors.accent;\n        ctx.beginPath();\n        ctx.arc(-this.width * 0.1, -this.height * 0.1, 3, 0, Math.PI * 2);\n        ctx.arc(this.width * 0.15, this.height * 0.1, 2, 0, Math.PI * 2);\n        ctx.arc(-this.width * 0.2, this.height * 0.2, 2, 0, Math.PI * 2);\n        ctx.fill();\n    }\n    \n    /**\n     * Draw crystal-type enemy (crystalline)\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {object} colors - Color scheme\n     * @param {number} pulsePhase - Animation pulse phase\n     */\n    drawCrystalEnemy(ctx, colors, pulsePhase) {\n        // Glowing crystal with energy pulses\n        const glowIntensity = 0.7 + pulsePhase * 0.3;\n        \n        // Outer glow\n        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.width * 0.6);\n        gradient.addColorStop(0, colors.accent + '80');\n        gradient.addColorStop(1, 'transparent');\n        ctx.fillStyle = gradient;\n        ctx.beginPath();\n        ctx.arc(0, 0, this.width * 0.6 * glowIntensity, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Crystal facets\n        ctx.fillStyle = colors.primary;\n        ctx.strokeStyle = colors.secondary;\n        ctx.lineWidth = 1;\n        \n        // Main crystal shape\n        ctx.beginPath();\n        ctx.moveTo(0, -this.height * 0.4);\n        ctx.lineTo(this.width * 0.2, -this.height * 0.2);\n        ctx.lineTo(this.width * 0.3, 0);\n        ctx.lineTo(this.width * 0.2, this.height * 0.3);\n        ctx.lineTo(0, this.height * 0.4);\n        ctx.lineTo(-this.width * 0.2, this.height * 0.3);\n        ctx.lineTo(-this.width * 0.3, 0);\n        ctx.lineTo(-this.width * 0.2, -this.height * 0.2);\n        ctx.closePath();\n        ctx.fill();\n        ctx.stroke();\n        \n        // Inner facet lines\n        ctx.strokeStyle = colors.accent;\n        ctx.beginPath();\n        ctx.moveTo(0, -this.height * 0.4);\n        ctx.lineTo(0, this.height * 0.4);\n        ctx.moveTo(-this.width * 0.3, 0);\n        ctx.lineTo(this.width * 0.3, 0);\n        ctx.stroke();\n    }\n    \n    /**\n     * Draw shadow-type enemy (dark, ethereal)\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {object} colors - Color scheme\n     * @param {number} pulsePhase - Animation pulse phase\n     */\n    drawShadowEnemy(ctx, colors, pulsePhase) {\n        // Wispy, ethereal appearance\n        const wispiness = Math.sin(this.animationTime * 4) * 0.3;\n        \n        // Shadow body with transparency\n        ctx.globalAlpha = 0.8 + pulsePhase * 0.2;\n        \n        // Main shadow form\n        ctx.fillStyle = colors.primary;\n        ctx.beginPath();\n        ctx.ellipse(0, 0, this.width * 0.4 + wispiness * 5, this.height * 0.4 + wispiness * 3, 0, 0, Math.PI * 2);\n        ctx.fill();\n        \n        // Wispy tendrils\n        ctx.strokeStyle = colors.secondary;\n        ctx.lineWidth = 3;\n        ctx.lineCap = 'round';\n        \n        for (let i = 0; i < 6; i++) {\n            const angle = (i / 6) * Math.PI * 2;\n            const tendrilWave = Math.sin(this.animationTime * 5 + i) * 8;\n            const startX = Math.cos(angle) * this.width * 0.3;\n            const startY = Math.sin(angle) * this.height * 0.3;\n            \n            ctx.beginPath();\n            ctx.moveTo(startX, startY);\n            ctx.lineTo(\n                startX + Math.cos(angle) * (10 + tendrilWave),\n                startY + Math.sin(angle) * (10 + tendrilWave)\n            );\n            ctx.stroke();\n        }\n        \n        // Glowing eyes\n        ctx.fillStyle = colors.accent;\n        ctx.beginPath();\n        ctx.arc(-6, -6, 2, 0, Math.PI * 2);\n        ctx.arc(6, -6, 2, 0, Math.PI * 2);\n        ctx.fill();\n    }\n    \n    /**\n     * Draw default enemy (fallback)\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {object} colors - Color scheme\n     * @param {number} pulsePhase - Animation pulse phase\n     */\n    drawDefaultEnemy(ctx, colors, pulsePhase) {\n        // Simple circular enemy\n        ctx.fillStyle = colors.primary;\n        ctx.strokeStyle = colors.secondary;\n        ctx.lineWidth = 2;\n        \n        ctx.beginPath();\n        ctx.arc(0, 0, this.width * 0.4, 0, Math.PI * 2);\n        ctx.fill();\n        ctx.stroke();\n        \n        // Simple face\n        ctx.fillStyle = colors.accent;\n        ctx.beginPath();\n        ctx.arc(-4, -4, 2, 0, Math.PI * 2);\n        ctx.arc(4, -4, 2, 0, Math.PI * 2);\n        ctx.fill();\n    }\n    \n    /**\n     * Render death animation effects\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    renderDeathAnimation(ctx) {\n        const progress = this.deathAnimationTime / this.deathAnimationDuration;\n        const alpha = 1 - progress;\n        const scale = 1 + progress * 2;\n        \n        ctx.globalAlpha = alpha;\n        ctx.scale(scale, scale);\n        \n        // Explosion effect\n        const colors = this.getTypeColors();\n        const particleCount = 8;\n        \n        for (let i = 0; i < particleCount; i++) {\n            const angle = (i / particleCount) * Math.PI * 2;\n            const distance = progress * 30;\n            const x = Math.cos(angle) * distance;\n            const y = Math.sin(angle) * distance;\n            \n            ctx.fillStyle = colors.accent;\n            ctx.beginPath();\n            ctx.arc(x, y, 3 * (1 - progress), 0, Math.PI * 2);\n            ctx.fill();\n        }\n        \n        ctx.globalAlpha = 1.0;\n    }\n    \n    /**\n     * Render debug information\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    renderDebugInfo(ctx) {\n        // Collision circle\n        ctx.strokeStyle = '#FF0000';\n        ctx.lineWidth = 1;\n        ctx.beginPath();\n        ctx.arc(0, 0, this.collisionRadius, 0, Math.PI * 2);\n        ctx.stroke();\n        \n        // Velocity vector\n        if (this.velocity.magnitude() > 1) {\n            ctx.strokeStyle = '#00FF00';\n            ctx.lineWidth = 2;\n            ctx.beginPath();\n            ctx.moveTo(0, 0);\n            const velocityScale = 0.1;\n            ctx.lineTo(this.velocity.x * velocityScale, this.velocity.y * velocityScale);\n            ctx.stroke();\n        }\n        \n        // Health bar\n        const barWidth = this.width;\n        const barHeight = 4;\n        const healthPercent = this.health / this.maxHealth;\n        \n        ctx.fillStyle = '#FF0000';\n        ctx.fillRect(-barWidth / 2, -this.height / 2 - 10, barWidth, barHeight);\n        ctx.fillStyle = '#00FF00';\n        ctx.fillRect(-barWidth / 2, -this.height / 2 - 10, barWidth * healthPercent, barHeight);\n        \n        // Type and effectiveness text\n        ctx.fillStyle = '#FFFFFF';\n        ctx.font = '8px Arial';\n        ctx.textAlign = 'center';\n        ctx.fillText(`${this.type} (${this.environmentalEffectiveness.toFixed(1)}x)`, 0, this.height / 2 + 15);\n    }\n    \n    /**\n     * Set movement pattern\n     * @param {string} pattern - Movement pattern name\n     * @param {object} options - Pattern-specific options\n     */\n    setMovementPattern(pattern, options = {}) {\n        this.movementPattern = pattern;\n        this.movementTimer = 0;\n        \n        // Apply pattern-specific settings\n        switch (pattern) {\n            case 'sine':\n                this.amplitude = options.amplitude || 50;\n                this.frequency = options.frequency || 2;\n                break;\n            case 'spiral':\n                this.amplitude = options.amplitude || 80;\n                this.frequency = options.frequency || 3;\n                break;\n            case 'formation':\n                this.isInFormation = true;\n                this.formationOffset = options.offset || new Vector2(0, 0);\n                break;\n            case 'zigzag':\n                this.frequency = options.frequency || 1.5;\n                break;\n            case 'circle':\n                this.amplitude = options.radius || 60;\n                this.frequency = options.frequency || 2;\n                break;\n            case 'predefined':\n                this.predefinedPattern = options.predefinedPattern || 'straight_down';\n                this.predefinedState = 0;\n                this.predefinedTimer = 0;\n                this.initialPosition = this.position.clone();\n                break;\n        }\n        \n        console.log(`Enemy ${this.id} movement pattern set to: ${pattern}`);\n    }\n    \n    /**\n     * Set formation target and offset\n     * @param {Vector2} target - Formation center target\n     * @param {Vector2} offset - Offset from formation center\n     */\n    setFormationTarget(target, offset = new Vector2(0, 0)) {\n        this.formationTarget = target.clone();\n        this.formationOffset = offset.clone();\n        this.isInFormation = true;\n    }\n    \n    /**\n     * Apply environmental effectiveness modifier\n     * @param {string} environment - Current environment type\n     * @param {number} effectiveness - Effectiveness multiplier (0.1 to 2.0)\n     */\n    applyEnvironmentalEffect(environment, effectiveness = 1.0) {\n        this.currentEnvironment = environment;\n        this.environmentalEffectiveness = GameMath.clamp(effectiveness, 0.1, 2.0);\n        \n        // Update speed and attack rate based on effectiveness\n        this.currentSpeed = this.baseSpeed * this.environmentalEffectiveness;\n        this.currentAttackRate = this.baseAttackRate * this.environmentalEffectiveness;\n        \n        // Removed spam logging - only log when effectiveness actually changes\n        if (this.environmentalEffectiveness !== effectiveness) {\n            console.log(`Enemy ${this.id} environmental effectiveness changed: ${this.environmentalEffectiveness} -> ${effectiveness} in ${environment}`);\n        }\n    }\n    \n    /**\n     * Take damage and handle destruction\n     * @param {number} damage - Amount of damage to take\n     * @returns {object} Result object with damage taken, health remaining, and destroyed status\n     */\n    takeDamage(damage) {\n        if (this.isDestroyed) {\n            return {\n                damageTaken: 0,\n                health: this.health,\n                destroyed: true,\n                scoreValue: 0\n            };\n        }\n        \n        const actualDamage = Math.min(damage, this.health);\n        this.health -= actualDamage;\n        \n        console.log(`Enemy ${this.id} took ${actualDamage} damage. Health: ${this.health}/${this.maxHealth}`);\n        \n        if (this.health <= 0) {\n            this.startDeathAnimation();\n            return {\n                damageTaken: actualDamage,\n                health: 0,\n                destroyed: true,\n                scoreValue: this.scoreValue\n            };\n        }\n        \n        return {\n            damageTaken: actualDamage,\n            health: this.health,\n            destroyed: false,\n            scoreValue: 0\n        };\n    }\n    \n    /**\n     * Start death animation sequence\n     */\n    startDeathAnimation() {\n        this.isDestroyed = true;\n        this.deathAnimationTime = 0;\n        this.canAttack = false;\n        \n        console.log(`Enemy ${this.id} destroyed! Score value: ${this.scoreValue}`);\n    }\n    \n    /**\n     * Check if enemy can attack\n     * @param {Vector2} playerPosition - Player's position\n     * @returns {boolean} True if enemy can attack\n     */\n    canAttackPlayer(playerPosition) {\n        if (!this.canAttack || this.isDestroyed || !playerPosition) {\n            return false;\n        }\n        \n        // Check if player is in attack range (varies by enemy type)\n        const attackRange = this.getAttackRange();\n        const distanceToPlayer = this.position.distance(playerPosition);\n        \n        return distanceToPlayer <= attackRange;\n    }\n    \n    /**\n     * Get attack range based on enemy type\n     * @returns {number} Attack range in pixels\n     */\n    getAttackRange() {\n        const rangeMap = {\n            [ENEMY_TYPES.AIR]: 200,\n            [ENEMY_TYPES.WATER]: 150,\n            [ENEMY_TYPES.FIRE]: 180,\n            [ENEMY_TYPES.EARTH]: 120,\n            [ENEMY_TYPES.CRYSTAL]: 250,\n            [ENEMY_TYPES.SHADOW]: 300\n        };\n        return rangeMap[this.type] || 200;\n    }\n    \n    /**\n     * Trigger attack (to be called by EnemyManager)\n     * @param {Vector2} playerPosition - Player's position for targeting\n     * @returns {object|null} Attack data or null if can't attack\n     */\n    attack(playerPosition) {\n        if (!this.canAttackPlayer(playerPosition)) {\n            return null;\n        }\n        \n        // Set attack cooldown\n        this.attackCooldown = (1000 / this.currentAttackRate) + GameMath.random(-200, 200);\n        this.canAttack = false;\n        \n        // Calculate attack direction\n        const direction = playerPosition.subtract(this.position).normalize();\n        \n        return {\n            position: this.position.clone(),\n            direction: direction,\n            type: this.type,\n            damage: this.getAttackDamage()\n        };\n    }\n    \n    /**\n     * Get attack damage based on enemy type\n     * @returns {number} Attack damage\n     */\n    getAttackDamage() {\n        const damageMap = {\n            [ENEMY_TYPES.AIR]: 15,\n            [ENEMY_TYPES.WATER]: 20,\n            [ENEMY_TYPES.FIRE]: 25,\n            [ENEMY_TYPES.EARTH]: 30,\n            [ENEMY_TYPES.CRYSTAL]: 22,\n            [ENEMY_TYPES.SHADOW]: 18\n        };\n        return damageMap[this.type] || 20;\n    }\n    \n    /**\n     * Get current status for debugging\n     * @returns {object} Status object\n     */\n    getStatus() {\n        return {\n            id: this.id,\n            type: this.type,\n            position: this.position.clone(),\n            health: this.health,\n            maxHealth: this.maxHealth,\n            movementPattern: this.movementPattern,\n            environmentalEffectiveness: this.environmentalEffectiveness,\n            currentEnvironment: this.currentEnvironment,\n            canAttack: this.canAttack,\n            isDestroyed: this.isDestroyed,\n            scoreValue: this.scoreValue\n        };\n    }\n}", "import { Enemy } from '../entities/Enemy.js';\nimport { Vector2 } from '../utils/Vector2.js';\nimport { GameMath } from '../utils/GameMath.js';\nimport { ENEMY_TYPES, GAME_CONFIG } from '../config/gameConfig.js';\n// import { EnemyProjectileSystem } from '../systems/EnemyProjectileSystem.js'; // Disabled - enemies don't fire projectiles\n\n/**\n * EnemyManager handles enemy spawning, wave management, and lifecycle\n * Manages collision detection between enemies and player/projectiles\n */\nexport class EnemyManager {\n    constructor(canvasWidth = GAME_CONFIG.CANVAS_WIDTH, canvasHeight = GAME_CONFIG.CANVAS_HEIGHT, gameObjectManager = null) {\n        this.canvasWidth = canvasWidth;\n        this.canvasHeight = canvasHeight;\n        this.gameObjectManager = gameObjectManager;\n\n        // Enemy tracking\n        this.activeEnemies = [];\n        this.enemyPool = []; // Object pool for performance\n        this.maxEnemies = 50;\n\n        // Wave management\n        this.currentWave = 0;\n        this.waveInProgress = false;\n        this.waveStartTime = 0;\n        this.waveConfig = null;\n        this.enemiesSpawnedInWave = 0;\n        this.enemiesKilledInWave = 0;\n        this.enemiesEscapedInWave = 0;\n        this.patternSpawnCounts = {}; // Track how many enemies spawned per pattern\n\n        // Spawn timing\n        this.lastSpawnTime = 0;\n        this.spawnCooldown = 1000; // Base spawn cooldown in milliseconds\n        this.spawnTimer = 0;\n\n        // Formation management\n        this.formations = [];\n        this.formationSpawnQueue = [];\n\n        // Environmental effects\n        this.currentEnvironment = 'space';\n        this.environmentalEffects = this.getDefaultEnvironmentalEffects();\n\n        // Statistics\n        this.totalEnemiesSpawned = 0;\n        this.totalEnemiesKilled = 0;\n        this.totalScore = 0;\n\n        // Collision detection optimization\n        this.collisionGrid = null;\n        this.gridSize = 64;\n        this.gridWidth = Math.ceil(canvasWidth / this.gridSize);\n        this.gridHeight = Math.ceil(canvasHeight / this.gridSize);\n\n        // Enemy projectile system disabled - enemies don't fire projectiles in this game\n        // this.projectileSystem = new EnemyProjectileSystem(canvasWidth, canvasHeight, gameObjectManager);\n\n        console.log('EnemyManager initialized');\n    }\n\n    /**\n     * Update all enemies and wave management\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     * @param {Vector2} playerPosition - Player's current position\n     */\n    update(deltaTime, playerPosition = null) {\n        // Update spawn timer\n        this.spawnTimer += deltaTime;\n\n        // Update wave management\n        this.updateWaveManagement(deltaTime);\n\n        // Spawn enemies based on current wave\n        this.updateEnemySpawning(deltaTime, playerPosition);\n\n        // Update all active enemies\n        this.updateActiveEnemies(deltaTime, playerPosition);\n\n        // Clean up destroyed enemies\n        this.cleanupDestroyedEnemies();\n\n        // Update formations\n        this.updateFormations(deltaTime);\n\n        // Enemy projectile system disabled - enemies don't fire projectiles\n        // this.projectileSystem.update(deltaTime, playerPosition);\n\n        // Enemy attacks disabled - enemies don't fire projectiles\n        // this.updateEnemyAttacks(deltaTime, playerPosition);\n\n        // Check wave completion\n        this.checkWaveCompletion();\n    }\n\n    /**\n     * Update wave management logic\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    updateWaveManagement(deltaTime) {\n        if (!this.waveInProgress && this.activeEnemies.length === 0) {\n            // Start next wave if no enemies are active\n            this.startNextWave();\n        }\n\n        if (this.waveInProgress) {\n            this.waveStartTime += deltaTime;\n        }\n    }\n\n    /**\n     * Update enemy spawning based on current wave configuration\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     * @param {Vector2} playerPosition - Player's current position\n     */\n    updateEnemySpawning(deltaTime, playerPosition) {\n        if (!this.waveInProgress || !this.waveConfig) return;\n\n        // Check if we should spawn more enemies\n        if (this.spawnTimer >= this.spawnCooldown &&\n            this.activeEnemies.length < this.maxEnemies &&\n            this.enemiesSpawnedInWave < this.waveConfig.totalEnemies) {\n\n            this.spawnEnemyFromWave();\n            this.spawnTimer = 0;\n        }\n\n        // Process formation spawn queue\n        this.processFormationSpawnQueue(deltaTime);\n    }\n\n    /**\n     * Update all active enemies\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     * @param {Vector2} playerPosition - Player's current position\n     */\n    updateActiveEnemies(deltaTime, playerPosition) {\n        for (let i = this.activeEnemies.length - 1; i >= 0; i--) {\n            const enemy = this.activeEnemies[i];\n\n            if (enemy.active) {\n                enemy.update(deltaTime, playerPosition);\n\n                // Apply environmental effects\n                this.applyEnvironmentalEffects(enemy);\n            }\n        }\n    }\n\n    /**\n     * Clean up destroyed or inactive enemies\n     */\n    cleanupDestroyedEnemies() {\n        for (let i = this.activeEnemies.length - 1; i >= 0; i--) {\n            const enemy = this.activeEnemies[i];\n\n            if (enemy.destroyed || !enemy.active) {\n                // Track whether enemy was killed or escaped\n                if (enemy.isDestroyed) {\n                    // Enemy was killed by player\n                    this.enemiesKilledInWave++;\n                    this.totalEnemiesKilled++;\n                    this.totalScore += enemy.scoreValue;\n                    console.log(`Enemy ${enemy.id} killed. Wave progress: ${this.enemiesKilledInWave + this.enemiesEscapedInWave}/${this.waveConfig?.totalEnemies || 0}`);\n                } else if (enemy.destroyed) {\n                    // Enemy escaped off-screen\n                    this.enemiesEscapedInWave++;\n                    console.log(`Enemy ${enemy.id} escaped. Wave progress: ${this.enemiesKilledInWave + this.enemiesEscapedInWave}/${this.waveConfig?.totalEnemies || 0}`);\n\n                    // Notify game about enemy escape\n                    this.onEnemyEscaped(enemy);\n                }\n\n                // Return enemy to pool\n                this.returnEnemyToPool(enemy);\n                this.activeEnemies.splice(i, 1);\n            }\n        }\n    }\n\n    /**\n     * Start the next wave\n     */\n    startNextWave() {\n        this.currentWave++;\n        this.waveInProgress = true;\n        this.waveStartTime = 0;\n        this.enemiesSpawnedInWave = 0;\n        this.enemiesKilledInWave = 0;\n        this.enemiesEscapedInWave = 0;\n        this.patternSpawnCounts = {}; // Reset pattern tracking\n\n        // Generate wave configuration\n        this.waveConfig = this.generateWaveConfig(this.currentWave);\n\n        // Initialize pattern spawn counts\n        if (this.waveConfig.spawnPatterns) {\n            for (let i = 0; i < this.waveConfig.spawnPatterns.length; i++) {\n                this.patternSpawnCounts[i] = 0;\n            }\n        }\n\n        // Set spawn cooldown based on wave difficulty\n        this.spawnCooldown = Math.max(200, 1500 - (this.currentWave * 50));\n\n        console.log(`Starting wave ${this.currentWave}:`, this.waveConfig);\n    }\n\n    /**\n     * Generate wave configuration based on wave number\n     * @param {number} waveNumber - Current wave number\n     * @returns {object} Wave configuration\n     */\n    generateWaveConfig(waveNumber) {\n        const baseEnemyCount = 5;\n        const enemyCountIncrease = Math.floor(waveNumber / 2);\n        const totalEnemies = baseEnemyCount + enemyCountIncrease;\n\n        // Determine enemy type distribution based on wave\n        const enemyTypes = this.getWaveEnemyTypes(waveNumber);\n\n        // Create spawn patterns\n        const spawnPatterns = this.generateSpawnPatterns(waveNumber, totalEnemies);\n\n        return {\n            waveNumber: waveNumber,\n            totalEnemies: totalEnemies,\n            enemyTypes: enemyTypes,\n            spawnPatterns: spawnPatterns,\n            difficulty: Math.min(10, Math.floor(waveNumber / 3) + 1),\n            hasFormation: waveNumber % 4 === 0, // Every 4th wave has formation\n            hasBoss: waveNumber % 10 === 0 // Every 10th wave has boss\n        };\n    }\n\n    /**\n     * Get enemy types for a specific wave\n     * @param {number} waveNumber - Wave number\n     * @returns {Array} Array of enemy types with weights\n     */\n    getWaveEnemyTypes(waveNumber) {\n        const types = [];\n\n        // Early waves - mostly air enemies\n        if (waveNumber <= 3) {\n            types.push({ type: ENEMY_TYPES.AIR, weight: 0.8 });\n            types.push({ type: ENEMY_TYPES.WATER, weight: 0.2 });\n        }\n        // Mid waves - introduce more variety\n        else if (waveNumber <= 7) {\n            types.push({ type: ENEMY_TYPES.AIR, weight: 0.4 });\n            types.push({ type: ENEMY_TYPES.WATER, weight: 0.3 });\n            types.push({ type: ENEMY_TYPES.FIRE, weight: 0.2 });\n            types.push({ type: ENEMY_TYPES.EARTH, weight: 0.1 });\n        }\n        // Later waves - all types\n        else {\n            types.push({ type: ENEMY_TYPES.AIR, weight: 0.2 });\n            types.push({ type: ENEMY_TYPES.WATER, weight: 0.2 });\n            types.push({ type: ENEMY_TYPES.FIRE, weight: 0.2 });\n            types.push({ type: ENEMY_TYPES.EARTH, weight: 0.15 });\n            types.push({ type: ENEMY_TYPES.CRYSTAL, weight: 0.15 });\n            types.push({ type: ENEMY_TYPES.SHADOW, weight: 0.1 });\n        }\n\n        return types;\n    }\n\n    /**\n     * Generate spawn patterns for the wave with predefined patterns\n     * @param {number} waveNumber - Wave number\n     * @param {number} totalEnemies - Total enemies in wave\n     * @returns {Array} Array of spawn patterns\n     */\n    generateSpawnPatterns(waveNumber, totalEnemies) {\n        return this.getPredefinedWavePattern(waveNumber, totalEnemies);\n    }\n\n    /**\n     * Get predefined movement pattern for specific wave\n     * @param {number} waveNumber - Wave number\n     * @param {number} totalEnemies - Total enemies in wave\n     * @returns {Array} Array of spawn patterns\n     */\n    getPredefinedWavePattern(waveNumber, totalEnemies) {\n        const patterns = [];\n\n        switch (waveNumber) {\n            case 1:\n                // Wave 1: Simple line formation moving straight down\n                patterns.push({\n                    type: 'formation',\n                    count: totalEnemies,\n                    formation: 'line',\n                    movementPattern: 'predefined',\n                    predefinedPattern: 'straight_down',\n                    spacing: 1500\n                });\n                break;\n\n            case 2:\n                // Wave 2: V-formation moving left 2 spaces, right 3 spaces, left 1 space\n                patterns.push({\n                    type: 'formation',\n                    count: totalEnemies,\n                    formation: 'v-formation',\n                    movementPattern: 'predefined',\n                    predefinedPattern: 'triangle_left_right',\n                    spacing: 1200\n                });\n                break;\n\n            case 3:\n                // Wave 3: Triangle formation with zigzag pattern\n                patterns.push({\n                    type: 'formation',\n                    count: Math.floor(totalEnemies * 0.7),\n                    formation: 'triangle',\n                    movementPattern: 'predefined',\n                    predefinedPattern: 'triangle_zigzag'\n                });\n                patterns.push({\n                    type: 'scattered',\n                    count: Math.ceil(totalEnemies * 0.3),\n                    movementPattern: 'predefined',\n                    predefinedPattern: 'side_sweep'\n                });\n                break;\n\n            case 4:\n                // Wave 4: Diamond formation with sine wave movement\n                patterns.push({\n                    type: 'formation',\n                    count: totalEnemies,\n                    formation: 'diamond',\n                    movementPattern: 'predefined',\n                    predefinedPattern: 'diamond_sine'\n                });\n                break;\n\n            case 5:\n                // Wave 5: Multiple small formations with coordinated movement\n                patterns.push({\n                    type: 'formation',\n                    count: Math.floor(totalEnemies * 0.5),\n                    formation: 'line',\n                    movementPattern: 'predefined',\n                    predefinedPattern: 'coordinated_sweep_left'\n                });\n                patterns.push({\n                    type: 'formation',\n                    count: Math.ceil(totalEnemies * 0.5),\n                    formation: 'line',\n                    movementPattern: 'predefined',\n                    predefinedPattern: 'coordinated_sweep_right'\n                });\n                break;\n\n            default:\n                // For waves 6+, cycle through complex patterns\n                const patternIndex = (waveNumber - 6) % 4;\n                switch (patternIndex) {\n                    case 0:\n                        patterns.push({\n                            type: 'formation',\n                            count: Math.floor(totalEnemies * 0.6),\n                            formation: 'v-formation',\n                            movementPattern: 'predefined',\n                            predefinedPattern: 'advanced_weave'\n                        });\n                        patterns.push({\n                            type: 'dive',\n                            count: Math.ceil(totalEnemies * 0.4),\n                            movementPattern: 'predefined',\n                            predefinedPattern: 'dive_attack'\n                        });\n                        break;\n                    case 1:\n                        patterns.push({\n                            type: 'formation',\n                            count: totalEnemies,\n                            formation: 'circle',\n                            movementPattern: 'predefined',\n                            predefinedPattern: 'spiral_descent'\n                        });\n                        break;\n                    case 2:\n                        patterns.push({\n                            type: 'formation',\n                            count: Math.floor(totalEnemies * 0.4),\n                            formation: 'line',\n                            movementPattern: 'predefined',\n                            predefinedPattern: 'pincer_left'\n                        });\n                        patterns.push({\n                            type: 'formation',\n                            count: Math.floor(totalEnemies * 0.4),\n                            formation: 'line',\n                            movementPattern: 'predefined',\n                            predefinedPattern: 'pincer_right'\n                        });\n                        patterns.push({\n                            type: 'scattered',\n                            count: Math.ceil(totalEnemies * 0.2),\n                            movementPattern: 'predefined',\n                            predefinedPattern: 'center_rush'\n                        });\n                        break;\n                    case 3:\n                        patterns.push({\n                            type: 'formation',\n                            count: totalEnemies,\n                            formation: 'wedge',\n                            movementPattern: 'predefined',\n                            predefinedPattern: 'wedge_assault'\n                        });\n                        break;\n                }\n                break;\n        }\n\n        return patterns;\n    }\n\n    /**\n     * Spawn an enemy from the current wave configuration\n     */\n    spawnEnemyFromWave() {\n        if (!this.waveConfig || this.enemiesSpawnedInWave >= this.waveConfig.totalEnemies) {\n            return;\n        }\n\n        // Select enemy type based on wave configuration\n        const enemyType = this.selectEnemyType(this.waveConfig.enemyTypes);\n\n        // Select spawn pattern\n        const patternData = this.selectSpawnPattern(this.waveConfig.spawnPatterns);\n        const pattern = patternData.pattern;\n\n        // Create spawn position\n        const spawnPos = this.generateSpawnPosition(pattern);\n\n        // Spawn the enemy\n        const enemy = this.spawnEnemy(spawnPos.x, spawnPos.y, enemyType);\n\n        if (enemy) {\n            // Apply movement pattern\n            const patternOptions = {\n                amplitude: GameMath.random(30, 80),\n                frequency: GameMath.random(1, 3)\n            };\n\n            // Add predefined pattern if specified\n            if (pattern.predefinedPattern) {\n                patternOptions.predefinedPattern = pattern.predefinedPattern;\n            }\n\n            enemy.setMovementPattern(pattern.movementPattern, patternOptions);\n\n            // Handle formation spawning\n            if (pattern.type === 'formation') {\n                this.addEnemyToFormation(enemy, pattern);\n            }\n\n            this.enemiesSpawnedInWave++;\n        }\n    }\n\n    /**\n     * Select enemy type based on weighted distribution\n     * @param {Array} enemyTypes - Array of enemy types with weights\n     * @returns {string} Selected enemy type\n     */\n    selectEnemyType(enemyTypes) {\n        const random = Math.random();\n        let cumulativeWeight = 0;\n\n        for (const typeData of enemyTypes) {\n            cumulativeWeight += typeData.weight;\n            if (random <= cumulativeWeight) {\n                return typeData.type;\n            }\n        }\n\n        // Fallback to first type\n        return enemyTypes[0].type;\n    }\n\n    /**\n     * Select spawn pattern from available patterns\n     * @param {Array} patterns - Available spawn patterns\n     * @returns {object} Selected spawn pattern with index\n     */\n    selectSpawnPattern(patterns) {\n        // Find patterns that haven't reached their spawn limit\n        const availablePatterns = [];\n\n        for (let i = 0; i < patterns.length; i++) {\n            const pattern = patterns[i];\n            const spawnedCount = this.patternSpawnCounts[i] || 0;\n\n            if (spawnedCount < pattern.count) {\n                availablePatterns.push({ pattern, index: i });\n            }\n        }\n\n        if (availablePatterns.length === 0) {\n            // All patterns exhausted, shouldn't happen but fallback\n            console.warn('All spawn patterns exhausted but still trying to spawn enemies');\n            return { pattern: patterns[0], index: 0 };\n        }\n\n        // Select randomly from available patterns\n        const selected = availablePatterns[Math.floor(Math.random() * availablePatterns.length)];\n\n        // Increment the spawn count for this pattern\n        this.patternSpawnCounts[selected.index]++;\n\n        return selected;\n    }\n\n    /**\n     * Generate spawn position based on pattern\n     * @param {object} pattern - Spawn pattern configuration\n     * @returns {Vector2} Spawn position\n     */\n    generateSpawnPosition(pattern) {\n        switch (pattern.type) {\n            case 'linear':\n                return new Vector2(\n                    GameMath.random(50, this.canvasWidth - 50),\n                    -30\n                );\n\n            case 'formation':\n                return new Vector2(\n                    this.canvasWidth / 2,\n                    -50\n                );\n\n            case 'scattered':\n                return new Vector2(\n                    GameMath.random(30, this.canvasWidth - 30),\n                    GameMath.random(-50, -20)\n                );\n\n            case 'dive':\n                // Spawn from sides for dive attacks\n                const side = Math.random() < 0.5 ? 'left' : 'right';\n                return new Vector2(\n                    side === 'left' ? -30 : this.canvasWidth + 30,\n                    GameMath.random(50, 150)\n                );\n\n            default:\n                return new Vector2(\n                    GameMath.random(50, this.canvasWidth - 50),\n                    -30\n                );\n        }\n    }\n\n    /**\n     * Spawn a new enemy at the specified position\n     * @param {number} x - X position\n     * @param {number} y - Y position\n     * @param {string} type - Enemy type\n     * @returns {Enemy|null} Spawned enemy or null if failed\n     */\n    spawnEnemy(x, y, type = ENEMY_TYPES.AIR) {\n        // Try to get enemy from pool first\n        let enemy = this.getEnemyFromPool();\n\n        if (!enemy) {\n            // Create new enemy if pool is empty\n            enemy = new Enemy(x, y, type, this.canvasWidth, this.canvasHeight);\n        } else {\n            // Reset pooled enemy\n            enemy.reset();\n            enemy.position.set(x, y);\n            enemy.type = type;\n            enemy.maxHealth = enemy.getTypeMaxHealth(type);\n            enemy.health = enemy.maxHealth;\n            enemy.baseSpeed = enemy.getTypeBaseSpeed(type);\n            enemy.currentSpeed = enemy.baseSpeed;\n            enemy.scoreValue = enemy.getTypeScoreValue(type);\n        }\n\n        // Apply environmental effects\n        this.applyEnvironmentalEffects(enemy);\n\n        // Add to active enemies\n        this.activeEnemies.push(enemy);\n        this.totalEnemiesSpawned++;\n\n        // Register with game object manager if available\n        if (this.gameObjectManager) {\n            this.gameObjectManager.add(enemy);\n        }\n\n        console.log(`Spawned ${type} enemy at (${x}, ${y}). Active enemies: ${this.activeEnemies.length}`);\n        return enemy;\n    }\n\n    /**\n     * Get enemy from object pool\n     * @returns {Enemy|null} Pooled enemy or null if pool is empty\n     */\n    getEnemyFromPool() {\n        return this.enemyPool.length > 0 ? this.enemyPool.pop() : null;\n    }\n\n    /**\n     * Return enemy to object pool\n     * @param {Enemy} enemy - Enemy to return to pool\n     */\n    returnEnemyToPool(enemy) {\n        if (this.enemyPool.length < this.maxEnemies) {\n            enemy.reset();\n            this.enemyPool.push(enemy);\n        }\n\n        // Remove from game object manager if available\n        if (this.gameObjectManager) {\n            this.gameObjectManager.remove(enemy);\n        }\n    }\n\n    /**\n     * Add enemy to formation\n     * @param {Enemy} enemy - Enemy to add to formation\n     * @param {object} pattern - Formation pattern\n     */\n    addEnemyToFormation(enemy, pattern) {\n        const formation = this.getOrCreateFormation(pattern.formation);\n\n        // Calculate formation position\n        const formationIndex = formation.enemies.length;\n        const offset = this.calculateFormationOffset(pattern.formation, formationIndex);\n\n        enemy.setFormationTarget(formation.center, offset);\n        formation.enemies.push(enemy);\n\n        console.log(`Added enemy to ${pattern.formation} formation. Formation size: ${formation.enemies.length}`);\n    }\n\n    /**\n     * Get or create formation\n     * @param {string} formationType - Type of formation\n     * @returns {object} Formation object\n     */\n    getOrCreateFormation(formationType) {\n        let formation = this.formations.find(f => f.type === formationType && f.enemies.length < 8);\n\n        if (!formation) {\n            formation = {\n                type: formationType,\n                center: new Vector2(this.canvasWidth / 2, 100),\n                enemies: [],\n                movementTimer: 0\n            };\n            this.formations.push(formation);\n        }\n\n        return formation;\n    }\n\n    /**\n     * Calculate formation offset for enemy position\n     * @param {string} formationType - Type of formation\n     * @param {number} index - Enemy index in formation\n     * @returns {Vector2} Formation offset\n     */\n    calculateFormationOffset(formationType, index) {\n        switch (formationType) {\n            case 'line':\n                return new Vector2((index - 2) * 40, 0);\n\n            case 'v-formation':\n                const side = index % 2 === 0 ? -1 : 1;\n                const row = Math.floor(index / 2);\n                return new Vector2(side * (row + 1) * 30, row * 25);\n\n            case 'triangle':\n                const triangleRow = Math.floor((-1 + Math.sqrt(1 + 8 * index)) / 2);\n                const posInRow = index - (triangleRow * (triangleRow + 1)) / 2;\n                const rowWidth = triangleRow + 1;\n                return new Vector2((posInRow - rowWidth / 2 + 0.5) * 35, triangleRow * 30);\n\n            case 'diamond':\n                const diamondSize = 3;\n                if (index < diamondSize) {\n                    // Top part of diamond\n                    return new Vector2((index - 1) * 40, -30);\n                } else if (index < diamondSize * 2 - 1) {\n                    // Middle part of diamond\n                    const midIndex = index - diamondSize;\n                    return new Vector2((midIndex - 1) * 60, 0);\n                } else {\n                    // Bottom part of diamond\n                    const botIndex = index - (diamondSize * 2 - 1);\n                    return new Vector2((botIndex - 1) * 40, 30);\n                }\n\n            case 'circle':\n                const angle = (index / 8) * Math.PI * 2;\n                const radius = 60;\n                return new Vector2(Math.cos(angle) * radius, Math.sin(angle) * radius);\n\n            case 'wedge':\n                const wedgeRow = Math.floor(index / 3);\n                const wedgePos = index % 3;\n                return new Vector2((wedgePos - 1) * (40 + wedgeRow * 10), wedgeRow * 25);\n\n            default:\n                // Default to line formation\n                return new Vector2((index - 2) * 40, 0);\n        }\n    }\n\n    /**\n     * Update formations\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    updateFormations(deltaTime) {\n        for (let i = this.formations.length - 1; i >= 0; i--) {\n            const formation = this.formations[i];\n            formation.movementTimer += deltaTime / 1000;\n\n            // Remove destroyed enemies from formation\n            formation.enemies = formation.enemies.filter(enemy => enemy.active && !enemy.destroyed);\n\n            // Remove empty formations\n            if (formation.enemies.length === 0) {\n                this.formations.splice(i, 1);\n                continue;\n            }\n\n            // Update formation center position (slow movement)\n            formation.center.y += 20 * (deltaTime / 1000);\n\n            // Add some horizontal movement\n            formation.center.x += Math.sin(formation.movementTimer * 0.5) * 10 * (deltaTime / 1000);\n\n            // Keep formation on screen\n            formation.center.x = GameMath.clamp(formation.center.x, 100, this.canvasWidth - 100);\n        }\n    }\n\n    /**\n     * Process formation spawn queue\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     */\n    processFormationSpawnQueue(deltaTime) {\n        // Implementation for queued formation spawning\n        // This can be used for more complex formation spawning patterns\n    }\n\n    /**\n     * Check if current wave is complete\n     */\n    checkWaveCompletion() {\n        if (this.waveInProgress && this.waveConfig) {\n            const totalProcessed = this.enemiesKilledInWave + this.enemiesEscapedInWave;\n            const allEnemiesSpawned = this.enemiesSpawnedInWave >= this.waveConfig.totalEnemies;\n            const allEnemiesProcessed = totalProcessed >= this.waveConfig.totalEnemies;\n            const noActiveEnemies = this.activeEnemies.length === 0;\n\n            if (allEnemiesSpawned && (allEnemiesProcessed || noActiveEnemies)) {\n                this.completeWave();\n            }\n        }\n    }\n\n    /**\n     * Complete the current wave\n     */\n    completeWave() {\n        this.waveInProgress = false;\n\n        console.log(`Wave ${this.currentWave} completed! Enemies killed: ${this.enemiesKilledInWave}/${this.waveConfig.totalEnemies}, Enemies escaped: ${this.enemiesEscapedInWave}/${this.waveConfig.totalEnemies}`);\n\n        // Calculate wave completion bonus\n        const completionBonus = this.calculateWaveBonus();\n        this.totalScore += completionBonus;\n\n        // Clear formations\n        this.formations = [];\n\n        // Trigger wave completion event\n        this.onWaveComplete(this.currentWave, completionBonus);\n    }\n\n    /**\n     * Calculate wave completion bonus\n     * @returns {number} Bonus score\n     */\n    calculateWaveBonus() {\n        const baseBonus = 100;\n        const waveMultiplier = this.currentWave;\n        const completionRatio = this.enemiesKilledInWave / this.waveConfig.totalEnemies;\n\n        return Math.floor(baseBonus * waveMultiplier * completionRatio);\n    }\n\n    /**\n     * Wave completion callback (override in game)\n     * @param {number} waveNumber - Completed wave number\n     * @param {number} bonus - Completion bonus\n     */\n    onWaveComplete(waveNumber, bonus) {\n        // Override this method in the game to handle wave completion\n        console.log(`Wave ${waveNumber} complete with bonus: ${bonus}`);\n    }\n\n    /**\n     * Enemy escaped callback (override in game)\n     * @param {object} enemy - Enemy that escaped\n     */\n    onEnemyEscaped(enemy) {\n        // Override this method in the game to handle enemy escapes\n        console.log(`Enemy ${enemy.id} escaped`);\n    }\n\n    /**\n     * Render all active enemies and projectiles\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} interpolation - Interpolation factor for smooth rendering\n     */\n    render(ctx, interpolation = 0) {\n        // Render enemies\n        for (const enemy of this.activeEnemies) {\n            if (enemy.visible) {\n                enemy.render(ctx, interpolation);\n            }\n        }\n\n        // Enemy projectiles disabled - enemies don't fire projectiles\n        // this.projectileSystem.render(ctx, interpolation);\n\n        // Render debug information\n        if (window.DEBUG_MODE) {\n            this.renderDebugInfo(ctx);\n        }\n    }\n\n    /**\n     * Render debug information\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    renderDebugInfo(ctx) {\n        ctx.fillStyle = '#FFFFFF';\n        ctx.font = '12px Arial';\n        ctx.textAlign = 'left';\n\n        const debugInfo = [\n            `Wave: ${this.currentWave}`,\n            `Active Enemies: ${this.activeEnemies.length}`,\n            `Spawned: ${this.enemiesSpawnedInWave}/${this.waveConfig?.totalEnemies || 0}`,\n            `Killed: ${this.enemiesKilledInWave}`,\n            `Total Score: ${this.totalScore}`,\n            `Formations: ${this.formations.length}`\n        ];\n\n        for (let i = 0; i < debugInfo.length; i++) {\n            ctx.fillText(debugInfo[i], 10, 20 + i * 15);\n        }\n\n        // Draw formation centers\n        ctx.strokeStyle = '#FFFF00';\n        ctx.lineWidth = 2;\n        for (const formation of this.formations) {\n            ctx.beginPath();\n            ctx.arc(formation.center.x, formation.center.y, 5, 0, Math.PI * 2);\n            ctx.stroke();\n        }\n    }\n\n    /**\n     * Check collision between player and enemies\n     * @param {PlayerShip} player - Player ship\n     * @returns {Array} Array of colliding enemies\n     */\n    checkPlayerCollisions(player) {\n        const collisions = [];\n\n        if (!player.active || player.isInvulnerable) {\n            return collisions;\n        }\n\n        for (const enemy of this.activeEnemies) {\n            if (enemy.active && !enemy.isDestroyed && enemy.collidesWith(player)) {\n                collisions.push(enemy);\n            }\n        }\n\n        return collisions;\n    }\n\n    /**\n     * Check collision between projectiles and enemies\n     * @param {Array} projectiles - Array of projectiles\n     * @returns {Array} Array of collision results\n     */\n    checkProjectileCollisions(projectiles) {\n        const collisions = [];\n\n        for (const projectile of projectiles) {\n            if (!projectile.active || projectile.hasTag('enemyProjectile')) {\n                continue; // Skip enemy projectiles\n            }\n\n            for (const enemy of this.activeEnemies) {\n                if (enemy.active && !enemy.isDestroyed && enemy.collidesWith(projectile)) {\n                    collisions.push({\n                        projectile: projectile,\n                        enemy: enemy,\n                        damage: projectile.damage || 25\n                    });\n                    break; // Projectile can only hit one enemy\n                }\n            }\n        }\n\n        return collisions;\n    }\n\n    /**\n     * Handle collision between player and enemy\n     * @param {PlayerShip} player - Player ship\n     * @param {Enemy} enemy - Colliding enemy\n     */\n    handlePlayerEnemyCollision(player, enemy) {\n        // Player takes damage\n        const damageResult = player.takeDamage(enemy.getAttackDamage());\n\n        // Enemy is destroyed on collision\n        enemy.takeDamage(enemy.health);\n\n        console.log(`Player-Enemy collision: Player took ${damageResult.damageTaken} damage, Enemy destroyed`);\n\n        return {\n            playerDamage: damageResult.damageTaken,\n            enemyDestroyed: true,\n            scoreGained: enemy.scoreValue\n        };\n    }\n\n    /**\n     * Handle collision between projectile and enemy\n     * @param {Projectile} projectile - Projectile\n     * @param {Enemy} enemy - Enemy\n     * @param {number} damage - Damage amount\n     */\n    handleProjectileEnemyCollision(projectile, enemy, damage) {\n        // Enemy takes damage\n        const damageResult = enemy.takeDamage(damage);\n\n        // Destroy projectile\n        projectile.destroy();\n\n        console.log(`Projectile-Enemy collision: Enemy took ${damageResult.damageTaken} damage`);\n\n        return {\n            enemyDestroyed: damageResult.destroyed,\n            scoreGained: damageResult.scoreValue,\n            damageDealt: damageResult.damageTaken\n        };\n    }\n\n    /**\n     * Apply environmental effects to enemy\n     * @param {Enemy} enemy - Enemy to apply effects to\n     */\n    applyEnvironmentalEffects(enemy) {\n        const effectiveness = this.calculateEnvironmentalEffectiveness(enemy.type, this.currentEnvironment);\n        enemy.applyEnvironmentalEffect(this.currentEnvironment, effectiveness);\n    }\n\n    /**\n     * Calculate environmental effectiveness for enemy type\n     * @param {string} enemyType - Enemy type\n     * @param {string} environment - Current environment\n     * @returns {number} Effectiveness multiplier\n     */\n    calculateEnvironmentalEffectiveness(enemyType, environment) {\n        const effectivenessMap = this.environmentalEffects[environment];\n        return effectivenessMap ? (effectivenessMap[enemyType] || 1.0) : 1.0;\n    }\n\n    /**\n     * Set current environment and update all enemies\n     * @param {string} environment - New environment type\n     * @param {object} effects - Environmental effects configuration\n     */\n    setEnvironment(environment, effects = null) {\n        this.currentEnvironment = environment;\n\n        if (effects) {\n            this.environmentalEffects[environment] = effects;\n        }\n\n        // Update all active enemies\n        for (const enemy of this.activeEnemies) {\n            this.applyEnvironmentalEffects(enemy);\n        }\n\n        console.log(`Environment changed to: ${environment}`);\n    }\n\n    /**\n     * Get default environmental effects\n     * @returns {object} Default environmental effects configuration\n     */\n    getDefaultEnvironmentalEffects() {\n        return {\n            space: {\n                [ENEMY_TYPES.AIR]: 1.2,\n                [ENEMY_TYPES.WATER]: 0.8,\n                [ENEMY_TYPES.FIRE]: 1.0,\n                [ENEMY_TYPES.EARTH]: 0.9,\n                [ENEMY_TYPES.CRYSTAL]: 1.1,\n                [ENEMY_TYPES.SHADOW]: 1.0\n            },\n            underwater: {\n                [ENEMY_TYPES.AIR]: 0.6,\n                [ENEMY_TYPES.WATER]: 1.5,\n                [ENEMY_TYPES.FIRE]: 0.3,\n                [ENEMY_TYPES.EARTH]: 0.8,\n                [ENEMY_TYPES.CRYSTAL]: 1.0,\n                [ENEMY_TYPES.SHADOW]: 0.9\n            },\n            volcanic: {\n                [ENEMY_TYPES.AIR]: 0.8,\n                [ENEMY_TYPES.WATER]: 0.4,\n                [ENEMY_TYPES.FIRE]: 1.6,\n                [ENEMY_TYPES.EARTH]: 1.3,\n                [ENEMY_TYPES.CRYSTAL]: 0.9,\n                [ENEMY_TYPES.SHADOW]: 0.7\n            },\n            crystal: {\n                [ENEMY_TYPES.AIR]: 1.0,\n                [ENEMY_TYPES.WATER]: 0.9,\n                [ENEMY_TYPES.FIRE]: 0.8,\n                [ENEMY_TYPES.EARTH]: 1.1,\n                [ENEMY_TYPES.CRYSTAL]: 1.8,\n                [ENEMY_TYPES.SHADOW]: 1.2\n            },\n            forest: {\n                [ENEMY_TYPES.AIR]: 0.7,\n                [ENEMY_TYPES.WATER]: 1.1,\n                [ENEMY_TYPES.FIRE]: 0.6,\n                [ENEMY_TYPES.EARTH]: 1.4,\n                [ENEMY_TYPES.CRYSTAL]: 0.8,\n                [ENEMY_TYPES.SHADOW]: 1.3\n            }\n        };\n    }\n\n    /**\n     * Update enemy attacks and projectile firing\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     * @param {Vector2} playerPosition - Player's position\n     */\n    updateEnemyAttacks(deltaTime, playerPosition) {\n        if (!playerPosition) return;\n\n        for (const enemy of this.activeEnemies) {\n            if (enemy.active && !enemy.isDestroyed && enemy.canAttackPlayer(playerPosition)) {\n                const attackData = enemy.attack(playerPosition);\n                if (attackData) {\n                    // Enemy projectiles disabled - enemies don't fire projectiles\n                    // this.projectileSystem.fireProjectile(\n                    //     attackData.position,\n                    //     attackData.direction,\n                    //     attackData.type,\n                    //     { damage: attackData.damage }\n                    // );\n                }\n            }\n        }\n    }\n\n    /**\n     * Get enemies that can attack the player\n     * @param {Vector2} playerPosition - Player's position\n     * @returns {Array} Array of enemies that can attack\n     */\n    getEnemiesInAttackRange(playerPosition) {\n        return this.activeEnemies.filter(enemy =>\n            enemy.canAttackPlayer(playerPosition)\n        );\n    }\n\n    /**\n     * Check collision between enemy projectiles and player\n     * @param {PlayerShip} player - Player ship\n     * @returns {Array} Array of colliding enemy projectiles\n     */\n    checkEnemyProjectileCollisions(player) {\n        // Enemy projectiles disabled - enemies don't fire projectiles\n        return [];\n        // return this.projectileSystem.checkPlayerCollisions(player);\n    }\n\n    /**\n     * Handle collision between enemy projectile and player\n     * @param {EnemyProjectile} projectile - Enemy projectile\n     * @param {PlayerShip} player - Player ship\n     * @returns {object} Collision result\n     */\n    handleEnemyProjectileCollision(projectile, player) {\n        // Enemy projectiles disabled - enemies don't fire projectiles\n        return { damage: 0, destroyed: false };\n        // return this.projectileSystem.handlePlayerCollision(projectile, player);\n    }\n\n    /**\n     * Trigger enemy attacks\n     * @param {Vector2} playerPosition - Player's position\n     * @returns {Array} Array of attack data from enemies\n     */\n    triggerEnemyAttacks(playerPosition) {\n        const attacks = [];\n        const attackingEnemies = this.getEnemiesInAttackRange(playerPosition);\n\n        for (const enemy of attackingEnemies) {\n            const attackData = enemy.attack(playerPosition);\n            if (attackData) {\n                attacks.push(attackData);\n            }\n        }\n\n        return attacks;\n    }\n\n    /**\n     * Get current wave status\n     * @returns {object} Wave status information\n     */\n    getWaveStatus() {\n        return {\n            currentWave: this.currentWave,\n            waveInProgress: this.waveInProgress,\n            enemiesSpawned: this.enemiesSpawnedInWave,\n            enemiesKilled: this.enemiesKilledInWave,\n            totalEnemies: this.waveConfig?.totalEnemies || 0,\n            activeEnemies: this.activeEnemies.length,\n            totalScore: this.totalScore,\n            waveProgress: this.waveConfig ? (this.enemiesSpawnedInWave / this.waveConfig.totalEnemies) : 0\n        };\n    }\n\n    /**\n     * Reset enemy manager (for new game)\n     */\n    reset() {\n        // Clear all enemies\n        for (const enemy of this.activeEnemies) {\n            enemy.destroy();\n        }\n        this.activeEnemies = [];\n\n        // Reset wave management\n        this.currentWave = 0;\n        this.waveInProgress = false;\n        this.waveConfig = null;\n        this.enemiesSpawnedInWave = 0;\n        this.enemiesKilledInWave = 0;\n        this.enemiesEscapedInWave = 0;\n        this.patternSpawnCounts = {};\n\n        // Reset timers\n        this.spawnTimer = 0;\n        this.waveStartTime = 0;\n\n        // Clear formations\n        this.formations = [];\n        this.formationSpawnQueue = [];\n\n        // Reset statistics\n        this.totalEnemiesSpawned = 0;\n        this.totalEnemiesKilled = 0;\n        this.totalScore = 0;\n\n        // Reset environment\n        this.currentEnvironment = 'space';\n\n        // Reset GameObject ID counter to start fresh\n        if (typeof GameObject !== 'undefined' && GameObject.idCounter) {\n            GameObject.idCounter = 0;\n        }\n\n        // Reset projectile system disabled - enemies don't fire projectiles\n        // this.projectileSystem.reset();\n\n        console.log('EnemyManager reset');\n    }\n\n    /**\n     * Update canvas dimensions (for window resize)\n     * @param {number} width - New canvas width\n     * @param {number} height - New canvas height\n     */\n    updateCanvasDimensions(width, height) {\n        this.canvasWidth = width;\n        this.canvasHeight = height;\n\n        // Update grid dimensions\n        this.gridWidth = Math.ceil(width / this.gridSize);\n        this.gridHeight = Math.ceil(height / this.gridSize);\n\n        // Update projectile system disabled - enemies don't fire projectiles\n        // this.projectileSystem.updateCanvasDimensions(width, height);\n\n        // Update all active enemies\n        for (const enemy of this.activeEnemies) {\n            enemy.canvasWidth = width;\n            enemy.canvasHeight = height;\n        }\n    }\n\n    /**\n     * Get statistics for display\n     * @returns {object} Statistics object\n     */\n    getStatistics() {\n        return {\n            currentWave: this.currentWave,\n            totalEnemiesSpawned: this.totalEnemiesSpawned,\n            totalEnemiesKilled: this.totalEnemiesKilled,\n            totalScore: this.totalScore,\n            activeEnemies: this.activeEnemies.length,\n            formations: this.formations.length,\n            killRatio: this.totalEnemiesSpawned > 0 ? (this.totalEnemiesKilled / this.totalEnemiesSpawned) : 0\n        };\n    }\n}", "import { GAME_CONFIG } from '../config/gameConfig.js';\n\n/**\n * TokenEconomyManager handles WISH token tracking, transactions, and rewards\n * Implements performance-based token calculation and balance management\n */\nexport class TokenEconomyManager {\n    constructor() {\n        // Token balance\n        this.playerBalance = 0;\n        this.totalEarned = 0;\n        this.totalSpent = 0;\n        \n        // Transaction history\n        this.transactionHistory = [];\n        this.maxHistorySize = 100;\n        \n        // Performance tracking for rewards\n        this.performanceMetrics = {\n            levelsCompleted: 0,\n            totalScore: 0,\n            averageCompletionTime: 0,\n            perfectCompletions: 0,\n            speedBonuses: 0,\n            accuracyBonuses: 0\n        };\n        \n        // Token reward multipliers\n        this.rewardMultipliers = {\n            base: 1.0,\n            speed: 1.5,\n            accuracy: 1.3,\n            perfect: 2.0,\n            difficulty: 1.0\n        };\n        \n        // Visual feedback state\n        this.pendingRewards = [];\n        this.rewardAnimations = [];\n        \n        // Callbacks for UI updates\n        this.onBalanceUpdateCallback = null;\n        this.onTransactionCallback = null;\n        this.onRewardEarnedCallback = null;\n        \n        console.log('TokenEconomyManager initialized');\n    }\n    \n    /**\n     * Calculate token reward based on level completion performance\n     * @param {object} completionData - Level completion data from LevelManager\n     * @returns {object} Token reward breakdown\n     */\n    calculateLevelReward(completionData) {\n        if (!completionData.completed) {\n            return { totalReward: 0, breakdown: { reason: 'level_not_completed' } };\n        }\n        \n        const levelNumber = completionData.levelNumber;\n        const completionTime = completionData.completionTime;\n        const scoreData = completionData.score;\n        \n        // Base reward calculation\n        const baseReward = this.calculateBaseReward(levelNumber);\n        \n        // Time-based bonus (inverse relationship - faster = more tokens)\n        const timeBonus = this.calculateTimeBonus(completionTime, levelNumber);\n        \n        // Score-based bonus\n        const scoreBonus = this.calculateScoreBonus(scoreData.totalScore, levelNumber);\n        \n        // Performance bonuses\n        const speedBonus = completionData.bonuses.speed ? this.calculateSpeedBonus(baseReward) : 0;\n        const accuracyBonus = completionData.bonuses.accuracy ? this.calculateAccuracyBonus(baseReward) : 0;\n        const perfectBonus = completionData.bonuses.perfect ? this.calculatePerfectBonus(baseReward) : 0;\n        \n        // Difficulty multiplier\n        const difficultyMultiplier = this.calculateDifficultyMultiplier(levelNumber);\n        \n        // Calculate total before multiplier\n        const subtotal = baseReward + timeBonus + scoreBonus + speedBonus + accuracyBonus + perfectBonus;\n        \n        // Apply difficulty multiplier\n        const totalReward = Math.floor(subtotal * difficultyMultiplier);\n        \n        const breakdown = {\n            baseReward,\n            timeBonus,\n            scoreBonus,\n            speedBonus,\n            accuracyBonus,\n            perfectBonus,\n            difficultyMultiplier,\n            subtotal,\n            totalReward\n        };\n        \n        console.log(`Level ${levelNumber} token reward calculated:`, breakdown);\n        \n        return { totalReward, breakdown };\n    }\n    \n    /**\n     * Calculate base reward for level completion\n     * @param {number} levelNumber - Level number\n     * @returns {number} Base reward amount\n     */\n    calculateBaseReward(levelNumber) {\n        const baseAmount = GAME_CONFIG.BASE_LEVEL_REWARD;\n        const levelMultiplier = Math.floor((levelNumber - 1) / 5) + 1; // Increases every 5 levels\n        return baseAmount * levelMultiplier;\n    }\n    \n    /**\n     * Calculate time bonus (faster completion = more tokens)\n     * @param {number} completionTime - Time taken in seconds\n     * @param {number} levelNumber - Level number\n     * @returns {number} Time bonus amount\n     */\n    calculateTimeBonus(completionTime, levelNumber) {\n        // Expected completion time increases with level\n        const expectedTime = 60 + (levelNumber * 10); // Base 60s + 10s per level\n        const fastTime = expectedTime * 0.5; // 50% of expected time for max bonus\n        \n        if (completionTime <= fastTime) {\n            // Excellent time - maximum bonus\n            return Math.floor(30 + (levelNumber * 5));\n        } else if (completionTime <= expectedTime * 0.75) {\n            // Good time - partial bonus\n            return Math.floor(20 + (levelNumber * 3));\n        } else if (completionTime <= expectedTime) {\n            // Acceptable time - small bonus\n            return Math.floor(10 + levelNumber);\n        }\n        \n        return 0; // No bonus for slow completion\n    }\n    \n    /**\n     * Calculate score bonus based on points earned\n     * @param {number} score - Score achieved\n     * @param {number} levelNumber - Level number\n     * @returns {number} Score bonus amount\n     */\n    calculateScoreBonus(score, levelNumber) {\n        // Score bonus is a percentage of the score, scaled by level\n        const basePercentage = 0.01; // 1% of score as base\n        const levelScaling = Math.min(2.0, 1.0 + (levelNumber * 0.05)); // Up to 2x scaling\n        \n        return Math.floor(score * basePercentage * levelScaling);\n    }\n    \n    /**\n     * Calculate speed bonus for fast completion\n     * @param {number} baseReward - Base reward amount\n     * @returns {number} Speed bonus amount\n     */\n    calculateSpeedBonus(baseReward) {\n        return Math.floor(baseReward * (this.rewardMultipliers.speed - 1.0));\n    }\n    \n    /**\n     * Calculate accuracy bonus for high accuracy\n     * @param {number} baseReward - Base reward amount\n     * @returns {number} Accuracy bonus amount\n     */\n    calculateAccuracyBonus(baseReward) {\n        return Math.floor(baseReward * (this.rewardMultipliers.accuracy - 1.0));\n    }\n    \n    /**\n     * Calculate perfect completion bonus\n     * @param {number} baseReward - Base reward amount\n     * @returns {number} Perfect bonus amount\n     */\n    calculatePerfectBonus(baseReward) {\n        return Math.floor(baseReward * (this.rewardMultipliers.perfect - 1.0));\n    }\n    \n    /**\n     * Calculate difficulty multiplier based on level\n     * @param {number} levelNumber - Level number\n     * @returns {number} Difficulty multiplier\n     */\n    calculateDifficultyMultiplier(levelNumber) {\n        // Multiplier increases gradually with level\n        const baseMultiplier = 1.0;\n        const increment = 0.1;\n        const maxMultiplier = 3.0;\n        \n        const multiplier = baseMultiplier + (Math.floor((levelNumber - 1) / 10) * increment);\n        return Math.min(maxMultiplier, multiplier);\n    }\n    \n    /**\n     * Award tokens to player balance\n     * @param {number} amount - Amount to award\n     * @param {string} reason - Reason for the award\n     * @param {object} metadata - Additional metadata\n     * @returns {object} Transaction result\n     */\n    awardTokens(amount, reason, metadata = {}) {\n        if (amount <= 0) {\n            console.warn('Attempted to award non-positive token amount:', amount);\n            return { success: false, reason: 'invalid_amount' };\n        }\n        \n        // Update balance\n        this.playerBalance += amount;\n        this.totalEarned += amount;\n        \n        // Create transaction record\n        const transaction = {\n            id: this.generateTransactionId(),\n            type: 'earned',\n            amount: amount,\n            reason: reason,\n            timestamp: Date.now(),\n            balanceAfter: this.playerBalance,\n            metadata: metadata\n        };\n        \n        // Add to history\n        this.addTransaction(transaction);\n        \n        // Update performance metrics\n        this.updatePerformanceMetrics(reason, metadata);\n        \n        // Add to pending rewards for visual feedback\n        this.addPendingReward(amount, reason);\n        \n        console.log(`Awarded ${amount} WISH tokens for ${reason}. New balance: ${this.playerBalance}`);\n        \n        // Trigger callbacks\n        this.triggerBalanceUpdate();\n        this.triggerRewardEarned(amount, reason, metadata);\n        \n        return { \n            success: true, \n            transaction: transaction,\n            newBalance: this.playerBalance\n        };\n    }\n    \n    /**\n     * Spend tokens from player balance\n     * @param {number} amount - Amount to spend\n     * @param {string} reason - Reason for spending\n     * @param {object} metadata - Additional metadata\n     * @returns {object} Transaction result\n     */\n    spendTokens(amount, reason, metadata = {}) {\n        if (amount <= 0) {\n            console.warn('Attempted to spend non-positive token amount:', amount);\n            return { success: false, reason: 'invalid_amount' };\n        }\n        \n        if (this.playerBalance < amount) {\n            console.warn(`Insufficient tokens. Required: ${amount}, Available: ${this.playerBalance}`);\n            return { success: false, reason: 'insufficient_balance', required: amount, available: this.playerBalance };\n        }\n        \n        // Update balance\n        this.playerBalance -= amount;\n        this.totalSpent += amount;\n        \n        // Create transaction record\n        const transaction = {\n            id: this.generateTransactionId(),\n            type: 'spent',\n            amount: amount,\n            reason: reason,\n            timestamp: Date.now(),\n            balanceAfter: this.playerBalance,\n            metadata: metadata\n        };\n        \n        // Add to history\n        this.addTransaction(transaction);\n        \n        console.log(`Spent ${amount} WISH tokens for ${reason}. New balance: ${this.playerBalance}`);\n        \n        // Trigger callbacks\n        this.triggerBalanceUpdate();\n        this.triggerTransaction(transaction);\n        \n        return { \n            success: true, \n            transaction: transaction,\n            newBalance: this.playerBalance\n        };\n    }\n    \n    /**\n     * Check if player can afford a purchase\n     * @param {number} amount - Amount to check\n     * @returns {boolean} Whether player can afford the amount\n     */\n    canAfford(amount) {\n        return this.playerBalance >= amount;\n    }\n    \n    /**\n     * Get current token balance\n     * @returns {number} Current balance\n     */\n    getBalance() {\n        return this.playerBalance;\n    }\n    \n    /**\n     * Get token economy statistics\n     * @returns {object} Economy statistics\n     */\n    getStatistics() {\n        const netProfit = this.totalEarned - this.totalSpent;\n        const averageEarningPerLevel = this.performanceMetrics.levelsCompleted > 0 \n            ? this.totalEarned / this.performanceMetrics.levelsCompleted \n            : 0;\n        \n        return {\n            currentBalance: this.playerBalance,\n            totalEarned: this.totalEarned,\n            totalSpent: this.totalSpent,\n            netProfit: netProfit,\n            transactionCount: this.transactionHistory.length,\n            averageEarningPerLevel: Math.floor(averageEarningPerLevel),\n            performanceMetrics: { ...this.performanceMetrics }\n        };\n    }\n    \n    /**\n     * Get recent transaction history\n     * @param {number} count - Number of recent transactions to return\n     * @returns {Array} Recent transactions\n     */\n    getRecentTransactions(count = 10) {\n        return this.transactionHistory\n            .slice(-count)\n            .reverse(); // Most recent first\n    }\n    \n    /**\n     * Add transaction to history\n     * @param {object} transaction - Transaction to add\n     */\n    addTransaction(transaction) {\n        this.transactionHistory.push(transaction);\n        \n        // Limit history size\n        if (this.transactionHistory.length > this.maxHistorySize) {\n            this.transactionHistory.shift();\n        }\n    }\n    \n    /**\n     * Generate unique transaction ID\n     * @returns {string} Transaction ID\n     */\n    generateTransactionId() {\n        return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    }\n    \n    /**\n     * Update performance metrics\n     * @param {string} reason - Reason for the update\n     * @param {object} metadata - Additional metadata\n     */\n    updatePerformanceMetrics(reason, metadata) {\n        if (reason === 'level_completion') {\n            this.performanceMetrics.levelsCompleted++;\n            \n            if (metadata.score) {\n                this.performanceMetrics.totalScore += metadata.score;\n            }\n            \n            if (metadata.completionTime) {\n                // Update average completion time\n                const currentAvg = this.performanceMetrics.averageCompletionTime;\n                const count = this.performanceMetrics.levelsCompleted;\n                this.performanceMetrics.averageCompletionTime = \n                    (currentAvg * (count - 1) + metadata.completionTime) / count;\n            }\n            \n            if (metadata.bonuses) {\n                if (metadata.bonuses.perfect) this.performanceMetrics.perfectCompletions++;\n                if (metadata.bonuses.speed) this.performanceMetrics.speedBonuses++;\n                if (metadata.bonuses.accuracy) this.performanceMetrics.accuracyBonuses++;\n            }\n        }\n    }\n    \n    /**\n     * Add pending reward for visual feedback\n     * @param {number} amount - Reward amount\n     * @param {string} reason - Reason for reward\n     */\n    addPendingReward(amount, reason) {\n        const reward = {\n            id: this.generateTransactionId(),\n            amount: amount,\n            reason: reason,\n            timestamp: Date.now(),\n            displayed: false\n        };\n        \n        this.pendingRewards.push(reward);\n    }\n    \n    /**\n     * Get pending rewards for display\n     * @returns {Array} Pending rewards\n     */\n    getPendingRewards() {\n        return this.pendingRewards.filter(reward => !reward.displayed);\n    }\n    \n    /**\n     * Mark reward as displayed\n     * @param {string} rewardId - Reward ID to mark as displayed\n     */\n    markRewardDisplayed(rewardId) {\n        const reward = this.pendingRewards.find(r => r.id === rewardId);\n        if (reward) {\n            reward.displayed = true;\n        }\n        \n        // Clean up old displayed rewards\n        const cutoffTime = Date.now() - 10000; // 10 seconds\n        this.pendingRewards = this.pendingRewards.filter(\n            reward => !reward.displayed || reward.timestamp > cutoffTime\n        );\n    }\n    \n    /**\n     * Update reward animations\n     * @param {number} deltaTime - Time elapsed since last update\n     */\n    updateRewardAnimations(deltaTime) {\n        // Update existing animations\n        for (let i = this.rewardAnimations.length - 1; i >= 0; i--) {\n            const animation = this.rewardAnimations[i];\n            animation.elapsed += deltaTime;\n            \n            // Remove completed animations\n            if (animation.elapsed >= animation.duration) {\n                this.rewardAnimations.splice(i, 1);\n            }\n        }\n        \n        // Add new animations for pending rewards\n        const pendingRewards = this.getPendingRewards();\n        for (const reward of pendingRewards) {\n            this.rewardAnimations.push({\n                id: reward.id,\n                amount: reward.amount,\n                reason: reward.reason,\n                startTime: Date.now(),\n                elapsed: 0,\n                duration: 3000, // 3 second animation\n                startY: 100,\n                endY: 50,\n                alpha: 1.0\n            });\n            \n            this.markRewardDisplayed(reward.id);\n        }\n    }\n    \n    /**\n     * Render token balance and reward animations\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     * @param {number} deltaTime - Time elapsed since last update\n     */\n    render(ctx, deltaTime) {\n        // Update animations\n        this.updateRewardAnimations(deltaTime);\n        \n        // Render token balance (top-right corner)\n        this.renderTokenBalance(ctx);\n        \n        // Render reward animations\n        this.renderRewardAnimations(ctx);\n    }\n    \n    /**\n     * Render token balance display\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    renderTokenBalance(ctx) {\n        const x = ctx.canvas.width - 20;\n        const y = 80;\n        \n        // Background\n        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';\n        ctx.fillRect(x - 150, y - 25, 140, 30);\n        \n        // Border\n        ctx.strokeStyle = '#FFD700';\n        ctx.lineWidth = 2;\n        ctx.strokeRect(x - 150, y - 25, 140, 30);\n        \n        // Token icon (simple star)\n        ctx.fillStyle = '#FFD700';\n        ctx.font = '16px Arial';\n        ctx.textAlign = 'left';\n        ctx.fillText('★', x - 145, y - 5);\n        \n        // Balance text\n        ctx.fillStyle = '#FFFFFF';\n        ctx.font = '14px Arial';\n        ctx.textAlign = 'right';\n        ctx.fillText(`${this.playerBalance.toLocaleString()} WISH`, x - 10, y - 5);\n    }\n    \n    /**\n     * Render reward animations\n     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context\n     */\n    renderRewardAnimations(ctx) {\n        for (const animation of this.rewardAnimations) {\n            const progress = animation.elapsed / animation.duration;\n            const y = animation.startY + (animation.endY - animation.startY) * progress;\n            const alpha = Math.max(0, 1.0 - progress);\n            \n            // Reward text\n            ctx.save();\n            ctx.globalAlpha = alpha;\n            ctx.fillStyle = '#00FF00';\n            ctx.font = 'bold 18px Arial';\n            ctx.textAlign = 'center';\n            ctx.fillText(`+${animation.amount} WISH`, ctx.canvas.width / 2, y);\n            \n            // Reason text\n            ctx.fillStyle = '#FFFFFF';\n            ctx.font = '12px Arial';\n            ctx.fillText(this.formatRewardReason(animation.reason), ctx.canvas.width / 2, y + 20);\n            ctx.restore();\n        }\n    }\n    \n    /**\n     * Format reward reason for display\n     * @param {string} reason - Reward reason\n     * @returns {string} Formatted reason\n     */\n    formatRewardReason(reason) {\n        const reasonMap = {\n            'level_completion': 'Level Complete',\n            'speed_bonus': 'Speed Bonus',\n            'accuracy_bonus': 'Accuracy Bonus',\n            'perfect_bonus': 'Perfect Run',\n            'enemy_defeat': 'Enemy Defeated',\n            'wave_completion': 'Wave Complete'\n        };\n        \n        return reasonMap[reason] || reason.replace(/_/g, ' ').toUpperCase();\n    }\n    \n    /**\n     * Reset token economy (for new game)\n     */\n    reset() {\n        this.playerBalance = 0;\n        this.totalEarned = 0;\n        this.totalSpent = 0;\n        this.transactionHistory = [];\n        this.performanceMetrics = {\n            levelsCompleted: 0,\n            totalScore: 0,\n            averageCompletionTime: 0,\n            perfectCompletions: 0,\n            speedBonuses: 0,\n            accuracyBonuses: 0\n        };\n        this.pendingRewards = [];\n        this.rewardAnimations = [];\n        \n        console.log('TokenEconomyManager reset');\n    }\n    \n    /**\n     * Trigger balance update callback\n     */\n    triggerBalanceUpdate() {\n        if (this.onBalanceUpdateCallback) {\n            this.onBalanceUpdateCallback(this.playerBalance, this.getStatistics());\n        }\n    }\n    \n    /**\n     * Trigger transaction callback\n     * @param {object} transaction - Transaction data\n     */\n    triggerTransaction(transaction) {\n        if (this.onTransactionCallback) {\n            this.onTransactionCallback(transaction);\n        }\n    }\n    \n    /**\n     * Trigger reward earned callback\n     * @param {number} amount - Reward amount\n     * @param {string} reason - Reward reason\n     * @param {object} metadata - Additional metadata\n     */\n    triggerRewardEarned(amount, reason, metadata) {\n        if (this.onRewardEarnedCallback) {\n            this.onRewardEarnedCallback(amount, reason, metadata);\n        }\n    }\n    \n    /**\n     * Set callback for balance updates\n     * @param {Function} callback - Callback function\n     */\n    setOnBalanceUpdate(callback) {\n        this.onBalanceUpdateCallback = callback;\n    }\n    \n    /**\n     * Set callback for transactions\n     * @param {Function} callback - Callback function\n     */\n    setOnTransaction(callback) {\n        this.onTransactionCallback = callback;\n    }\n    \n    /**\n     * Set callback for reward earned events\n     * @param {Function} callback - Callback function\n     */\n    setOnRewardEarned(callback) {\n        this.onRewardEarnedCallback = callback;\n    }\n}", "import { GAME_CONFIG } from '../config/gameConfig.js';\n\n/**\n * PowerUp base class for all power-up types\n * Provides common functionality for power-up effects, duration tracking, and application\n */\nexport class PowerUp {\n    constructor(type, cost, duration = null, description = '') {\n        this.type = type;\n        this.cost = cost;\n        this.duration = duration; // null for permanent power-ups\n        this.description = description;\n        this.isActive = false;\n        this.timeRemaining = duration;\n        this.appliedAt = null;\n        this.id = PowerUp.generateId();\n        \n        // Visual properties\n        this.icon = null;\n        this.color = '#00ffff';\n        this.glowColor = '#ffffff';\n    }\n    \n    // Static ID generator\n    static idCounter = 0;\n    static generateId() {\n        return `powerup_${++PowerUp.idCounter}`;\n    }\n    \n    /**\n     * Apply the power-up effect to the player ship\n     * @param {PlayerShip} playerShip - The player ship to apply the effect to\n     * @returns {boolean} True if successfully applied\n     */\n    apply(playerShip) {\n        if (this.isActive) {\n            console.warn(`PowerUp ${this.type} is already active`);\n            return false;\n        }\n        \n        this.isActive = true;\n        this.appliedAt = Date.now();\n        this.timeRemaining = this.duration;\n        \n        console.log(`Applied power-up: ${this.type}`);\n        return this.applyEffect(playerShip);\n    }\n    \n    /**\n     * Remove the power-up effect from the player ship\n     * @param {PlayerShip} playerShip - The player ship to remove the effect from\n     * @returns {boolean} True if successfully removed\n     */\n    remove(playerShip) {\n        if (!this.isActive) {\n            console.warn(`PowerUp ${this.type} is not active`);\n            return false;\n        }\n        \n        this.isActive = false;\n        this.timeRemaining = 0;\n        \n        console.log(`Removed power-up: ${this.type}`);\n        return this.removeEffect(playerShip);\n    }\n    \n    /**\n     * Update the power-up (handle duration countdown)\n     * @param {number} deltaTime - Time elapsed since last update in milliseconds\n     * @param {PlayerShip} playerShip - The player ship (for auto-removal)\n     * @returns {boolean} True if power-up is still active\n     */\n    update(deltaTime, playerShip) {\n        if (!this.isActive) return false;\n        \n        // Handle duration countdown for temporary power-ups\n        if (this.duration !== null && this.timeRemaining > 0) {\n            this.timeRemaining -= deltaTime;\n            \n            // Auto-remove when expired\n            if (this.timeRemaining <= 0) {\n                this.remove(playerShip);\n                return false;\n            }\n        }\n        \n        return true;\n    }\n    \n    /**\n     * Get the remaining time as a percentage (0-1)\n     * @returns {number} Percentage of time remaining\n     */\n    getTimeRemainingPercentage() {\n        if (this.duration === null) return 1; // Permanent power-ups\n        if (!this.isActive) return 0;\n        return Math.max(0, this.timeRemaining / this.duration);\n    }\n    \n    /**\n     * Get formatted time remaining string\n     * @returns {string} Formatted time string\n     */\n    getFormattedTimeRemaining() {\n        if (this.duration === null) return 'Permanent';\n        if (!this.isActive) return 'Inactive';\n        \n        const seconds = Math.ceil(this.timeRemaining / 1000);\n        return `${seconds}s`;\n    }\n    \n    /**\n     * Check if this power-up can be purchased (override in subclasses for special conditions)\n     * @param {PlayerShip} playerShip - The player ship\n     * @param {number} playerTokens - Current player token balance\n     * @returns {object} Purchase availability info\n     */\n    canPurchase(playerShip, playerTokens) {\n        return {\n            canPurchase: playerTokens >= this.cost && !this.isActive,\n            reason: playerTokens < this.cost ? 'insufficient_tokens' : \n                   this.isActive ? 'already_active' : 'available'\n        };\n    }\n    \n    /**\n     * Apply the specific effect (override in subclasses)\n     * @param {PlayerShip} playerShip - The player ship\n     * @returns {boolean} True if successfully applied\n     */\n    applyEffect(playerShip) {\n        // Override in subclasses\n        return true;\n    }\n    \n    /**\n     * Remove the specific effect (override in subclasses)\n     * @param {PlayerShip} playerShip - The player ship\n     * @returns {boolean} True if successfully removed\n     */\n    removeEffect(playerShip) {\n        // Override in subclasses\n        return true;\n    }\n    \n    /**\n     * Get power-up info for UI display\n     * @returns {object} Power-up display information\n     */\n    getDisplayInfo() {\n        return {\n            id: this.id,\n            type: this.type,\n            cost: this.cost,\n            duration: this.duration,\n            description: this.description,\n            isActive: this.isActive,\n            timeRemaining: this.timeRemaining,\n            timeRemainingPercentage: this.getTimeRemainingPercentage(),\n            formattedTimeRemaining: this.getFormattedTimeRemaining(),\n            icon: this.icon,\n            color: this.color,\n            glowColor: this.glowColor\n        };\n    }\n}\n\n/**\n * Extra Life power-up - Adds one extra life to the player\n */\nexport class ExtraLifePowerUp extends PowerUp {\n    constructor() {\n        super(\n            'EXTRA_LIFE',\n            GAME_CONFIG.POWER_UP_COSTS.EXTRA_LIFE,\n            null, // Permanent effect\n            'Gain an extra life to continue your journey'\n        );\n        \n        this.icon = '❤️';\n        this.color = '#ff4444';\n        this.glowColor = '#ff8888';\n    }\n    \n    applyEffect(playerShip) {\n        playerShip.addLives(1);\n        return true;\n    }\n    \n    canPurchase(playerShip, playerTokens) {\n        // Extra life can always be purchased if player has tokens\n        return {\n            canPurchase: playerTokens >= this.cost,\n            reason: playerTokens < this.cost ? 'insufficient_tokens' : 'available'\n        };\n    }\n}\n\n/**\n * Spread Ammo power-up - Gives the player spread pattern ammunition\n */\nexport class SpreadAmmoPowerUp extends PowerUp {\n    constructor() {\n        super(\n            'SPREAD_AMMO',\n            GAME_CONFIG.POWER_UP_COSTS.SPREAD_AMMO,\n            30000, // 30 seconds\n            'Fire projectiles in a spread pattern for better coverage'\n        );\n        \n        this.icon = '🔥';\n        this.color = '#ffaa00';\n        this.glowColor = '#ffdd44';\n    }\n    \n    applyEffect(playerShip) {\n        if (playerShip.weaponSystem) {\n            playerShip.weaponSystem.enableSpreadPattern(true);\n            return true;\n        }\n        return false;\n    }\n    \n    removeEffect(playerShip) {\n        if (playerShip.weaponSystem) {\n            playerShip.weaponSystem.enableSpreadPattern(false);\n            return true;\n        }\n        return false;\n    }\n}\n\n/**\n * Extra Wingman power-up - Adds a wingman ship that provides covering fire\n */\nexport class ExtraWingmanPowerUp extends PowerUp {\n    constructor() {\n        super(\n            'EXTRA_WINGMAN',\n            GAME_CONFIG.POWER_UP_COSTS.EXTRA_WINGMAN,\n            45000, // 45 seconds\n            'Deploy a wingman ship to provide covering fire'\n        );\n        \n        this.icon = '🚀';\n        this.color = '#00ff88';\n        this.glowColor = '#44ffaa';\n        this.wingmanShip = null;\n    }\n    \n    applyEffect(playerShip) {\n        // TODO: Implement wingman ship creation and management\n        // This would require a WingmanShip class and integration with the game object manager\n        console.log('Wingman power-up applied (implementation pending)');\n        return true;\n    }\n    \n    removeEffect(playerShip) {\n        // TODO: Remove wingman ship from game\n        if (this.wingmanShip) {\n            this.wingmanShip.destroy();\n            this.wingmanShip = null;\n        }\n        console.log('Wingman power-up removed (implementation pending)');\n        return true;\n    }\n}\n\n/**\n * PowerUp factory for creating power-up instances\n */\nexport class PowerUpFactory {\n    static createPowerUp(type) {\n        switch (type) {\n            case 'EXTRA_LIFE':\n                return new ExtraLifePowerUp();\n            case 'SPREAD_AMMO':\n                return new SpreadAmmoPowerUp();\n            case 'EXTRA_WINGMAN':\n                return new ExtraWingmanPowerUp();\n            default:\n                throw new Error(`Unknown power-up type: ${type}`);\n        }\n    }\n    \n    static getAllPowerUpTypes() {\n        return ['EXTRA_LIFE', 'SPREAD_AMMO', 'EXTRA_WINGMAN'];\n    }\n    \n    static createAllPowerUps() {\n        return this.getAllPowerUpTypes().map(type => this.createPowerUp(type));\n    }\n}\n", "import { PowerUpFactory } from '../systems/PowerUp.js';\n\n/**\n * GenieInterface - Modal interface for purchasing power-ups and reality warps between levels\n * Provides a mystical Genie-themed UI for player purchases using WISH tokens\n */\nexport class GenieInterface {\n    constructor(tokenManager, gameEngine) {\n        this.tokenManager = tokenManager;\n        this.gameEngine = gameEngine;\n        \n        // UI state\n        this.isVisible = false;\n        this.isInitialized = false;\n        this.container = null;\n        \n        // Available power-ups\n        this.availablePowerUps = PowerUpFactory.createAllPowerUps();\n        this.activePowerUps = new Map(); // Track active power-ups by type\n        \n        // Callbacks\n        this.onPowerUpPurchased = null;\n        this.onWarpPurchased = null;\n        this.onClose = null;\n        \n        // Animation state\n        this.animationFrame = null;\n        this.glowAnimation = 0;\n        \n        console.log('GenieInterface created');\n    }\n    \n    /**\n     * Initialize the Genie interface\n     */\n    async initialize() {\n        if (this.isInitialized) return;\n        \n        try {\n            // Create container element\n            this.container = document.createElement('div');\n            this.container.id = 'genie-interface';\n            this.container.className = 'genie-interface hidden';\n            \n            // Add to document body\n            document.body.appendChild(this.container);\n            \n            // Set up event listeners\n            this.setupEventListeners();\n            \n            this.isInitialized = true;\n            console.log('GenieInterface initialized successfully');\n            \n        } catch (error) {\n            console.error('GenieInterface initialization error:', error);\n            throw error;\n        }\n    }\n    \n    /**\n     * Show the Genie interface\n     */\n    show() {\n        if (!this.isInitialized) {\n            console.error('GenieInterface not initialized');\n            return;\n        }\n        \n        this.isVisible = true;\n        this.updatePowerUpAvailability();\n        this.render();\n        this.container.classList.remove('hidden');\n        this.container.classList.add('visible');\n        \n        // Start glow animation\n        this.startGlowAnimation();\n        \n        console.log('GenieInterface shown');\n    }\n    \n    /**\n     * Hide the Genie interface\n     */\n    hide() {\n        if (!this.isVisible) return;\n        \n        this.isVisible = false;\n        this.container.classList.remove('visible');\n        this.container.classList.add('hidden');\n        \n        // Stop glow animation\n        this.stopGlowAnimation();\n        \n        console.log('GenieInterface hidden');\n    }\n    \n    /**\n     * Update power-up availability based on current game state\n     */\n    updatePowerUpAvailability() {\n        const playerTokens = this.tokenManager.getBalance();\n        const playerShip = this.gameEngine.playerShip;\n        \n        this.availablePowerUps.forEach(powerUp => {\n            const availability = powerUp.canPurchase(playerShip, playerTokens);\n            powerUp.availability = availability;\n        });\n    }\n    \n    /**\n     * Render the Genie interface\n     */\n    render() {\n        if (!this.container) return;\n        \n        const playerTokens = this.tokenManager.getBalance();\n        \n        this.container.innerHTML = `\n            <div class=\"genie-modal\">\n                <div class=\"genie-backdrop\" id=\"genie-backdrop\"></div>\n                <div class=\"genie-content\">\n                    <div class=\"genie-header\">\n                        <div class=\"genie-character\">\n                            <div class=\"genie-lamp\">🪔</div>\n                            <div class=\"genie-smoke\"></div>\n                        </div>\n                        <h1 class=\"genie-title\">The Mystical Genie</h1>\n                        <p class=\"genie-subtitle\">Your wishes are my command, traveler...</p>\n                        <div class=\"token-display\">\n                            <span class=\"token-icon\">✨</span>\n                            <span class=\"token-amount\">${playerTokens}</span>\n                            <span class=\"token-label\">WISH Tokens</span>\n                        </div>\n                    </div>\n                    \n                    <div class=\"genie-body\">\n                        <div class=\"power-ups-section\">\n                            <h2 class=\"section-title\">Power-Up Enchantments</h2>\n                            <div class=\"power-ups-grid\">\n                                ${this.renderPowerUps()}\n                            </div>\n                        </div>\n                        \n                        <div class=\"reality-warp-section\">\n                            <h2 class=\"section-title\">Reality Warp Magic</h2>\n                            <div class=\"warp-options\">\n                                <div class=\"warp-card coming-soon\">\n                                    <div class=\"warp-icon\">🌌</div>\n                                    <h3>Reality Warp</h3>\n                                    <p>Transform the battlefield to your advantage</p>\n                                    <div class=\"warp-cost\">Coming Soon</div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                    \n                    <div class=\"genie-footer\">\n                        <button id=\"genie-close-btn\" class=\"genie-button secondary\">\n                            Continue Journey\n                        </button>\n                    </div>\n                </div>\n            </div>\n        `;\n        \n        // Set up button event listeners\n        this.setupButtonEventListeners();\n    }\n    \n    /**\n     * Render power-up cards\n     */\n    renderPowerUps() {\n        return this.availablePowerUps.map(powerUp => {\n            const availability = powerUp.availability || { canPurchase: false, reason: 'unknown' };\n            const isActive = this.activePowerUps.has(powerUp.type);\n            const canPurchase = availability.canPurchase && !isActive;\n            \n            let statusClass = '';\n            let statusText = '';\n            let buttonText = `Purchase (${powerUp.cost} ✨)`;\n            \n            if (isActive) {\n                statusClass = 'active';\n                statusText = 'Active';\n                buttonText = 'Already Active';\n            } else if (!availability.canPurchase) {\n                statusClass = 'unavailable';\n                if (availability.reason === 'insufficient_tokens') {\n                    statusText = 'Insufficient Tokens';\n                    buttonText = `Need ${powerUp.cost - this.tokenManager.getBalance()} more ✨`;\n                } else {\n                    statusText = 'Unavailable';\n                    buttonText = 'Cannot Purchase';\n                }\n            }\n            \n            return `\n                <div class=\"power-up-card ${statusClass}\" data-power-up=\"${powerUp.type}\">\n                    <div class=\"power-up-icon\">${powerUp.icon}</div>\n                    <h3 class=\"power-up-name\">${this.formatPowerUpName(powerUp.type)}</h3>\n                    <p class=\"power-up-description\">${powerUp.description}</p>\n                    <div class=\"power-up-details\">\n                        <div class=\"power-up-cost\">\n                            <span class=\"cost-amount\">${powerUp.cost}</span>\n                            <span class=\"cost-icon\">✨</span>\n                        </div>\n                        <div class=\"power-up-duration\">\n                            ${powerUp.duration ? `${Math.ceil(powerUp.duration / 1000)}s` : 'Permanent'}\n                        </div>\n                    </div>\n                    <div class=\"power-up-status\">${statusText}</div>\n                    <button class=\"power-up-button ${canPurchase ? 'primary' : 'disabled'}\" \n                            data-power-up=\"${powerUp.type}\"\n                            ${!canPurchase ? 'disabled' : ''}>\n                        ${buttonText}\n                    </button>\n                </div>\n            `;\n        }).join('');\n    }\n    \n    /**\n     * Format power-up type name for display\n     */\n    formatPowerUpName(type) {\n        switch (type) {\n            case 'EXTRA_LIFE':\n                return 'Extra Life';\n            case 'SPREAD_AMMO':\n                return 'Spread Ammo';\n            case 'EXTRA_WINGMAN':\n                return 'Extra Wingman';\n            default:\n                return type.replace(/_/g, ' ').toLowerCase()\n                    .replace(/\\b\\w/g, l => l.toUpperCase());\n        }\n    }\n    \n    /**\n     * Set up event listeners\n     */\n    setupEventListeners() {\n        // Handle escape key to close\n        document.addEventListener('keydown', (e) => {\n            if (e.key === 'Escape' && this.isVisible) {\n                this.close();\n            }\n        });\n    }\n    \n    /**\n     * Set up button event listeners after rendering\n     */\n    setupButtonEventListeners() {\n        // Close button\n        const closeBtn = this.container.querySelector('#genie-close-btn');\n        if (closeBtn) {\n            closeBtn.addEventListener('click', () => this.close());\n        }\n        \n        // Backdrop click to close\n        const backdrop = this.container.querySelector('#genie-backdrop');\n        if (backdrop) {\n            backdrop.addEventListener('click', () => this.close());\n        }\n        \n        // Power-up purchase buttons\n        const powerUpButtons = this.container.querySelectorAll('.power-up-button:not(.disabled)');\n        powerUpButtons.forEach(button => {\n            button.addEventListener('click', (e) => {\n                const powerUpType = e.target.dataset.powerUp;\n                this.handlePowerUpPurchase(powerUpType);\n            });\n        });\n    }\n    \n    /**\n     * Handle power-up purchase\n     */\n    handlePowerUpPurchase(powerUpType) {\n        const powerUp = this.availablePowerUps.find(p => p.type === powerUpType);\n        if (!powerUp) {\n            console.error('Power-up not found:', powerUpType);\n            return;\n        }\n        \n        // Check if purchase is valid\n        const playerTokens = this.tokenManager.getBalance();\n        const playerShip = this.gameEngine.playerShip;\n        const availability = powerUp.canPurchase(playerShip, playerTokens);\n        \n        if (!availability.canPurchase) {\n            console.warn('Cannot purchase power-up:', availability.reason);\n            return;\n        }\n        \n        // Spend tokens\n        const spendResult = this.tokenManager.spendTokens(powerUp.cost, `power_up_${powerUpType.toLowerCase()}`);\n        if (!spendResult.success) {\n            console.error('Failed to spend tokens:', spendResult.reason);\n            return;\n        }\n        \n        // Apply power-up\n        const applyResult = powerUp.apply(playerShip);\n        if (!applyResult) {\n            console.error('Failed to apply power-up');\n            // Refund tokens\n            this.tokenManager.awardTokens(powerUp.cost, 'power_up_refund');\n            return;\n        }\n        \n        // Track active power-up\n        this.activePowerUps.set(powerUpType, powerUp);\n        \n        // Update UI\n        this.updatePowerUpAvailability();\n        this.render();\n        \n        // Trigger callback\n        if (this.onPowerUpPurchased) {\n            this.onPowerUpPurchased(powerUp, spendResult);\n        }\n        \n        console.log(`Power-up purchased: ${powerUpType} for ${powerUp.cost} tokens`);\n    }\n    \n    /**\n     * Close the interface\n     */\n    close() {\n        this.hide();\n        \n        if (this.onClose) {\n            this.onClose();\n        }\n    }\n    \n    /**\n     * Start glow animation\n     */\n    startGlowAnimation() {\n        if (this.animationFrame) return;\n        \n        const animate = () => {\n            this.glowAnimation += 0.05;\n            \n            // Apply glow effect to lamp and tokens\n            const lamp = this.container.querySelector('.genie-lamp');\n            const tokenIcon = this.container.querySelector('.token-icon');\n            \n            if (lamp) {\n                const glow = Math.sin(this.glowAnimation) * 0.5 + 0.5;\n                lamp.style.filter = `drop-shadow(0 0 ${10 + glow * 10}px #ffd700)`;\n            }\n            \n            if (tokenIcon) {\n                const glow = Math.sin(this.glowAnimation + 1) * 0.5 + 0.5;\n                tokenIcon.style.filter = `drop-shadow(0 0 ${5 + glow * 5}px #00ffff)`;\n            }\n            \n            if (this.isVisible) {\n                this.animationFrame = requestAnimationFrame(animate);\n            }\n        };\n        \n        animate();\n    }\n    \n    /**\n     * Stop glow animation\n     */\n    stopGlowAnimation() {\n        if (this.animationFrame) {\n            cancelAnimationFrame(this.animationFrame);\n            this.animationFrame = null;\n        }\n    }\n    \n    /**\n     * Update active power-ups (call from game loop)\n     */\n    updateActivePowerUps(deltaTime) {\n        const playerShip = this.gameEngine.playerShip;\n        \n        for (const [type, powerUp] of this.activePowerUps) {\n            const stillActive = powerUp.update(deltaTime, playerShip);\n            \n            if (!stillActive) {\n                this.activePowerUps.delete(type);\n                console.log(`Power-up expired: ${type}`);\n            }\n        }\n    }\n    \n    /**\n     * Set callbacks\n     */\n    setOnPowerUpPurchased(callback) {\n        this.onPowerUpPurchased = callback;\n    }\n    \n    setOnWarpPurchased(callback) {\n        this.onWarpPurchased = callback;\n    }\n    \n    setOnClose(callback) {\n        this.onClose = callback;\n    }\n    \n    /**\n     * Cleanup resources\n     */\n    destroy() {\n        this.stopGlowAnimation();\n        \n        if (this.container && this.container.parentNode) {\n            this.container.parentNode.removeChild(this.container);\n        }\n        \n        this.container = null;\n        this.isInitialized = false;\n        \n        console.log('GenieInterface destroyed');\n    }\n}\n", "var e={GG_GET_GAME_DATA:\"GG_GET_GAME_DATA\",GG_UPDATE_GAME_DATA:\"GG_UPDATE_GAME_DATA\",GG_SET_GAME_DATA:\"GG_SET_GAME_DATA\",GG_PAUSED_FROM_GAME:\"GG_PAUSED_FROM_GAME\",GG_PAUSED_FROM_PARENT:\"GG_PAUSED_FROM_PARENT\",GG_QUIT_FROM_PARENT:\"GG_QUIT_FROM_PARENT\",GG_GAME_OVER:\"GG_GAME_OVER\",GG_RESUMED_FROM_GAME:\"GG_RESUMED_FROM_GAME\",GG_RESUMED_FROM_PARENT:\"GG_RESUMED_FROM_PARENT\",GG_GAME_LOAD_FINISHED:\"GG_GAME_LOAD_FINISHED\"},t=function(){function t(){this.registeredListeners=[]}return t.prototype.getTargetWindow=function(){try{if(window.top&&window.top!==window)return window.top}catch(e){console.warn(\"window.top access failed:\",e.message)}try{if(window.parent&&window.parent!==window)return window.parent}catch(e){console.warn(\"window.parent access failed:\",e.message)}return null},t.getInstance=function(){return t.instance||(t.instance=new t),t.instance},t.prototype.checkRegisteredListenersAndAdd=function(e,t){this.registeredListeners.includes(e)||(window.addEventListener(\"message\",t),this.registeredListeners.push(e))},t.prototype.registerListener=function(e,t){if(this.getTargetWindow()){this.checkRegisteredListenersAndAdd(e,function(n){n.data.event_type===e&&t()})}else console.error(\"Functions should be called from inside an iframe\")},t.prototype.getGameData=function(t,n){var o=this.getTargetWindow();if(o){var s=setTimeout(function(){n(t)},3e3);this.checkRegisteredListenersAndAdd(e.GG_SET_GAME_DATA,function(t){t.data.event_type===e.GG_SET_GAME_DATA&&(clearTimeout(s),n(t.data.payload.gameData))}),o.postMessage({event_type:e.GG_GET_GAME_DATA,payload:{defaultData:t}},\"*\")}else console.error(\"Functions should be called from inside an iframe\")},t.prototype.saveGameData=function(t){var n=this.getTargetWindow();n?n.postMessage({event_type:e.GG_UPDATE_GAME_DATA,payload:{data:t}},\"*\"):console.error(\"Functions should be called from inside an iframe\")},t.prototype.gameOver=function(t){var n=this.getTargetWindow();n?(console.log(\"sending game over to Goama\",t),n.postMessage({event_type:e.GG_GAME_OVER,payload:{score:t}},\"*\")):console.error(\"Functions should be called from inside an iframe\")},t.prototype.gamePaused=function(){var t=this.getTargetWindow();t?t.postMessage({event_type:e.GG_PAUSED_FROM_GAME},\"*\"):console.error(\"Functions should be called from inside an iframe\")},t.prototype.gameResumed=function(){var t=this.getTargetWindow();t?t.postMessage({event_type:e.GG_RESUMED_FROM_GAME},\"*\"):console.error(\"Functions should be called from inside an iframe\")},t.prototype.gameLoaded=function(){var t=this.getTargetWindow();t?t.postMessage({event_type:e.GG_GAME_LOAD_FINISHED},\"*\"):console.error(\"Functions should be called from inside an iframe\")},t.prototype.listenPaused=function(t){this.registerListener(e.GG_PAUSED_FROM_PARENT,t)},t.prototype.listenResumed=function(t){this.registerListener(e.GG_RESUMED_FROM_PARENT,t)},t.prototype.listenQuit=function(t){this.registerListener(e.GG_QUIT_FROM_PARENT,t)},t}(),n=t.getInstance();\"undefined\"!=typeof window&&(window.GGSDK=n);var o=n.getGameData,s=n.saveGameData,i=n.gameOver,a=n.gamePaused,r=n.gameResumed,_=n.gameLoaded,G=n.listenPaused,d=n.listenResumed,A=n.listenQuit;export{e as EVENT_TYPES,t as GGSDK,n as default,_ as gameLoaded,i as gameOver,a as gamePaused,r as gameResumed,o as getGameData,G as listenPaused,A as listenQuit,d as listenResumed,s as saveGameData};\n//# sourceMappingURL=index.esm.js.map\n", "import * as GGSDK from 'gg-game-sdk';\n\n/**\n * OrangeSDKManager handles data persistence to Orange SDK for tournaments\n * Only saves data - does not load progress (game starts fresh each time)\n * Tracks special statuses that can affect gameplay (e.g., login streaks, bonuses)\n */\nexport class OrangeSDKManager {\n    constructor() {\n        // SDK state\n        this.isSDKReady = false;\n        this.isInitialized = false;\n        \n        // Player progress data structure\n        this.playerData = {\n            // Core progress\n            totalScore: 0,\n            highestLevel: 0,\n            wishTokensEarned: 0,\n            wishTokensSpent: 0,\n            wishTokensBalance: 0,\n            \n            // Level completion tracking\n            levelsCompleted: [],\n            levelBestScores: {},\n            totalPlayTime: 0,\n            \n            // Performance metrics\n            perfectCompletions: 0,\n            totalEnemiesDefeated: 0,\n            averageCompletionTime: 0,\n            \n            // Special statuses (affects gameplay)\n            specialStatuses: {\n                loginStreak: 0,\n                lastLoginDate: null,\n                bonusLivesEarned: 0,\n                tournamentParticipant: false,\n                achievementUnlocks: []\n            },\n            \n            // Session data\n            currentSession: {\n                startTime: Date.now(),\n                levelsPlayedThisSession: 0,\n                tokensEarnedThisSession: 0,\n                scoreThisSession: 0\n            },\n            \n            // Metadata\n            lastSaveTime: Date.now(),\n            gameVersion: '1.0.0',\n            totalSessions: 0\n        };\n        \n        // Save operation state\n        this.saveInProgress = false;\n        this.pendingSaveData = null;\n        this.saveRetryCount = 0;\n        this.maxRetries = 3;\n        this.retryDelay = 1000; // 1 second\n        \n        // Callbacks\n        this.onDataSavedCallback = null;\n        this.onSaveErrorCallback = null;\n        this.onSpecialStatusCallback = null;\n        \n        // Auto-save configuration\n        this.autoSaveEnabled = true;\n        this.autoSaveInterval = 30000; // 30 seconds\n        this.lastAutoSave = Date.now();\n        \n        console.log('OrangeSDKManager initialized');\n    }\n    \n    /**\n     * Initialize the Orange SDK and set up event listeners\n     */\n    async initialize() {\n        if (this.isInitialized) return;\n        \n        try {\n            // Initialize session data\n            this.playerData.currentSession.startTime = Date.now();\n            this.playerData.totalSessions++;\n            \n            // Set up SDK event listeners\n            this.setupSDKEventListeners();\n            \n            // Check for special statuses on startup\n            await this.checkSpecialStatuses();\n            \n            // Send game loaded event\n            GGSDK.gameLoaded();\n            \n            this.isSDKReady = true;\n            this.isInitialized = true;\n            \n            console.log('OrangeSDKManager initialized successfully');\n            \n            // Perform initial save to establish baseline\n            await this.savePlayerData('game_start');\n            \n        } catch (error) {\n            console.error('Failed to initialize OrangeSDKManager:', error);\n            this.isSDKReady = false;\n        }\n    }\n    \n    /**\n     * Set up SDK event listeners for pause/resume/quit\n     */\n    setupSDKEventListeners() {\n        // Listen for pause events from parent\n        GGSDK.listenPaused(() => {\n            console.log('Game paused by parent window');\n            this.handleGamePause();\n        });\n        \n        // Listen for resume events from parent\n        GGSDK.listenResumed(() => {\n            console.log('Game resumed by parent window');\n            this.handleGameResume();\n        });\n        \n        // Listen for quit events from parent\n        GGSDK.listenQuit(() => {\n            console.log('Game quit requested by parent window');\n            this.handleGameQuit();\n        });\n    }\n    \n    /**\n     * Check for special statuses that affect gameplay\n     */\n    async checkSpecialStatuses() {\n        try {\n            // Get existing data to check login streak\n            const defaultData = { specialStatuses: this.playerData.specialStatuses };\n            \n            // This is the only time we read data - to check special statuses\n            GGSDK.getGameData(defaultData, (data) => {\n                if (data && data.specialStatuses) {\n                    const savedStatuses = data.specialStatuses;\n                    const today = new Date().toDateString();\n                    const lastLogin = savedStatuses.lastLoginDate;\n                    \n                    // Check login streak\n                    if (lastLogin) {\n                        const lastLoginDate = new Date(lastLogin);\n                        const daysDiff = Math.floor((Date.now() - lastLoginDate.getTime()) / (1000 * 60 * 60 * 24));\n                        \n                        if (daysDiff === 1) {\n                            // Consecutive day login\n                            this.playerData.specialStatuses.loginStreak = (savedStatuses.loginStreak || 0) + 1;\n                        } else if (daysDiff > 1) {\n                            // Streak broken\n                            this.playerData.specialStatuses.loginStreak = 1;\n                        } else {\n                            // Same day login\n                            this.playerData.specialStatuses.loginStreak = savedStatuses.loginStreak || 1;\n                        }\n                    } else {\n                        // First time login\n                        this.playerData.specialStatuses.loginStreak = 1;\n                    }\n                    \n                    // Update last login date\n                    this.playerData.specialStatuses.lastLoginDate = today;\n                    \n                    // Copy other special statuses\n                    this.playerData.specialStatuses.bonusLivesEarned = savedStatuses.bonusLivesEarned || 0;\n                    this.playerData.specialStatuses.tournamentParticipant = savedStatuses.tournamentParticipant || false;\n                    this.playerData.specialStatuses.achievementUnlocks = savedStatuses.achievementUnlocks || [];\n                    \n                    // Check for special status bonuses\n                    this.processSpecialStatusBonuses();\n                }\n            });\n            \n        } catch (error) {\n            console.error('Error checking special statuses:', error);\n        }\n    }\n    \n    /**\n     * Process special status bonuses that affect gameplay\n     */\n    processSpecialStatusBonuses() {\n        const streak = this.playerData.specialStatuses.loginStreak;\n        \n        // Award bonus lives for login streaks\n        if (streak >= 2 && streak <= 7) {\n            const bonusLives = Math.min(streak - 1, 3); // Max 3 bonus lives\n            this.playerData.specialStatuses.bonusLivesEarned = bonusLives;\n            \n            console.log(`Login streak bonus: ${bonusLives} extra lives for ${streak} day streak`);\n            \n            // Trigger callback for game to apply bonus\n            if (this.onSpecialStatusCallback) {\n                this.onSpecialStatusCallback('bonus_lives', bonusLives);\n            }\n        }\n        \n        // Other special status processing can be added here\n        if (this.playerData.specialStatuses.tournamentParticipant) {\n            console.log('Tournament participant status active');\n            if (this.onSpecialStatusCallback) {\n                this.onSpecialStatusCallback('tournament_participant', true);\n            }\n        }\n    }\n    \n    /**\n     * Update player progress data\n     * @param {object} progressData - Progress data to update\n     */\n    updatePlayerProgress(progressData) {\n        if (!progressData) return;\n        \n        // Update core progress\n        if (progressData.score !== undefined) {\n            this.playerData.totalScore = Math.max(this.playerData.totalScore, progressData.score);\n            this.playerData.currentSession.scoreThisSession += progressData.scoreGained || 0;\n        }\n        \n        if (progressData.level !== undefined) {\n            this.playerData.highestLevel = Math.max(this.playerData.highestLevel, progressData.level);\n        }\n        \n        if (progressData.tokensEarned !== undefined) {\n            this.playerData.wishTokensEarned += progressData.tokensEarned;\n            this.playerData.wishTokensBalance += progressData.tokensEarned;\n            this.playerData.currentSession.tokensEarnedThisSession += progressData.tokensEarned;\n        }\n        \n        if (progressData.tokensSpent !== undefined) {\n            this.playerData.wishTokensSpent += progressData.tokensSpent;\n            this.playerData.wishTokensBalance -= progressData.tokensSpent;\n        }\n        \n        // Update level completion\n        if (progressData.levelCompleted !== undefined) {\n            const level = progressData.levelCompleted;\n            if (!this.playerData.levelsCompleted.includes(level)) {\n                this.playerData.levelsCompleted.push(level);\n                this.playerData.currentSession.levelsPlayedThisSession++;\n            }\n            \n            // Update best score for level\n            if (progressData.levelScore !== undefined) {\n                const currentBest = this.playerData.levelBestScores[level] || 0;\n                this.playerData.levelBestScores[level] = Math.max(currentBest, progressData.levelScore);\n            }\n        }\n        \n        // Update performance metrics\n        if (progressData.perfectCompletion) {\n            this.playerData.perfectCompletions++;\n        }\n        \n        if (progressData.enemiesDefeated !== undefined) {\n            this.playerData.totalEnemiesDefeated += progressData.enemiesDefeated;\n        }\n        \n        if (progressData.completionTime !== undefined) {\n            // Update average completion time\n            const totalLevels = this.playerData.levelsCompleted.length;\n            if (totalLevels > 0) {\n                this.playerData.averageCompletionTime = \n                    ((this.playerData.averageCompletionTime * (totalLevels - 1)) + progressData.completionTime) / totalLevels;\n            }\n        }\n        \n        // Update play time\n        const currentTime = Date.now();\n        const sessionTime = currentTime - this.playerData.currentSession.startTime;\n        this.playerData.totalPlayTime += sessionTime;\n        this.playerData.currentSession.startTime = currentTime;\n        \n        console.log('Player progress updated:', progressData);\n    }\n    \n    /**\n     * Save player data to Orange SDK\n     * @param {string} reason - Reason for saving\n     * @param {boolean} force - Force save even if one is in progress\n     */\n    async savePlayerData(reason = 'manual', force = false) {\n        if (!this.isSDKReady) {\n            console.warn('SDK not ready, cannot save data');\n            return false;\n        }\n        \n        if (this.saveInProgress && !force) {\n            console.log('Save already in progress, queuing data');\n            this.pendingSaveData = { ...this.playerData };\n            return false;\n        }\n        \n        this.saveInProgress = true;\n        this.playerData.lastSaveTime = Date.now();\n        \n        const dataToSave = {\n            ...this.playerData,\n            saveReason: reason,\n            saveTimestamp: Date.now()\n        };\n        \n        try {\n            await this.performSaveWithRetry(dataToSave);\n            \n            console.log(`Player data saved successfully (${reason})`);\n            \n            // Trigger success callback\n            if (this.onDataSavedCallback) {\n                this.onDataSavedCallback(dataToSave, reason);\n            }\n            \n            // Process any pending save\n            if (this.pendingSaveData) {\n                const pendingData = this.pendingSaveData;\n                this.pendingSaveData = null;\n                setTimeout(() => this.savePlayerData('pending_update'), 100);\n            }\n            \n            return true;\n            \n        } catch (error) {\n            console.error('Failed to save player data:', error);\n            \n            // Trigger error callback\n            if (this.onSaveErrorCallback) {\n                this.onSaveErrorCallback(error, reason);\n            }\n            \n            return false;\n        } finally {\n            this.saveInProgress = false;\n            this.saveRetryCount = 0;\n        }\n    }\n\n    /**\n     * Perform save operation with retry logic\n     * @param {object} dataToSave - Data to save\n     */\n    async performSaveWithRetry(dataToSave) {\n        for (let attempt = 0; attempt <= this.maxRetries; attempt++) {\n            try {\n                // Use Promise wrapper for SDK save operation\n                await new Promise((resolve, reject) => {\n                    try {\n                        GGSDK.saveGameData(dataToSave);\n                        // SDK doesn't provide callback, assume success after short delay\n                        setTimeout(resolve, 100);\n                    } catch (error) {\n                        reject(error);\n                    }\n                });\n\n                return; // Success\n\n            } catch (error) {\n                this.saveRetryCount = attempt + 1;\n\n                if (attempt < this.maxRetries) {\n                    console.warn(`Save attempt ${attempt + 1} failed, retrying in ${this.retryDelay}ms:`, error);\n                    await new Promise(resolve => setTimeout(resolve, this.retryDelay));\n                    this.retryDelay *= 2; // Exponential backoff\n                } else {\n                    throw error; // Final attempt failed\n                }\n            }\n        }\n    }\n\n    /**\n     * Handle level completion - automatic save trigger\n     * @param {object} completionData - Level completion data\n     */\n    async onLevelCompleted(completionData) {\n        if (!completionData) return;\n\n        // Update progress data\n        this.updatePlayerProgress({\n            level: completionData.levelNumber,\n            levelCompleted: completionData.levelNumber,\n            levelScore: completionData.score?.totalScore || 0,\n            scoreGained: completionData.score?.totalScore || 0,\n            tokensEarned: completionData.score?.tokenReward || 0,\n            perfectCompletion: completionData.perfectCompletion,\n            enemiesDefeated: completionData.enemiesDefeated,\n            completionTime: completionData.completionTime\n        });\n\n        // Send game over event to SDK with score\n        const finalScore = completionData.score?.totalScore || 0;\n        GGSDK.gameOver(finalScore);\n\n        // Auto-save progress\n        await this.savePlayerData('level_completed');\n\n        console.log(`Level ${completionData.levelNumber} completion saved to Orange SDK`);\n    }\n\n    /**\n     * Handle token changes - automatic save trigger\n     * @param {object} tokenData - Token change data\n     */\n    async onTokensChanged(tokenData) {\n        if (!tokenData) return;\n\n        // Update progress data\n        this.updatePlayerProgress({\n            tokensEarned: tokenData.earned || 0,\n            tokensSpent: tokenData.spent || 0\n        });\n\n        // Auto-save if significant token change\n        const significantChange = (tokenData.earned || 0) + (tokenData.spent || 0) >= 50;\n        if (significantChange) {\n            await this.savePlayerData('token_change');\n        }\n    }\n\n    /**\n     * Handle game pause event\n     */\n    handleGamePause() {\n        // Send pause event to SDK\n        GGSDK.gamePaused();\n\n        // Save current progress\n        this.savePlayerData('game_paused');\n    }\n\n    /**\n     * Handle game resume event\n     */\n    handleGameResume() {\n        // Send resume event to SDK\n        GGSDK.gameResumed();\n\n        // Update session start time\n        this.playerData.currentSession.startTime = Date.now();\n    }\n\n    /**\n     * Handle game quit event\n     */\n    async handleGameQuit() {\n        // Update final session data\n        const sessionTime = Date.now() - this.playerData.currentSession.startTime;\n        this.playerData.totalPlayTime += sessionTime;\n\n        // Send final game over event\n        GGSDK.gameOver(this.playerData.totalScore);\n\n        // Final save before quit\n        await this.savePlayerData('game_quit');\n\n        console.log('Game quit - final data saved to Orange SDK');\n    }\n\n    /**\n     * Update auto-save if enabled and interval has passed\n     */\n    updateAutoSave() {\n        if (!this.autoSaveEnabled || !this.isSDKReady) return;\n\n        const now = Date.now();\n        if (now - this.lastAutoSave >= this.autoSaveInterval) {\n            this.savePlayerData('auto_save');\n            this.lastAutoSave = now;\n        }\n    }\n\n    /**\n     * Get current player data (for debugging/display)\n     * @returns {object} Current player data\n     */\n    getPlayerData() {\n        return { ...this.playerData };\n    }\n\n    /**\n     * Get special status information\n     * @returns {object} Special status data\n     */\n    getSpecialStatuses() {\n        return { ...this.playerData.specialStatuses };\n    }\n\n    /**\n     * Add achievement unlock\n     * @param {string} achievementId - Achievement identifier\n     */\n    unlockAchievement(achievementId) {\n        if (!this.playerData.specialStatuses.achievementUnlocks.includes(achievementId)) {\n            this.playerData.specialStatuses.achievementUnlocks.push(achievementId);\n            this.savePlayerData('achievement_unlock');\n\n            console.log(`Achievement unlocked: ${achievementId}`);\n\n            if (this.onSpecialStatusCallback) {\n                this.onSpecialStatusCallback('achievement_unlock', achievementId);\n            }\n        }\n    }\n\n    /**\n     * Set tournament participant status\n     * @param {boolean} isParticipant - Tournament participation status\n     */\n    setTournamentParticipant(isParticipant) {\n        this.playerData.specialStatuses.tournamentParticipant = isParticipant;\n        this.savePlayerData('tournament_status');\n\n        if (this.onSpecialStatusCallback) {\n            this.onSpecialStatusCallback('tournament_participant', isParticipant);\n        }\n    }\n\n    /**\n     * Set callback for when data is saved\n     * @param {function} callback - Callback function\n     */\n    setOnDataSavedCallback(callback) {\n        this.onDataSavedCallback = callback;\n    }\n\n    /**\n     * Set callback for save errors\n     * @param {function} callback - Callback function\n     */\n    setOnSaveErrorCallback(callback) {\n        this.onSaveErrorCallback = callback;\n    }\n\n    /**\n     * Set callback for special status changes\n     * @param {function} callback - Callback function\n     */\n    setOnSpecialStatusCallback(callback) {\n        this.onSpecialStatusCallback = callback;\n    }\n\n    /**\n     * Enable or disable auto-save\n     * @param {boolean} enabled - Auto-save enabled state\n     * @param {number} interval - Auto-save interval in milliseconds\n     */\n    setAutoSave(enabled, interval = 30000) {\n        this.autoSaveEnabled = enabled;\n        this.autoSaveInterval = interval;\n\n        if (enabled) {\n            this.lastAutoSave = Date.now();\n        }\n\n        console.log(`Auto-save ${enabled ? 'enabled' : 'disabled'} with ${interval}ms interval`);\n    }\n\n    /**\n     * Get save statistics\n     * @returns {object} Save operation statistics\n     */\n    getSaveStatistics() {\n        return {\n            isSDKReady: this.isSDKReady,\n            saveInProgress: this.saveInProgress,\n            saveRetryCount: this.saveRetryCount,\n            lastSaveTime: this.playerData.lastSaveTime,\n            autoSaveEnabled: this.autoSaveEnabled,\n            autoSaveInterval: this.autoSaveInterval,\n            lastAutoSave: this.lastAutoSave\n        };\n    }\n\n    /**\n     * Reset manager state (for testing)\n     */\n    reset() {\n        this.playerData = {\n            totalScore: 0,\n            highestLevel: 0,\n            wishTokensEarned: 0,\n            wishTokensSpent: 0,\n            wishTokensBalance: 0,\n            levelsCompleted: [],\n            levelBestScores: {},\n            totalPlayTime: 0,\n            perfectCompletions: 0,\n            totalEnemiesDefeated: 0,\n            averageCompletionTime: 0,\n            specialStatuses: {\n                loginStreak: 0,\n                lastLoginDate: null,\n                bonusLivesEarned: 0,\n                tournamentParticipant: false,\n                achievementUnlocks: []\n            },\n            currentSession: {\n                startTime: Date.now(),\n                levelsPlayedThisSession: 0,\n                tokensEarnedThisSession: 0,\n                scoreThisSession: 0\n            },\n            lastSaveTime: Date.now(),\n            gameVersion: '1.0.0',\n            totalSessions: 0\n        };\n\n        this.saveInProgress = false;\n        this.pendingSaveData = null;\n        this.saveRetryCount = 0;\n        this.retryDelay = 1000;\n\n        console.log('OrangeSDKManager reset');\n    }\n}\n", "import { InputManager } from '../input/InputManager.js';\nimport { PlayerShip } from '../entities/PlayerShip.js';\nimport { GameObjectManager } from '../utils/GameObjectManager.js';\nimport { LevelManager } from '../managers/LevelManager.js';\nimport { EnemyManager } from '../managers/EnemyManager.js';\nimport { TokenEconomyManager } from '../managers/TokenEconomyManager.js';\nimport { GenieInterface } from '../ui/GenieInterface.js';\nimport { OrangeSDKManager } from '../managers/OrangeSDKManager.js';\n\n/**\n * Core Game Engine - Main game loop and state management\n * Implements fixed timestep game loop with 60 FPS target\n */\nexport class GameEngine {\n    constructor(canvas, uiElement) {\n        this.canvas = canvas;\n        this.ctx = canvas.getContext('2d');\n        this.uiElement = uiElement;\n        this.stars = [];\n        \n        // Game state\n        this.isRunning = false;\n        this.isPaused = false;\n        \n        // Fixed timestep configuration\n        this.targetFPS = 60;\n        this.fixedTimeStep = 1000 / this.targetFPS; // 16.67ms per frame\n        this.maxFrameTime = 250; // Maximum frame time to prevent spiral of death\n        \n        // Timing variables\n        this.lastFrameTime = 0;\n        this.accumulator = 0;\n        this.currentTime = 0;\n        \n        // Performance tracking\n        this.frameCount = 0;\n        this.fpsTimer = 0;\n        this.currentFPS = 0;\n        \n        // Bind methods\n        this.gameLoop = this.gameLoop.bind(this);\n        this.handleResize = this.handleResize.bind(this);\n    }\n    \n    async init() {\n        console.log('Initializing WarpSpace Game Engine...');\n        \n        // Set up canvas properties\n        this.setupCanvas();\n        \n        // Initialize game systems (placeholder for now)\n        await this.initializeSystems();\n        \n        // Start the game loop\n        this.start();\n        \n        return Promise.resolve();\n    }\n    \n    setupCanvas() {\n        // Set up canvas for crisp pixel rendering\n        this.ctx.imageSmoothingEnabled = false;\n        \n        // Set canvas size based on container\n        this.handleResize();\n        window.addEventListener('resize', this.handleResize);\n        \n        console.log(`Canvas initialized: ${this.canvas.width}x${this.canvas.height}`);\n    }\n    \n    initializeStarfield() {\n        const starCount = 200;\n        for (let i = 0; i < starCount; i++) {\n            this.stars.push({\n                x: Math.random() * this.canvas.width,\n                y: Math.random() * this.canvas.height,\n                size: Math.random() * 1.5 + 0.5,\n                speed: (Math.random() * 25) + 5 // pixels per second\n            });\n        }\n    }\n    \n    async initializeSystems() {\n        // Initialize input manager\n        this.inputManager = new InputManager(this.canvas);\n\n        // Initialize game object manager\n        this.gameObjectManager = new GameObjectManager();\n\n        // Initialize player ship at bottom center of screen\n        const startX = this.canvas.width / 2;\n        const startY = this.canvas.height - 100; // 100 pixels from bottom\n        this.playerShip = new PlayerShip(startX, startY, this.canvas.width, this.canvas.height, this.gameObjectManager);\n\n        // Initialize level manager\n        this.levelManager = new LevelManager(this.gameObjectManager);\n\n        // Initialize enemy manager\n        this.enemyManager = new EnemyManager(this.canvas.width, this.canvas.height, this.gameObjectManager);\n\n        // Initialize token economy manager\n        this.tokenManager = new TokenEconomyManager();\n\n        // Initialize Orange SDK manager\n        this.orangeSDKManager = new OrangeSDKManager();\n\n        // Initialize Genie interface\n        this.genieInterface = new GenieInterface(this.tokenManager, this);\n\n        // Set up level manager callbacks\n        this.setupLevelManagerCallbacks();\n\n        // Set up enemy manager callbacks\n        this.setupEnemyManagerCallbacks();\n\n        // Set up Orange SDK manager callbacks\n        this.setupOrangeSDKCallbacks();\n\n        // Set up token manager callbacks\n        this.setupTokenManagerCallbacks();\n\n        // Initialize Genie interface\n        await this.genieInterface.initialize();\n        this.setupGenieInterfaceCallbacks();\n\n        // Initialize Orange SDK (async)\n        this.initializeOrangeSDK();\n\n        // Initialize starfield\n        this.initializeStarfield();\n\n        console.log('Game systems initialized');\n\n        // Note: Level is NOT started here - it will be started when player clicks \"Start Game\"\n    }\n    \n    start() {\n        if (!this.isRunning) {\n            this.isRunning = true;\n            this.isPaused = false;\n            this.currentTime = performance.now();\n            this.lastFrameTime = this.currentTime;\n            this.accumulator = 0;\n            this.frameCount = 0;\n            this.fpsTimer = 0;\n            requestAnimationFrame(this.gameLoop);\n            console.log('Game loop started with fixed timestep');\n        }\n    }\n\n    /**\n     * Start the actual gameplay (called when player clicks \"Start Game\")\n     * @param {object} user - Authenticated user data\n     */\n    startGame(user = null) {\n        console.log('Starting gameplay for user:', user);\n\n        // Start the first level\n        if (this.levelManager) {\n            this.levelManager.startLevel(1);\n        }\n\n        console.log('Game started - Level 1 initialized');\n    }\n    \n    pause() {\n        this.isPaused = true;\n        console.log('Game paused');\n\n        // Notify Orange SDK of pause\n        if (this.orangeSDKManager) {\n            this.orangeSDKManager.handleGamePause();\n        }\n    }\n\n    resume() {\n        if (this.isPaused) {\n            this.isPaused = false;\n            // Reset timing to prevent large delta time jump\n            this.currentTime = performance.now();\n            this.lastFrameTime = this.currentTime;\n            this.accumulator = 0;\n            console.log('Game resumed');\n\n            // Notify Orange SDK of resume\n            if (this.orangeSDKManager) {\n                this.orangeSDKManager.handleGameResume();\n            }\n        }\n    }\n\n    async destroy() {\n        this.isRunning = false;\n\n        // Handle Orange SDK quit before cleanup\n        if (this.orangeSDKManager) {\n            await this.orangeSDKManager.handleGameQuit();\n        }\n\n        // Cleanup input manager\n        if (this.inputManager) {\n            this.inputManager.destroy();\n        }\n\n        console.log('Game engine destroyed');\n    }\n    \n    gameLoop(currentTime) {\n        if (!this.isRunning) return;\n        \n        // Calculate frame time and clamp to prevent spiral of death\n        let frameTime = currentTime - this.lastFrameTime;\n        if (frameTime > this.maxFrameTime) {\n            frameTime = this.maxFrameTime;\n        }\n        \n        this.lastFrameTime = currentTime;\n        \n        if (!this.isPaused) {\n            // Add frame time to accumulator\n            this.accumulator += frameTime;\n            \n            // Fixed timestep updates - run as many updates as needed\n            while (this.accumulator >= this.fixedTimeStep) {\n                this.update(this.fixedTimeStep);\n                this.accumulator -= this.fixedTimeStep;\n            }\n            \n            // Calculate interpolation factor for smooth rendering\n            const interpolation = this.accumulator / this.fixedTimeStep;\n            \n            // Always render (variable timestep for smooth visuals)\n            this.render(interpolation);\n            \n            // Update FPS counter\n            this.updateFPSCounter(frameTime);\n        }\n        \n        requestAnimationFrame(this.gameLoop);\n    }\n    \n    update(deltaTime) {\n        this.updateStarfield(deltaTime);\n        \n        // Update input manager\n        if (this.inputManager) {\n            this.inputManager.update();\n        }\n        \n        // Update player ship with movement input (handle separately from GameObjectManager)\n        if (this.playerShip && this.inputManager) {\n            const movementInput = this.inputManager.getMovementVector();\n            this.playerShip.update(deltaTime, movementInput);\n            \n            // Handle weapon firing input\n            if (this.inputManager.isActionDown('fire')) {\n                this.playerShip.fire();\n            }\n            \n            // Debug: Test damage system (D key)\n            if (this.inputManager.isKeyPressed('KeyD')) {\n                this.playerShip.takeDamage(25); // Take 25 damage for testing\n            }\n            \n            // Debug: Test healing system (H key)\n            if (this.inputManager.isKeyPressed('KeyH')) {\n                this.playerShip.heal(25); // Heal 25 health for testing\n            }\n            \n            // Debug: Add extra life (L key)\n            if (this.inputManager.isKeyPressed('KeyL')) {\n                this.playerShip.addLives(1); // Add 1 life for testing\n            }\n\n            // Debug: Add tokens (T key)\n            if (this.inputManager.isKeyPressed('KeyT')) {\n                this.tokenManager.awardTokens(500, 'debug_tokens');\n            }\n\n            // Debug: Show Genie interface (G key)\n            if (this.inputManager.isKeyPressed('KeyG')) {\n                this.showGenieInterface({ levelNumber: 1, nextLevel: 2 });\n            }\n        }\n\n        // Update active power-ups\n        if (this.genieInterface) {\n            this.genieInterface.updateActivePowerUps(deltaTime);\n        }\n        \n        // Update level manager\n        if (this.levelManager) {\n            const gameState = {\n                playerDestroyed: this.playerShip ? this.playerShip.getHealthStatus().isDestroyed : false,\n                playerDamageTaken: false, // This would be set by damage events\n                shotsFired: 0, // This would be tracked by weapon system\n                shotsHit: 0 // This would be tracked by collision system\n            };\n            this.levelManager.update(deltaTime, gameState);\n        }\n        \n        // Update enemy manager (only if level is in progress)\n        if (this.enemyManager && this.playerShip && this.levelManager && this.levelManager.levelInProgress) {\n            const playerPosition = this.playerShip.position;\n            this.enemyManager.update(deltaTime, playerPosition);\n\n            // Handle collisions\n            this.handleCollisions();\n        }\n        \n        // Update all other game objects (projectiles, enemies, etc.)\n        if (this.gameObjectManager) {\n            this.gameObjectManager.update(deltaTime);\n        }\n        \n        // Clean up out-of-bounds projectiles\n        this.cleanupProjectiles();\n\n        // Update Orange SDK auto-save\n        if (this.orangeSDKManager) {\n            this.orangeSDKManager.updateAutoSave();\n        }\n    }\n    \n    /**\n     * Clean up projectiles that are out of bounds\n     */\n    cleanupProjectiles() {\n        if (!this.gameObjectManager) return;\n        \n        const bounds = {\n            left: -50,\n            right: this.canvas.width + 50,\n            top: -50,\n            bottom: this.canvas.height + 50\n        };\n        \n        const projectiles = this.gameObjectManager.findByTag('projectile');\n        for (const projectile of projectiles) {\n            if (projectile.isOutOfBounds(bounds)) {\n                this.gameObjectManager.returnToPool('projectile', projectile);\n            }\n        }\n    }\n    \n    updateStarfield(deltaTime) {\n        const dtSeconds = deltaTime / 1000;\n        for (const star of this.stars) {\n            star.y += star.speed * dtSeconds;\n            if (star.y > this.canvas.height) {\n                star.y = 0;\n                star.x = Math.random() * this.canvas.width;\n            }\n        }\n    }\n    \n    render(interpolation = 0) {\n        // Clear canvas\n        this.ctx.fillStyle = '#000011';\n        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n        \n        // Render basic space background\n        this.renderStarField();\n        \n        // Render player ship separately\n        if (this.playerShip) {\n            this.playerShip.render(this.ctx, interpolation);\n        }\n        \n        // Render enemy manager (enemies and their projectiles)\n        if (this.enemyManager) {\n            this.enemyManager.render(this.ctx, interpolation);\n        }\n        \n        // Render all other game objects (projectiles, enemies, etc.)\n        if (this.gameObjectManager) {\n            this.gameObjectManager.render(this.ctx, interpolation);\n        }\n        \n        // Game title (smaller and positioned at top)\n        this.ctx.fillStyle = '#ffffff';\n        this.ctx.font = '20px Arial';\n        this.ctx.textAlign = 'center';\n        this.ctx.fillText('WarpSpace', this.canvas.width / 2, 30);\n        \n        // Display input debug info\n        if (this.inputManager) {\n            this.renderInputDebug();\n            \n            // Render input manager (virtual joystick, etc.)\n            this.inputManager.render(this.ctx);\n        }\n        \n        // Display FPS counter\n        this.ctx.font = '12px Arial';\n        this.ctx.textAlign = 'left';\n        this.ctx.fillText(`FPS: ${this.currentFPS}`, 10, 20);\n        \n        // Display health and lives UI\n        this.renderHealthAndLivesUI();\n        \n        // Display level and score UI\n        this.renderLevelAndScoreUI();\n        \n        // Display token balance and reward animations\n        if (this.tokenManager) {\n            this.tokenManager.render(this.ctx, this.fixedTimeStep);\n        }\n    }\n    \n    renderInputDebug() {\n        if (!this.inputManager) return;\n        \n        // Display movement input\n        const movement = this.inputManager.getMovementVector();\n        this.ctx.font = '12px Arial';\n        this.ctx.textAlign = 'left';\n        this.ctx.fillStyle = '#ffffff';\n        this.ctx.fillText(`Movement: ${movement.x.toFixed(2)}, ${movement.y.toFixed(2)}`, 10, 40);\n        \n        // Display active actions\n        const actions = ['fire', 'pause', 'interact'];\n        let yOffset = 60;\n        \n        for (const action of actions) {\n            if (this.inputManager.isActionDown(action)) {\n                this.ctx.fillStyle = '#00ff00';\n                this.ctx.fillText(`${action.toUpperCase()}: ACTIVE`, 10, yOffset);\n            } else {\n                this.ctx.fillStyle = '#666666';\n                this.ctx.fillText(`${action}: inactive`, 10, yOffset);\n            }\n            yOffset += 15;\n        }\n        \n        // Display mouse position\n        this.ctx.fillStyle = '#ffffff';\n        this.ctx.fillText(`Mouse: ${this.inputManager.mousePosition.x.toFixed(0)}, ${this.inputManager.mousePosition.y.toFixed(0)}`, 10, yOffset + 10);\n        \n        // Display touch info for mobile\n        if (this.inputManager.isTouchDevice) {\n            this.ctx.fillText(`Touch Device: ${this.inputManager.touches.size} touches`, 10, yOffset + 25);\n        }\n        \n        // Display game object manager stats\n        if (this.gameObjectManager) {\n            const stats = this.gameObjectManager.getStats();\n            this.ctx.fillText(`Objects: ${stats.totalObjects} (${stats.activeObjects} active)`, 10, yOffset + 40);\n            \n            const projectiles = this.gameObjectManager.findByTag('projectile');\n            this.ctx.fillText(`Projectiles: ${projectiles.length}`, 10, yOffset + 55);\n        }\n    }\n    \n    updateFPSCounter(frameTime) {\n        this.frameCount++;\n        this.fpsTimer += frameTime;\n        \n        // Update FPS display every second\n        if (this.fpsTimer >= 1000) {\n            this.currentFPS = Math.round((this.frameCount * 1000) / this.fpsTimer);\n            this.frameCount = 0;\n            this.fpsTimer = 0;\n        }\n    }\n    \n    renderStarField() {\n        // Simple star field effect\n        this.ctx.fillStyle = '#ffffff';\n        for (const star of this.stars) {\n            this.ctx.fillRect(star.x, star.y, star.size, star.size);\n        }\n    }\n    \n    /**\n     * Render health bar and lives counter UI\n     */\n    renderHealthAndLivesUI() {\n        if (!this.playerShip) return;\n        \n        const healthStatus = this.playerShip.getHealthStatus();\n        \n        // Health bar position (top-right corner)\n        const healthBarX = this.canvas.width - 220;\n        const healthBarY = 15;\n        const healthBarWidth = 200;\n        const healthBarHeight = 20;\n        \n        // Health bar background\n        this.ctx.fillStyle = '#333333';\n        this.ctx.fillRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight);\n        \n        // Health bar border\n        this.ctx.strokeStyle = '#666666';\n        this.ctx.lineWidth = 2;\n        this.ctx.strokeRect(healthBarX, healthBarY, healthBarWidth, healthBarHeight);\n        \n        // Health bar fill\n        const healthWidth = healthBarWidth * healthStatus.healthPercentage;\n        let healthColor = '#00ff00'; // Green for healthy\n        \n        if (healthStatus.healthPercentage < 0.3) {\n            healthColor = '#ff0000'; // Red for critical\n        } else if (healthStatus.healthPercentage < 0.6) {\n            healthColor = '#ffaa00'; // Orange for damaged\n        }\n        \n        this.ctx.fillStyle = healthColor;\n        this.ctx.fillRect(healthBarX + 2, healthBarY + 2, healthWidth - 4, healthBarHeight - 4);\n        \n        // Health text\n        this.ctx.fillStyle = '#ffffff';\n        this.ctx.font = '12px Arial';\n        this.ctx.textAlign = 'center';\n        this.ctx.fillText(\n            `${healthStatus.health}/${healthStatus.maxHealth}`, \n            healthBarX + healthBarWidth / 2, \n            healthBarY + healthBarHeight / 2 + 4\n        );\n        \n        // Lives counter (below health bar)\n        const livesY = healthBarY + healthBarHeight + 25;\n        this.ctx.textAlign = 'right';\n        this.ctx.fillStyle = '#ffffff';\n        this.ctx.font = '16px Arial';\n        this.ctx.fillText(`Lives: ${healthStatus.lives}`, this.canvas.width - 20, livesY);\n        \n        // Draw life icons\n        const lifeIconSize = 16;\n        const lifeIconSpacing = 20;\n        const lifeIconStartX = this.canvas.width - 20 - (healthStatus.lives * lifeIconSpacing);\n        \n        for (let i = 0; i < healthStatus.lives; i++) {\n            const iconX = lifeIconStartX + (i * lifeIconSpacing);\n            const iconY = livesY + 10;\n            \n            // Draw small ship icon for each life\n            this.ctx.fillStyle = '#4A90E2';\n            this.ctx.beginPath();\n            this.ctx.moveTo(iconX, iconY - lifeIconSize / 2);\n            this.ctx.lineTo(iconX - lifeIconSize / 3, iconY + lifeIconSize / 3);\n            this.ctx.lineTo(iconX + lifeIconSize / 3, iconY + lifeIconSize / 3);\n            this.ctx.closePath();\n            this.ctx.fill();\n        }\n        \n        // Show invulnerability status\n        if (healthStatus.isInvulnerable) {\n            this.ctx.fillStyle = '#ffff00';\n            this.ctx.font = '12px Arial';\n            this.ctx.textAlign = 'right';\n            const invulnTime = (healthStatus.invulnerabilityTimeRemaining / 1000).toFixed(1);\n            this.ctx.fillText(`Invulnerable: ${invulnTime}s`, this.canvas.width - 20, livesY + 50);\n        }\n        \n        // Show game over message if destroyed\n        if (healthStatus.isDestroyed) {\n            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';\n            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n            \n            this.ctx.fillStyle = '#ff0000';\n            this.ctx.font = '48px Arial';\n            this.ctx.textAlign = 'center';\n            this.ctx.fillText('GAME OVER', this.canvas.width / 2, this.canvas.height / 2);\n            \n            this.ctx.fillStyle = '#ffffff';\n            this.ctx.font = '24px Arial';\n            this.ctx.fillText('No lives remaining', this.canvas.width / 2, this.canvas.height / 2 + 50);\n        }\n    }\n    \n    /**\n     * Render level and score UI\n     */\n    renderLevelAndScoreUI() {\n        if (!this.levelManager) return;\n        \n        const levelStatus = this.levelManager.getLevelStatus();\n        \n        // Level information (top-left corner)\n        this.ctx.fillStyle = '#ffffff';\n        this.ctx.font = '16px Arial';\n        this.ctx.textAlign = 'left';\n        this.ctx.fillText(`Level: ${levelStatus.currentLevel}`, 10, 120);\n        \n        // Score display\n        this.ctx.fillText(`Score: ${levelStatus.score.current.toLocaleString()}`, 10, 140);\n        this.ctx.font = '12px Arial';\n        this.ctx.fillText(`Level Score: ${levelStatus.score.level.toLocaleString()}`, 10, 155);\n        \n        // Progress display\n        if (levelStatus.levelInProgress) {\n            const progressText = `Enemies: ${levelStatus.progress.enemiesDefeated}/${levelStatus.progress.requiredEnemies}`;\n            this.ctx.fillText(progressText, 10, 175);\n            \n            const waveText = `Waves: ${levelStatus.progress.wavesCompleted}/${levelStatus.progress.requiredWaves}`;\n            this.ctx.fillText(waveText, 10, 190);\n            \n            // Level timer\n            const timeElapsed = (levelStatus.completionTime / 1000).toFixed(1);\n            this.ctx.fillText(`Time: ${timeElapsed}s`, 10, 205);\n            \n            // Time limit warning\n            if (levelStatus.levelConfig && levelStatus.levelConfig.timeLimit) {\n                const timeRemaining = levelStatus.levelConfig.timeLimit - (levelStatus.completionTime / 1000);\n                if (timeRemaining <= 30 && timeRemaining > 0) {\n                    this.ctx.fillStyle = '#ff6600';\n                    this.ctx.fillText(`Time Remaining: ${timeRemaining.toFixed(1)}s`, 10, 220);\n                } else if (timeRemaining <= 10 && timeRemaining > 0) {\n                    this.ctx.fillStyle = '#ff0000';\n                    this.ctx.fillText(`TIME CRITICAL: ${timeRemaining.toFixed(1)}s`, 10, 220);\n                }\n            }\n        }\n        \n        // Performance indicators\n        if (levelStatus.performance.perfectCompletion) {\n            this.ctx.fillStyle = '#00ff00';\n            this.ctx.font = '10px Arial';\n            this.ctx.fillText('PERFECT RUN', 10, 240);\n        }\n        \n        // Environment display\n        if (levelStatus.levelConfig && levelStatus.levelConfig.environment) {\n            this.ctx.fillStyle = '#cccccc';\n            this.ctx.font = '12px Arial';\n            const envText = `Environment: ${levelStatus.levelConfig.environment.toUpperCase()}`;\n            this.ctx.fillText(envText, 10, 260);\n        }\n        \n        // Level completion overlay\n        if (!levelStatus.levelInProgress && levelStatus.levelConfig) {\n            this.renderLevelCompletionOverlay(levelStatus);\n        }\n    }\n    \n    /**\n     * Render level completion overlay\n     * @param {object} levelStatus - Current level status\n     */\n    renderLevelCompletionOverlay(levelStatus) {\n        // Semi-transparent overlay\n        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';\n        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n        \n        // Level complete text\n        this.ctx.fillStyle = '#00ff00';\n        this.ctx.font = '36px Arial';\n        this.ctx.textAlign = 'center';\n        this.ctx.fillText(`Level ${levelStatus.currentLevel} Complete!`, this.canvas.width / 2, this.canvas.height / 2 - 50);\n        \n        // Score breakdown\n        this.ctx.fillStyle = '#ffffff';\n        this.ctx.font = '18px Arial';\n        this.ctx.fillText(`Final Score: ${levelStatus.score.level.toLocaleString()}`, this.canvas.width / 2, this.canvas.height / 2);\n        \n        this.ctx.font = '14px Arial';\n        this.ctx.fillText(`Enemies Defeated: ${levelStatus.progress.enemiesDefeated}`, this.canvas.width / 2, this.canvas.height / 2 + 25);\n        \n        const timeText = `Completion Time: ${(levelStatus.completionTime / 1000).toFixed(2)}s`;\n        this.ctx.fillText(timeText, this.canvas.width / 2, this.canvas.height / 2 + 45);\n        \n        // Next level indicator\n        this.ctx.fillStyle = '#cccccc';\n        this.ctx.font = '12px Arial';\n        this.ctx.fillText('Preparing next level...', this.canvas.width / 2, this.canvas.height / 2 + 80);\n    }\n    \n    /**\n     * Set up level manager callbacks\n     */\n    setupLevelManagerCallbacks() {\n        // Level start callback\n        this.levelManager.setOnLevelStart((levelNumber, levelConfig) => {\n            console.log(`Level ${levelNumber} started:`, levelConfig);\n\n            // Reset enemy manager for new level\n            if (this.enemyManager) {\n                this.enemyManager.reset();\n                console.log('Enemy manager reset for new level');\n            }\n        });\n        \n        // Level completion callback\n        this.levelManager.setOnLevelComplete((completionData) => {\n            console.log('Level completed:', completionData);\n            \n            if (completionData.completed) {\n                // Level completed successfully\n                console.log(`Level ${completionData.levelNumber} completed in ${completionData.completionTime.toFixed(2)}s`);\n                console.log(`Score: ${completionData.score.totalScore}, Enemies: ${completionData.enemiesDefeated}`);\n                \n                // Calculate and award WISH tokens\n                if (this.tokenManager) {\n                    const tokenReward = this.tokenManager.calculateLevelReward(completionData);\n                    if (tokenReward.totalReward > 0) {\n                        const awardResult = this.tokenManager.awardTokens(\n                            tokenReward.totalReward, \n                            'level_completion',\n                            {\n                                levelNumber: completionData.levelNumber,\n                                completionTime: completionData.completionTime,\n                                score: completionData.score.totalScore,\n                                bonuses: completionData.bonuses,\n                                breakdown: tokenReward.breakdown\n                            }\n                        );\n                        \n                        console.log(`Awarded ${tokenReward.totalReward} WISH tokens for level completion`);\n                        console.log('Token reward breakdown:', tokenReward.breakdown);\n\n                        // Update completion data with token reward for Orange SDK\n                        completionData.score.tokenReward = tokenReward.totalReward;\n                    }\n                }\n\n                // Save progress to Orange SDK\n                if (this.orangeSDKManager) {\n                    this.orangeSDKManager.onLevelCompleted(completionData);\n                }\n\n                // Show Genie interface for power-up purchases\n                this.showGenieInterface(completionData);\n            } else {\n                // Level failed\n                console.log(`Level ${completionData.levelNumber} failed: ${completionData.reason}`);\n                \n                // Auto-retry after a brief delay\n                setTimeout(() => {\n                    if (completionData.canRetry) {\n                        this.levelManager.startLevel(completionData.levelNumber);\n                    }\n                }, 3000);\n            }\n        });\n        \n        // Score update callback\n        this.levelManager.setOnScoreUpdate((scoreData) => {\n            // Update UI with new score information\n            // This could trigger score display animations, etc.\n        });\n    }\n    \n    /**\n     * Set up enemy manager callbacks\n     */\n    setupEnemyManagerCallbacks() {\n        // Wave completion callback\n        this.enemyManager.onWaveComplete = (waveNumber, bonus) => {\n            console.log(`Wave ${waveNumber} completed with bonus: ${bonus}`);\n\n            // Record wave completion in level manager\n            if (this.levelManager) {\n                this.levelManager.recordWaveCompletion(waveNumber, bonus);\n            }\n        };\n\n        // Enemy escaped callback\n        this.enemyManager.onEnemyEscaped = (enemy) => {\n            console.log(`Enemy ${enemy.id} escaped off-screen`);\n\n            // Record enemy escape in level manager (treat as defeated for progress)\n            if (this.levelManager) {\n                this.levelManager.recordEnemyDefeat(enemy, 0); // 0 score for escaped enemies\n            }\n        };\n    }\n    \n    /**\n     * Handle collisions between game objects\n     */\n    handleCollisions() {\n        if (!this.enemyManager || !this.playerShip) return;\n        \n        // Check player-enemy collisions\n        const playerCollisions = this.enemyManager.checkPlayerCollisions(this.playerShip);\n        for (const enemy of playerCollisions) {\n            const result = this.enemyManager.handlePlayerEnemyCollision(this.playerShip, enemy);\n            console.log('Player-Enemy collision:', result);\n        }\n        \n        // Check projectile-enemy collisions\n        const projectiles = this.gameObjectManager.findByTag('projectile');\n        const projectileCollisions = this.enemyManager.checkProjectileCollisions(projectiles);\n        \n        for (const collision of projectileCollisions) {\n            const result = this.enemyManager.handleProjectileEnemyCollision(\n                collision.projectile, \n                collision.enemy, \n                collision.damage\n            );\n            \n            // Record enemy defeat in level manager\n            if (result.enemyDestroyed && this.levelManager) {\n                this.levelManager.recordEnemyDefeat(collision.enemy, result.scoreGained);\n            }\n        }\n    }\n    \n    /**\n     * Set up token manager callbacks\n     */\n    setupTokenManagerCallbacks() {\n        // Balance update callback\n        this.tokenManager.setOnBalanceUpdate((balance, statistics) => {\n            console.log(`Token balance updated: ${balance} WISH tokens`);\n\n            // Notify Orange SDK of token changes\n            if (this.orangeSDKManager) {\n                this.orangeSDKManager.onTokensChanged({\n                    balance: balance,\n                    statistics: statistics\n                });\n            }\n        });\n\n        // Transaction callback\n        this.tokenManager.setOnTransaction((transaction) => {\n            console.log('Token transaction:', transaction);\n\n            // Notify Orange SDK of significant token changes\n            if (this.orangeSDKManager && transaction.amount >= 50) {\n                const tokenData = transaction.type === 'earned'\n                    ? { earned: transaction.amount }\n                    : { spent: transaction.amount };\n\n                this.orangeSDKManager.onTokensChanged(tokenData);\n            }\n        });\n\n        // Reward earned callback\n        this.tokenManager.setOnRewardEarned((amount, reason, metadata) => {\n            console.log(`Token reward earned: ${amount} for ${reason}`);\n        });\n    }\n\n    /**\n     * Set up Genie interface callbacks\n     */\n    setupGenieInterfaceCallbacks() {\n        // Power-up purchased callback\n        this.genieInterface.setOnPowerUpPurchased((powerUp, transaction) => {\n            console.log(`Power-up purchased: ${powerUp.type} for ${powerUp.cost} tokens`);\n\n            // Update active power-ups tracking\n            // The GenieInterface already handles the power-up application\n        });\n\n        // Reality warp purchased callback (for future implementation)\n        this.genieInterface.setOnWarpPurchased((warpOption, transaction) => {\n            console.log(`Reality warp purchased: ${warpOption.type} for ${warpOption.cost} tokens`);\n        });\n\n        // Interface closed callback\n        this.genieInterface.setOnClose(() => {\n            console.log('Genie interface closed, resuming game');\n            this.continueToNextLevel();\n        });\n    }\n\n    /**\n     * Show the Genie interface between levels\n     */\n    showGenieInterface(completionData) {\n        console.log('Showing Genie interface for level completion');\n\n        // Store completion data for later use\n        this.lastCompletionData = completionData;\n\n        // Pause the game\n        this.pause();\n\n        // Show the Genie interface\n        this.genieInterface.show();\n    }\n\n    /**\n     * Continue to the next level after Genie interface is closed\n     */\n    continueToNextLevel() {\n        if (this.lastCompletionData) {\n            const completionData = this.lastCompletionData;\n            this.lastCompletionData = null;\n\n            // Continue to next level or end game\n            if (completionData.nextLevel <= this.levelManager.maxLevels) {\n                console.log(`Starting level ${completionData.nextLevel}`);\n                this.levelManager.startLevel(completionData.nextLevel);\n            } else {\n                console.log('Game completed! All levels finished.');\n                // TODO: Show game completion screen\n            }\n        }\n    }\n\n    /**\n     * Initialize Orange SDK asynchronously\n     */\n    async initializeOrangeSDK() {\n        try {\n            await this.orangeSDKManager.initialize();\n            console.log('Orange SDK initialized successfully');\n        } catch (error) {\n            console.error('Failed to initialize Orange SDK:', error);\n        }\n    }\n\n    /**\n     * Set up Orange SDK manager callbacks\n     */\n    setupOrangeSDKCallbacks() {\n        // Set up special status callback to handle bonuses\n        this.orangeSDKManager.setOnSpecialStatusCallback((statusType, value) => {\n            this.handleSpecialStatus(statusType, value);\n        });\n\n        // Set up save success callback\n        this.orangeSDKManager.setOnDataSavedCallback((data, reason) => {\n            console.log(`Game data saved to Orange SDK (${reason})`);\n        });\n\n        // Set up save error callback\n        this.orangeSDKManager.setOnSaveErrorCallback((error, reason) => {\n            console.error(`Failed to save game data (${reason}):`, error);\n        });\n    }\n\n    /**\n     * Handle special status bonuses from Orange SDK\n     * @param {string} statusType - Type of special status\n     * @param {*} value - Status value\n     */\n    handleSpecialStatus(statusType, value) {\n        switch (statusType) {\n            case 'bonus_lives':\n                if (this.playerShip && value > 0) {\n                    this.playerShip.addLives(value);\n                    console.log(`Bonus lives awarded: ${value} (login streak bonus)`);\n                }\n                break;\n\n            case 'tournament_participant':\n                if (value) {\n                    console.log('Tournament participant status active');\n                    // Could add special tournament UI indicators or bonuses\n                }\n                break;\n\n            case 'achievement_unlock':\n                console.log(`Achievement unlocked: ${value}`);\n                // Could trigger achievement notification UI\n                break;\n\n            default:\n                console.log(`Unknown special status: ${statusType} = ${value}`);\n        }\n    }\n\n    handleResize() {\n        const container = document.getElementById('game-container');\n        if (container) {\n            const { width, height } = container.getBoundingClientRect();\n            this.canvas.width = width;\n            this.canvas.height = height;\n            console.log(`Canvas resized to: ${this.canvas.width}x${this.canvas.height}`);\n        }\n    }\n}", "/**\n * AuthManager - Orange ID authentication wrapper\n * Handles Orange ID integration with authentication state management\n */\nexport class AuthManager {\n    constructor(config = {}) {\n        this.config = {\n            baseUrl: 'https://api.bedrockpassport.com',\n            authCallbackUrl: window.location.origin,\n            tenantId: config.tenantId || 'orange-abc123',\n            subscriptionKey: config.subscriptionKey || 'your_API_Key',\n            debugMode: config.debugMode || this.isDebugEnvironment()\n        };\n        \n        this.user = null;\n        this.token = null;\n        this.refreshToken = null;\n        this.isAuthenticated = false;\n        this.authCallbacks = [];\n    }\n\n    isDebugEnvironment() {\n        return window.location.hostname === 'localhost' || \n               window.location.hostname === '127.0.0.1' ||\n               window.location.search.includes('debug=true');\n    }\n\n    async initializeOrangeID() {\n        if (this.config.debugMode) {\n            console.log('Debug mode enabled - Orange ID initialization skipped');\n            return true;\n        }\n\n        return new Promise((resolve, reject) => {\n            // Add a small delay to ensure scripts are fully loaded\n            setTimeout(() => {\n                // Check if all required libraries are loaded\n                console.log('Checking required libraries:', {\n                    React: !!window.React,\n                    ReactDOM: !!window.ReactDOM,\n                    Bedrock: !!window.Bedrock\n                });\n\n                if (!window.React || !window.ReactDOM || !window.Bedrock) {\n                    console.error('Required libraries not loaded:', {\n                        React: !!window.React,\n                        ReactDOM: !!window.ReactDOM,\n                        Bedrock: !!window.Bedrock\n                    });\n                    reject(new Error('Required libraries failed to load. Please check your internet connection.'));\n                    return;\n                }\n\n                console.log('All libraries loaded, setting up Orange ID widget...');\n                this.setupOrangeIDWidget(resolve, reject);\n            }, 100);\n        });\n    }\n\n\n\n    setupOrangeIDWidget(resolve, reject) {\n        try {\n            const container = document.getElementById('bedrock-login-widget');\n            if (!container) {\n                reject(new Error('Orange ID container not found'));\n                return;\n            }\n\n            const root = ReactDOM.createRoot(container);\n\n            // Check for existing auth tokens in URL\n            const urlParams = new URLSearchParams(window.location.search);\n            const token = urlParams.get('token');\n            const refreshToken = urlParams.get('refreshToken');\n\n            if (token && refreshToken) {\n                // Handle callback processing\n                root.render(\n                    React.createElement(\n                        window.Bedrock.BedrockPassportProvider,\n                        this.config,\n                        React.createElement(this.createCallbackProcessor(token, refreshToken))\n                    )\n                );\n            } else {\n                // Normal login flow\n                root.render(\n                    React.createElement(\n                        window.Bedrock.BedrockPassportProvider,\n                        this.config,\n                        React.createElement(window.Bedrock.LoginPanel, {\n                            title: \"Sign in to\",\n                            logo: \"https://irp.cdn-website.com/e81c109a/dms3rep/multi/orange-web3-logo-v2a-20241018.svg\",\n                            logoAlt: \"Orange Web3\",\n                            walletButtonText: \"Connect Wallet\",\n                            showConnectWallet: false,\n                            separatorText: \"OR\",\n                            features: {\n                                enableWalletConnect: false,\n                                enableAppleLogin: true,\n                                enableGoogleLogin: true,\n                                enableEmailLogin: false,\n                            },\n                            onLoginSuccess: (user, tokens) => {\n                                console.log('Orange ID login success:', user);\n                                this.handleAuthSuccess(tokens.accessToken, tokens.refreshToken);\n                            },\n                            onLoginError: (error) => {\n                                console.error('Orange ID login error:', error);\n                                this.handleAuthError(error);\n                            }\n                        })\n                    )\n                );\n            }\n\n            // Listen for authentication events\n            this.setupAuthEventListeners();\n            resolve(true);\n        } catch (error) {\n            console.error('Error setting up Orange ID widget:', error);\n            reject(error);\n        }\n    }\n\n    createCallbackProcessor(token, refreshToken) {\n        const self = this;\n        return function CallbackProcessor() {\n            const { loginCallback } = window.Bedrock.useBedrockPassport();\n\n            React.useEffect(() => {\n                async function processCallback() {\n                    try {\n                        const success = await loginCallback(token, refreshToken);\n                        if (success) {\n                            console.log('Callback processing successful');\n                            // The auth success will be handled by the onLoginSuccess callback\n                        } else {\n                            self.handleAuthError(new Error('Login callback failed'));\n                        }\n                    } catch (error) {\n                        console.error('Callback processing error:', error);\n                        self.handleAuthError(error);\n                    }\n                }\n                processCallback();\n            }, [loginCallback]);\n\n            return React.createElement('div', {\n                style: { textAlign: 'center', padding: '20px', color: '#00ffff' }\n            }, 'Processing authentication...');\n        };\n    }\n\n    setupAuthEventListeners() {\n        // Listen for custom auth events (for additional integration points)\n        window.addEventListener('orangeAuthSuccess', (event) => {\n            this.handleAuthSuccess(event.detail.token, event.detail.refreshToken);\n        });\n\n        window.addEventListener('orangeAuthError', (event) => {\n            this.handleAuthError(event.detail.error);\n        });\n    }\n\n    async handleAuthSuccess(token, refreshToken) {\n        this.token = token;\n        this.refreshToken = refreshToken;\n        \n        try {\n            const userProfile = await this.validateToken(token);\n            this.user = userProfile;\n            this.isAuthenticated = true;\n            \n            // Clear URL parameters\n            const url = new URL(window.location);\n            url.searchParams.delete('token');\n            url.searchParams.delete('refreshToken');\n            window.history.replaceState({}, document.title, url);\n            \n            this.notifyAuthCallbacks(true, this.user);\n        } catch (error) {\n            this.handleAuthError(error);\n        }\n    }\n\n    handleAuthError(error) {\n        console.error('Authentication error:', error);\n        this.isAuthenticated = false;\n        this.user = null;\n        this.token = null;\n        this.refreshToken = null;\n        this.notifyAuthCallbacks(false, error);\n    }\n\n    async validateToken(token) {\n        const response = await fetch('https://api.bedrockpassport.com/api/v1/auth/user', {\n            headers: {\n                'Authorization': `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            }\n        });\n\n        if (!response.ok) {\n            throw new Error('Token validation failed');\n        }\n\n        return await response.json();\n    }\n\n    enableDebugMode() {\n        this.config.debugMode = true;\n        this.user = {\n            id: 'debug-user-123',\n            email: '<EMAIL>',\n            name: 'Debug User',\n            provider: 'debug'\n        };\n        this.isAuthenticated = true;\n        this.notifyAuthCallbacks(true, this.user);\n    }\n\n    async logout() {\n        this.user = null;\n        this.token = null;\n        this.refreshToken = null;\n        this.isAuthenticated = false;\n        this.notifyAuthCallbacks(false, null);\n    }\n\n    getUser() {\n        return this.user;\n    }\n\n    onAuthStateChange(callback) {\n        this.authCallbacks.push(callback);\n    }\n\n    notifyAuthCallbacks(isAuthenticated, data) {\n        this.authCallbacks.forEach(callback => {\n            callback(isAuthenticated, data);\n        });\n    }\n}\n", "import { AuthManager } from '../auth/AuthManager.js';\n\nexport class MainMenu {\n  constructor(gameEngine) {\n    this.gameEngine = gameEngine;\n    this.authManager = new AuthManager({\n      tenantId: 'orange-abc123', // Replace with actual tenant ID\n      subscriptionKey: 'your_API_Key' // Replace with actual API key\n    });\n    \n    this.container = null;\n    this.isVisible = false;\n    this.setupEventListeners();\n  }\n\n  async initialize() {\n    this.createMenuHTML();\n\n    console.log('MainMenu initializing, debug mode:', this.authManager.config.debugMode);\n\n    // Initialize Orange ID if not in debug mode\n    if (!this.authManager.config.debugMode) {\n      try {\n        console.log('Initializing Orange ID...');\n        await this.authManager.initializeOrangeID();\n        console.log('Orange ID initialized successfully');\n      } catch (error) {\n        console.error('Failed to initialize Orange ID:', error);\n        this.showError(`Authentication system failed to load: ${error.message}. You can use debug login instead.`);\n      }\n    } else {\n      console.log('Debug mode enabled - Orange ID initialization skipped');\n    }\n\n    this.authManager.onAuthStateChange((isAuthenticated, data) => {\n      if (isAuthenticated) {\n        this.onAuthenticationSuccess(data);\n      } else {\n        this.onAuthenticationFailure(data);\n      }\n    });\n  }\n\n  createMenuHTML() {\n    this.container = document.createElement('div');\n    this.container.id = 'main-menu';\n    this.container.className = 'main-menu';\n    \n    this.container.innerHTML = `\n      <div class=\"menu-background\">\n        <div class=\"stars\"></div>\n        <div class=\"menu-content\">\n          <h1 class=\"game-title\">WARPSPACE</h1>\n          <p class=\"game-subtitle\">Reality Warping Shooter</p>\n          \n          <div class=\"auth-section\" id=\"auth-section\">\n            <div class=\"orange-id-container\">\n              <div id=\"bedrock-login-widget\"></div>\n            </div>\n            \n            <div class=\"debug-section\" id=\"debug-section\">\n              <button id=\"debug-login-btn\" class=\"debug-button\">\n                🔧 Debug Login (Skip Auth)\n              </button>\n              <p style=\"color: #888; font-size: 12px; margin-top: 10px;\">\n                Debug mode: ${this.authManager.config.debugMode ? 'ON' : 'OFF'}\n              </p>\n            </div>\n          </div>\n          \n          <div class=\"game-controls\" id=\"game-controls\" style=\"display: none;\">\n            <button id=\"start-game-btn\" class=\"menu-button primary\">\n              Start Game\n            </button>\n            <button id=\"genie-shop-btn\" class=\"menu-button\">\n              🪔 Genie Shop\n            </button>\n            <button id=\"instructions-btn\" class=\"menu-button\">\n              How to Play\n            </button>\n            <button id=\"logout-btn\" class=\"menu-button secondary\">\n              Logout\n            </button>\n          </div>\n          \n          <div class=\"user-info\" id=\"user-info\" style=\"display: none;\">\n            <p>Welcome, <span id=\"user-name\"></span>!</p>\n            <p>WISH Tokens: <span id=\"token-balance\">0</span></p>\n          </div>\n          \n          <div class=\"error-message\" id=\"error-message\" style=\"display: none;\"></div>\n        </div>\n      </div>\n    `;\n    \n    document.body.appendChild(this.container);\n  }\n\n  setupEventListeners() {\n    document.addEventListener('click', (event) => {\n      if (event.target.id === 'debug-login-btn') {\n        this.authManager.enableDebugMode();\n      } else if (event.target.id === 'start-game-btn') {\n        this.startGame();\n      } else if (event.target.id === 'genie-shop-btn') {\n        this.showGenieShop();\n      } else if (event.target.id === 'instructions-btn') {\n        this.showInstructions();\n      } else if (event.target.id === 'logout-btn') {\n        this.logout();\n      }\n    });\n  }\n\n  onAuthenticationSuccess(user) {\n    console.log('Authentication successful:', user);\n    \n    // Hide auth section, show game controls\n    document.getElementById('auth-section').style.display = 'none';\n    document.getElementById('game-controls').style.display = 'block';\n    document.getElementById('user-info').style.display = 'block';\n    \n    // Update user info\n    document.getElementById('user-name').textContent = user.name || user.email || 'Player';\n    \n    // Load user's token balance (would come from Orange SDK later)\n    this.loadUserTokenBalance();\n    \n    this.hideError();\n  }\n\n  onAuthenticationFailure(error) {\n    console.error('Authentication failed:', error);\n    \n    // Show auth section, hide game controls\n    document.getElementById('auth-section').style.display = 'block';\n    document.getElementById('game-controls').style.display = 'none';\n    document.getElementById('user-info').style.display = 'none';\n    \n    if (error && typeof error === 'object') {\n      this.showError('Authentication failed. Please try again.');\n    }\n  }\n\n  async loadUserTokenBalance() {\n    // Placeholder for Orange SDK integration\n    // For now, show default balance\n    document.getElementById('token-balance').textContent = '100';\n  }\n\n  startGame() {\n    if (!this.authManager.isAuthenticated) {\n      this.showError('Please authentication first');\n      return;\n    }\n\n    this.hide();\n    this.gameEngine.startGame(this.authManager.getUser());\n  }\n\n  showGenieShop() {\n    if (!this.authManager.isAuthenticated) {\n      this.showError('Please authenticate first');\n      return;\n    }\n\n    console.log('Opening Genie Shop from main menu...');\n\n    // Get the game engine's genie interface\n    if (this.gameEngine && this.gameEngine.genieInterface) {\n      // Give some debug tokens if the player has none\n      if (this.gameEngine.tokenManager && this.gameEngine.tokenManager.getBalance() < 100) {\n        this.gameEngine.tokenManager.awardTokens(500, 'debug_menu_tokens');\n        console.log('Awarded debug tokens for testing');\n      }\n\n      this.gameEngine.genieInterface.show();\n    } else {\n      this.showError('Genie Shop not available. Please start the game first.');\n    }\n  }\n\n  showInstructions() {\n    const instructions = `\n      WARPSPACE - How to Play\n      \n      🚀 CONTROLS:\n      • Arrow Keys / WASD: Move ship\n      • Spacebar: Fire weapons\n      • P: Pause game\n      \n      💎 WISH TOKENS:\n      • Earn tokens by completing levels quickly\n      • Spend tokens on reality warps and power-ups\n      • Visit the Genie between levels to make purchases\n      \n      🌟 REALITY WARPS:\n      • Transform battlefields with AI-generated environments\n      • Different environments affect enemy effectiveness\n      • Strategic warping can give you tactical advantages\n      \n      🎯 POWER-UPS:\n      • Extra Wingman: Additional firepower\n      • Extra Life: Survive longer\n      • Spread Ammo: Wider shot pattern\n    `;\n    \n    alert(instructions); // Replace with proper modal later\n  }\n\n  async logout() {\n    await this.authManager.logout();\n    this.onAuthenticationFailure(null);\n  }\n\n  show() {\n    if (this.container) {\n      this.container.style.display = 'block';\n      this.isVisible = true;\n    }\n  }\n\n  hide() {\n    if (this.container) {\n      this.container.style.display = 'none';\n      this.isVisible = false;\n    }\n  }\n\n  showError(message) {\n    const errorElement = document.getElementById('error-message');\n    if (errorElement) {\n      errorElement.textContent = message;\n      errorElement.style.display = 'block';\n    }\n  }\n\n  hideError() {\n    const errorElement = document.getElementById('error-message');\n    if (errorElement) {\n      errorElement.style.display = 'none';\n    }\n  }\n\n  destroy() {\n    if (this.container) {\n      document.body.removeChild(this.container);\n      this.container = null;\n    }\n  }\n}\n", "import { GameEngine } from './core/GameEngine.js';\nimport { MainMenu } from './ui/MainMenu.js';\n\nclass Game {\n  constructor() {\n    this.gameEngine = null;\n    this.mainMenu = null;\n    this.isInitialized = false;\n  }\n\n  async initialize() {\n    if (this.isInitialized) return;\n\n    try {\n      // Get canvas element\n      const canvas = document.getElementById('gameCanvas');\n      if (!canvas) {\n        throw new Error('Game canvas not found');\n      }\n\n      // Initialize game engine\n      this.gameEngine = new GameEngine(canvas);\n      await this.gameEngine.init();\n\n      // Initialize main menu\n      this.mainMenu = new MainMenu(this.gameEngine);\n      await this.mainMenu.initialize();\n\n      // Show main menu\n      this.mainMenu.show();\n\n      this.isInitialized = true;\n      console.log('Game initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize game:', error);\n      this.showInitializationError(error);\n    }\n  }\n\n  showInitializationError(error) {\n    document.body.innerHTML = `\n      <div style=\"\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        height: 100vh;\n        background: #0a0a2e;\n        color: white;\n        font-family: Arial, sans-serif;\n        text-align: center;\n      \">\n        <div>\n          <h1>Game Initialization Failed</h1>\n          <p>Error: ${error.message}</p>\n          <button onclick=\"location.reload()\" style=\"\n            background: #00ffff;\n            color: #0a0a2e;\n            border: none;\n            padding: 10px 20px;\n            border-radius: 5px;\n            cursor: pointer;\n            font-size: 16px;\n            margin-top: 20px;\n          \">\n            Retry\n          </button>\n        </div>\n      </div>\n    `;\n  }\n}\n\n// Global game instance\nlet game = null;\n\n// Initialize game when DOM is loaded\ndocument.addEventListener('DOMContentLoaded', async () => {\n  game = new Game();\n  await game.initialize();\n});\n\n// Handle page visibility changes\ndocument.addEventListener('visibilitychange', () => {\n  if (game && game.gameEngine) {\n    if (document.hidden) {\n      game.gameEngine.pause();\n    } else {\n      game.gameEngine.resume();\n    }\n  }\n});\n\n// Handle page unload to save final data\nwindow.addEventListener('beforeunload', async (event) => {\n  if (game && game.gameEngine) {\n    // Save final data before page unload\n    await game.gameEngine.destroy();\n  }\n});\n\n// Handle page unload for mobile devices\ndocument.addEventListener('pagehide', async (event) => {\n  if (game && game.gameEngine) {\n    await game.gameEngine.destroy();\n  }\n});\n"], "names": ["Vector2", "x", "y", "other", "scalar", "mag", "angle", "magnitude", "cos", "sin", "newX", "newY", "tolerance", "InputManager", "canvas", "event", "key", "button", "rect", "touch", "touchPos", "action", "keys", "movement", "joystickInput", "existing", "filtered", "k", "VirtualJoystick", "ctx", "touchId", "position", "offset", "GameObject$1", "_a", "deltaTime", "interpolation", "renderPos", "tag", "target", "direction", "speed", "force", "bounds", "__publicField", "Projectile", "GameObject", "type", "owner", "alpha", "currentPos", "nextPos", "renderCurrentPos", "interpolatedOffset", "velocityEnd", "margin", "color", "factor", "hex", "r", "g", "b", "WeaponSystem", "gameObjectManager", "projectile", "firePosition", "baseAngle", "angleStep", "i", "spreadDirection", "ownerPos", "forwardOffset", "firePos", "firePos3", "offset3", "flashAlpha", "flashPos", "gradient", "pattern", "rateMs", "damage", "angleRadians", "enabled", "restorePattern", "PlayerShip", "canvasWidth", "canvasHeight", "movementInput", "accelerationForce", "leftBound", "rightBound", "topBound", "bottomBound", "shipAlpha", "shipColor", "strokeColor", "flashPhase", "flashIntensity", "thrusterIntensity", "animationPhase", "baseFlame<PERSON><PERSON>th", "flameVariation", "flame<PERSON><PERSON><PERSON>", "length", "intensity", "thrusterSize", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "velocityScale", "width", "height", "result", "weaponSystem", "actualDamage", "startX", "startY", "healAmount", "actualHeal", "extraLives", "color1", "color2", "hex1", "hex2", "r1", "g1", "b1", "r2", "g2", "b2", "toHex", "n", "ObjectPool", "createFn", "resetFn", "initialSize", "obj", "index", "GameObjectManager", "object", "pool", "objectToRemove", "id", "group1Tag", "group2Tag", "callback", "group1", "group2", "obj1", "obj2", "cellSize", "spatialHash", "cellX", "cellY", "dx", "dy", "nearbyObjects", "poolStats", "GAME_CONFIG", "ENVIRONMENT_TYPES", "ENEMY_TYPES", "LevelManager", "levelNumber", "difficultyMultiplier", "baseEnemyCount", "enemyCountIncrease", "totalEnemies", "baseWaveCount", "waveIncrease", "totalWaves", "environment", "<PERSON><PERSON><PERSON>", "hasSpecialMechanics", "baseTimeLimit", "timeLimitAdjustment", "timeLimit", "score<PERSON>arget", "environments", "bossEnvironments", "baseIndex", "variation", "finalIndex", "distribution", "conditions", "effects", "gameState", "enemiesCompleted", "wavesCompleted", "timeExpired", "player<PERSON><PERSON><PERSON><PERSON>", "completionTimeSeconds", "scoreData", "completionData", "reason", "failureData", "completionTime", "config", "enemyScore", "timeBonus", "accuracyBonus", "perfectBonus", "completionBonus", "baseTotal", "totalScore", "targetTime", "timeRatio", "accuracy", "enemy", "scoreValue", "waveNumber", "waveBonus", "baseReward", "levelMultiplier", "GameMath", "value", "min", "max", "start", "end", "t", "point", "rect1", "rect2", "circle1", "circle2", "degrees", "radians", "circle", "closestX", "closestY", "lineStart", "lineEnd", "fx", "fy", "a", "c", "discriminant", "discriminantSqrt", "t1", "t2", "edge0", "edge1", "angle1", "angle2", "diff", "current", "maxStep", "fromMin", "fromMax", "to<PERSON>in", "toMax", "Enemy", "playerPosition", "dt", "effectiveSpeed", "sineOffset", "targetX", "xDirection", "radius", "targetY", "curveDirection", "centerX", "centerY", "hoverDistance", "distanceToPlayer", "perpendicular", "frameTime", "sweepPhase", "weaveX", "weaveY", "spiralX", "spiralY", "side", "curveIntensity", "screenCenter", "weave", "acceleration", "sweepDirection", "effectivenessAlpha", "colors", "pulsePhase", "scale", "colorSchemes", "wingFlap", "bellScale", "tentacleWave", "flicker1", "flicker2", "glowIntensity", "wispiness", "tendrilWave", "progress", "particleCount", "distance", "<PERSON><PERSON><PERSON><PERSON>", "barHeight", "healthPercent", "options", "effectiveness", "attackRange", "EnemyManager", "_b", "enemyTypes", "spawnPatterns", "types", "patterns", "enemyType", "spawnPos", "patternOptions", "random", "cumulativeWeight", "typeData", "availablePatterns", "selected", "formation", "formationIndex", "formationType", "f", "row", "triangleRow", "posInRow", "row<PERSON>id<PERSON>", "diamondSize", "midIndex", "botIndex", "wedgeRow", "wedgePos", "totalProcessed", "allEnemiesSpawned", "allEnemiesProcessed", "noActiveEnemies", "waveMultiplier", "completionRatio", "bonus", "debugInfo", "player", "collisions", "projectiles", "damageResult", "effectivenessMap", "attacks", "attackingEnemies", "attackData", "TokenEconomyManager", "scoreBonus", "speedBonus", "subtotal", "totalReward", "breakdown", "baseAmount", "expectedTime", "fastTime", "score", "levelScaling", "multiplier", "amount", "metadata", "transaction", "netProfit", "averageEarningPerLevel", "count", "currentAvg", "reward", "rewardId", "cutoffTime", "animation", "pendingRewards", "_PowerUp", "cost", "duration", "description", "playerShip", "playerTokens", "PowerUp", "ExtraLifePowerUp", "SpreadAmmoPowerUp", "ExtraWingmanPowerUp", "PowerUpFactory", "GenieInterface", "tokenManager", "gameEngine", "error", "powerUp", "availability", "isActive", "canPurchase", "statusClass", "statusText", "buttonText", "l", "closeBtn", "backdrop", "e", "powerUpType", "p", "spendResult", "animate", "lamp", "tokenIcon", "glow", "o", "s", "_", "G", "d", "A", "OrangeSDKManager", "GGSDK.gameLoaded", "GGSDK.listenPaused", "GGSDK.listenResumed", "GGSDK.listenQuit", "defaultData", "GGSDK.getGameData", "data", "savedStatuses", "today", "lastLogin", "lastLoginDate", "daysDiff", "streak", "bonusLives", "progressData", "level", "currentBest", "totalLevels", "currentTime", "sessionTime", "dataToSave", "pendingData", "attempt", "resolve", "reject", "GGSDK.saveGameData", "_c", "finalScore", "_d", "GGSDK.gameOver", "tokenData", "GGSDK.gamePaused", "GGSDK.gameResumed", "now", "achievementId", "isParticipant", "interval", "GameEngine", "uiElement", "user", "dtSeconds", "star", "actions", "yOffset", "stats", "healthStatus", "healthBarX", "healthBarY", "healthBarWidth", "healthBarHeight", "healthWidth", "healthColor", "livesY", "lifeIconSize", "lifeIconSpacing", "lifeIconStartX", "iconX", "iconY", "invulnTime", "levelStatus", "progressText", "waveText", "timeElapsed", "timeRemaining", "envText", "timeText", "levelConfig", "tokenReward", "playerCollisions", "projectileCollisions", "collision", "balance", "statistics", "warpOption", "statusType", "container", "<PERSON>th<PERSON><PERSON><PERSON>", "root", "urlParams", "token", "refreshToken", "tokens", "self", "login<PERSON><PERSON><PERSON>", "processCallback", "userProfile", "url", "response", "isAuthenticated", "MainMenu", "message", "errorElement", "Game", "game"], "mappings": "02BAGO,MAAMA,CAAQ,CACjB,YAAYC,EAAI,EAAGC,EAAI,EAAG,CACtB,KAAK,EAAID,EACT,KAAK,EAAIC,CACb,CAGA,OAAO,MAAO,CACV,OAAO,IAAIF,EAAQ,EAAG,CAAC,CAC3B,CAEA,OAAO,KAAM,CACT,OAAO,IAAIA,EAAQ,EAAG,CAAC,CAC3B,CAEA,OAAO,IAAK,CACR,OAAO,IAAIA,EAAQ,EAAG,EAAE,CAC5B,CAEA,OAAO,MAAO,CACV,OAAO,IAAIA,EAAQ,EAAG,CAAC,CAC3B,CAEA,OAAO,MAAO,CACV,OAAO,IAAIA,EAAQ,GAAI,CAAC,CAC5B,CAEA,OAAO,OAAQ,CACX,OAAO,IAAIA,EAAQ,EAAG,CAAC,CAC3B,CAGA,IAAIG,EAAO,CACP,OAAO,IAAIH,EAAQ,KAAK,EAAIG,EAAM,EAAG,KAAK,EAAIA,EAAM,CAAC,CACzD,CAEA,SAASA,EAAO,CACZ,OAAO,IAAIH,EAAQ,KAAK,EAAIG,EAAM,EAAG,KAAK,EAAIA,EAAM,CAAC,CACzD,CAEA,SAASC,EAAQ,CACb,OAAO,IAAIJ,EAAQ,KAAK,EAAII,EAAQ,KAAK,EAAIA,CAAM,CACvD,CAEA,OAAOA,EAAQ,CACX,GAAIA,IAAW,EAAG,MAAM,IAAI,MAAM,kBAAkB,EACpD,OAAO,IAAIJ,EAAQ,KAAK,EAAII,EAAQ,KAAK,EAAIA,CAAM,CACvD,CAGA,WAAY,CACR,OAAO,KAAK,KAAK,KAAK,EAAI,KAAK,EAAI,KAAK,EAAI,KAAK,CAAC,CACtD,CAEA,WAAY,CACR,MAAMC,EAAM,KAAK,UAAS,EAC1B,OAAIA,IAAQ,EAAUL,EAAQ,KAAI,EAC3B,KAAK,OAAOK,CAAG,CAC1B,CAGA,SAASF,EAAO,CACZ,OAAO,KAAK,SAASA,CAAK,EAAE,UAAS,CACzC,CAGA,IAAIA,EAAO,CACP,OAAO,KAAK,EAAIA,EAAM,EAAI,KAAK,EAAIA,EAAM,CAC7C,CAGA,WAAWA,EAAO,CACd,YAAK,GAAKA,EAAM,EAChB,KAAK,GAAKA,EAAM,EACT,IACX,CAEA,gBAAgBA,EAAO,CACnB,YAAK,GAAKA,EAAM,EAChB,KAAK,GAAKA,EAAM,EACT,IACX,CAEA,gBAAgBC,EAAQ,CACpB,YAAK,GAAKA,EACV,KAAK,GAAKA,EACH,IACX,CAEA,kBAAmB,CACf,MAAMC,EAAM,KAAK,UAAS,EAC1B,OAAIA,EAAM,IACN,KAAK,GAAKA,EACV,KAAK,GAAKA,GAEP,IACX,CAGA,IAAIJ,EAAGC,EAAG,CACN,YAAK,EAAID,EACT,KAAK,EAAIC,EACF,IACX,CAEA,cAAcC,EAAO,CACjB,YAAK,EAAIA,EAAM,EACf,KAAK,EAAIA,EAAM,EACR,IACX,CAGA,OAAQ,CACJ,OAAO,KAAK,MAAM,KAAK,EAAG,KAAK,CAAC,CACpC,CAEA,OAAO,UAAUG,EAAOC,EAAY,EAAG,CACnC,OAAO,IAAIP,EACP,KAAK,IAAIM,CAAK,EAAIC,EAClB,KAAK,IAAID,CAAK,EAAIC,CAC9B,CACI,CAGA,OAAOD,EAAO,CACV,MAAME,EAAM,KAAK,IAAIF,CAAK,EACpBG,EAAM,KAAK,IAAIH,CAAK,EACpBI,EAAO,KAAK,EAAIF,EAAM,KAAK,EAAIC,EAC/BE,EAAO,KAAK,EAAIF,EAAM,KAAK,EAAID,EACrC,OAAO,IAAIR,EAAQU,EAAMC,CAAI,CACjC,CAEA,cAAcL,EAAO,CACjB,MAAME,EAAM,KAAK,IAAIF,CAAK,EACpBG,EAAM,KAAK,IAAIH,CAAK,EACpBI,EAAO,KAAK,EAAIF,EAAM,KAAK,EAAIC,EAC/BE,EAAO,KAAK,EAAIF,EAAM,KAAK,EAAID,EACrC,YAAK,EAAIE,EACT,KAAK,EAAIC,EACF,IACX,CAGA,eAAgB,CACZ,OAAO,IAAIX,EAAQ,CAAC,KAAK,EAAG,KAAK,CAAC,CACtC,CAGA,OAAQ,CACJ,OAAO,IAAIA,EAAQ,KAAK,EAAG,KAAK,CAAC,CACrC,CAEA,OAAOG,EAAOS,EAAY,EAAG,CACzB,OAAIA,IAAc,EACP,KAAK,IAAMT,EAAM,GAAK,KAAK,IAAMA,EAAM,EAE3C,KAAK,IAAI,KAAK,EAAIA,EAAM,CAAC,GAAKS,GAC9B,KAAK,IAAI,KAAK,EAAIT,EAAM,CAAC,GAAKS,CACzC,CAEA,UAAW,CACP,MAAO,WAAW,KAAK,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,EAAE,QAAQ,CAAC,CAAC,GAC7D,CACJ,CChKO,MAAMC,CAAa,CACtB,YAAYC,EAAQ,CAChB,KAAK,OAASA,EAGd,KAAK,KAAO,IAAI,IAChB,KAAK,YAAc,IAAI,IACvB,KAAK,aAAe,IAAI,IAExB,KAAK,cAAgB,IAAId,EAAQ,EAAG,CAAC,EACrC,KAAK,aAAe,IAAI,IACxB,KAAK,aAAe,IAAI,IACxB,KAAK,cAAgB,IAAI,IAEzB,KAAK,QAAU,IAAI,IACnB,KAAK,aAAe,IAAI,IACxB,KAAK,WAAa,IAAI,IAGtB,KAAK,YAAc,IAAI,IACvB,KAAK,qBAAoB,EAGzB,KAAK,cAAgB,iBAAkB,OACvC,KAAK,gBAAkB,KAGvB,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,YAAc,KAAK,YAAY,KAAK,IAAI,EAC7C,KAAK,gBAAkB,KAAK,gBAAgB,KAAK,IAAI,EACrD,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,gBAAkB,KAAK,gBAAgB,KAAK,IAAI,EACrD,KAAK,iBAAmB,KAAK,iBAAiB,KAAK,IAAI,EACvD,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EACnD,KAAK,gBAAkB,KAAK,gBAAgB,KAAK,IAAI,EAGrD,KAAK,KAAI,CACb,CAEA,MAAO,CAEH,SAAS,iBAAiB,UAAW,KAAK,aAAa,EACvD,SAAS,iBAAiB,QAAS,KAAK,WAAW,EAGnD,KAAK,OAAO,iBAAiB,YAAa,KAAK,eAAe,EAC9D,KAAK,OAAO,iBAAiB,UAAW,KAAK,aAAa,EAC1D,KAAK,OAAO,iBAAiB,YAAa,KAAK,eAAe,EAG9D,KAAK,OAAO,iBAAiB,aAAc,KAAK,iBAAkB,CAAE,QAAS,GAAO,EACpF,KAAK,OAAO,iBAAiB,WAAY,KAAK,eAAgB,CAAE,QAAS,GAAO,EAChF,KAAK,OAAO,iBAAiB,YAAa,KAAK,gBAAiB,CAAE,QAAS,GAAO,EAGlF,KAAK,OAAO,iBAAiB,cAAgB,GAAM,EAAE,gBAAgB,EAGjE,KAAK,eACL,KAAK,oBAAmB,EAG5B,QAAQ,IAAI,0BAA0B,CAC1C,CAEA,sBAAuB,CAEnB,KAAK,YAAY,IAAI,SAAU,CAAC,UAAW,MAAM,CAAC,EAClD,KAAK,YAAY,IAAI,WAAY,CAAC,YAAa,MAAM,CAAC,EACtD,KAAK,YAAY,IAAI,WAAY,CAAC,YAAa,MAAM,CAAC,EACtD,KAAK,YAAY,IAAI,YAAa,CAAC,aAAc,MAAM,CAAC,EAGxD,KAAK,YAAY,IAAI,OAAQ,CAAC,QAAS,OAAO,CAAC,EAC/C,KAAK,YAAY,IAAI,QAAS,CAAC,SAAU,MAAM,CAAC,EAChD,KAAK,YAAY,IAAI,WAAY,CAAC,OAAQ,MAAM,CAAC,EAGjD,KAAK,YAAY,IAAI,QAAS,CAAC,IAAI,CAAC,CACxC,CAGA,cAAce,EAAO,CACjB,MAAMC,EAAMD,EAAM,KAEb,KAAK,KAAK,IAAIC,CAAG,GAClB,KAAK,YAAY,IAAIA,EAAK,EAAI,EAGlC,KAAK,KAAK,IAAIA,EAAK,EAAI,EAGnB,KAAK,UAAUA,CAAG,GAClBD,EAAM,eAAc,CAE5B,CAEA,YAAYA,EAAO,CACf,MAAMC,EAAMD,EAAM,KAClB,KAAK,KAAK,IAAIC,EAAK,EAAK,EACxB,KAAK,aAAa,IAAIA,EAAK,EAAI,EAE3B,KAAK,UAAUA,CAAG,GAClBD,EAAM,eAAc,CAE5B,CAGA,gBAAgBA,EAAO,CACnB,MAAME,EAASF,EAAM,OAEhB,KAAK,aAAa,IAAIE,CAAM,GAC7B,KAAK,aAAa,IAAIA,EAAQ,EAAI,EAGtC,KAAK,aAAa,IAAIA,EAAQ,EAAI,EAClC,KAAK,oBAAoBF,CAAK,EAE9BA,EAAM,eAAc,CACxB,CAEA,cAAcA,EAAO,CACjB,MAAME,EAASF,EAAM,OACrB,KAAK,aAAa,IAAIE,EAAQ,EAAK,EACnC,KAAK,cAAc,IAAIA,EAAQ,EAAI,EACnC,KAAK,oBAAoBF,CAAK,EAE9BA,EAAM,eAAc,CACxB,CAEA,gBAAgBA,EAAO,CACnB,KAAK,oBAAoBA,CAAK,CAClC,CAEA,oBAAoBA,EAAO,CACvB,MAAMG,EAAO,KAAK,OAAO,sBAAqB,EAC9C,KAAK,cAAc,IACfH,EAAM,QAAUG,EAAK,KACrBH,EAAM,QAAUG,EAAK,GACjC,CACI,CAGA,iBAAiBH,EAAO,CACpBA,EAAM,eAAc,EAEpB,UAAWI,KAASJ,EAAM,eAAgB,CACtC,MAAMK,EAAW,KAAK,iBAAiBD,CAAK,EAC5C,KAAK,QAAQ,IAAIA,EAAM,WAAYC,CAAQ,EAC3C,KAAK,aAAa,IAAID,EAAM,WAAYC,EAAS,OAAO,EAGpD,KAAK,iBACL,KAAK,gBAAgB,iBAAiBD,EAAM,WAAYC,CAAQ,CAExE,CACJ,CAEA,eAAeL,EAAO,CAClBA,EAAM,eAAc,EAEpB,UAAWI,KAASJ,EAAM,eAAgB,CACtC,MAAMK,EAAW,KAAK,iBAAiBD,CAAK,EAC5C,KAAK,WAAW,IAAIA,EAAM,WAAYC,CAAQ,EAC9C,KAAK,QAAQ,OAAOD,EAAM,UAAU,EAGhC,KAAK,iBACL,KAAK,gBAAgB,eAAeA,EAAM,UAAU,CAE5D,CACJ,CAEA,gBAAgBJ,EAAO,CACnBA,EAAM,eAAc,EAEpB,UAAWI,KAASJ,EAAM,eAAgB,CACtC,MAAMK,EAAW,KAAK,iBAAiBD,CAAK,EAC5C,KAAK,QAAQ,IAAIA,EAAM,WAAYC,CAAQ,EAGvC,KAAK,iBACL,KAAK,gBAAgB,gBAAgBD,EAAM,WAAYC,CAAQ,CAEvE,CACJ,CAEA,iBAAiBD,EAAO,CACpB,MAAMD,EAAO,KAAK,OAAO,sBAAqB,EAC9C,OAAO,IAAIlB,EACPmB,EAAM,QAAUD,EAAK,KACrBC,EAAM,QAAUD,EAAK,GACjC,CACI,CAGA,UAAUF,EAAK,CACX,OAAO,KAAK,KAAK,IAAIA,CAAG,GAAK,EACjC,CAEA,aAAaA,EAAK,CACd,OAAO,KAAK,YAAY,IAAIA,CAAG,GAAK,EACxC,CAEA,cAAcA,EAAK,CACf,OAAO,KAAK,aAAa,IAAIA,CAAG,GAAK,EACzC,CAEA,YAAYC,EAAS,EAAG,CACpB,OAAO,KAAK,aAAa,IAAIA,CAAM,GAAK,EAC5C,CAEA,eAAeA,EAAS,EAAG,CACvB,OAAO,KAAK,aAAa,IAAIA,CAAM,GAAK,EAC5C,CAEA,gBAAgBA,EAAS,EAAG,CACxB,OAAO,KAAK,cAAc,IAAIA,CAAM,GAAK,EAC7C,CAGA,aAAaI,EAAQ,CACjB,MAAMC,EAAO,KAAK,YAAY,IAAID,CAAM,EACxC,OAAKC,EAEEA,EAAK,KAAKN,GAAO,KAAK,UAAUA,CAAG,CAAC,EAFzB,EAGtB,CAEA,gBAAgBK,EAAQ,CACpB,MAAMC,EAAO,KAAK,YAAY,IAAID,CAAM,EACxC,OAAKC,EAEEA,EAAK,KAAKN,GAAO,KAAK,aAAaA,CAAG,CAAC,EAF5B,EAGtB,CAEA,iBAAiBK,EAAQ,CACrB,MAAMC,EAAO,KAAK,YAAY,IAAID,CAAM,EACxC,OAAKC,EAEEA,EAAK,KAAKN,GAAO,KAAK,cAAcA,CAAG,CAAC,EAF7B,EAGtB,CAGA,mBAAoB,CAChB,MAAMO,EAAW,IAAIvB,EAAQ,EAAG,CAAC,EAQjC,GANI,KAAK,aAAa,UAAU,IAAGuB,EAAS,GAAK,GAC7C,KAAK,aAAa,WAAW,IAAGA,EAAS,GAAK,GAC9C,KAAK,aAAa,QAAQ,IAAGA,EAAS,GAAK,GAC3C,KAAK,aAAa,UAAU,IAAGA,EAAS,GAAK,GAG7C,KAAK,iBAAmB,KAAK,gBAAgB,SAAQ,EAAI,CACzD,MAAMC,EAAgB,KAAK,gBAAgB,SAAQ,EACnDD,EAAS,WAAWC,CAAa,CACrC,CAGA,OAAID,EAAS,UAAS,EAAK,GACvBA,EAAS,iBAAgB,EAGtBA,CACX,CAGA,cAAcF,EAAQC,EAAM,CACxB,KAAK,YAAY,IAAID,EAAQ,MAAM,QAAQC,CAAI,EAAIA,EAAO,CAACA,CAAI,CAAC,CACpE,CAEA,cAAcD,EAAQL,EAAK,CACvB,MAAMS,EAAW,KAAK,YAAY,IAAIJ,CAAM,GAAK,CAAA,EACjDI,EAAS,KAAKT,CAAG,EACjB,KAAK,YAAY,IAAIK,EAAQI,CAAQ,CACzC,CAEA,iBAAiBJ,EAAQL,EAAK,CAE1B,MAAMU,GADW,KAAK,YAAY,IAAIL,CAAM,GAAK,CAAA,GACvB,OAAOM,GAAKA,IAAMX,CAAG,EAC/C,KAAK,YAAY,IAAIK,EAAQK,CAAQ,CACzC,CAGA,UAAUV,EAAK,CACX,UAAWM,KAAQ,KAAK,YAAY,OAAM,EACtC,GAAIA,EAAK,SAASN,CAAG,EACjB,MAAO,GAGf,MAAO,EACX,CAGA,qBAAsB,CAClB,KAAK,gBAAkB,IAAIY,EAAgB,KAAK,MAAM,CAC1D,CAGA,QAAS,CAEL,KAAK,YAAY,MAAK,EACtB,KAAK,aAAa,MAAK,EACvB,KAAK,aAAa,MAAK,EACvB,KAAK,cAAc,MAAK,EACxB,KAAK,aAAa,MAAK,EACvB,KAAK,WAAW,MAAK,EAGjB,KAAK,iBACL,KAAK,gBAAgB,OAAM,CAEnC,CAGA,OAAOC,EAAK,CACJ,KAAK,iBACL,KAAK,gBAAgB,OAAOA,CAAG,CAEvC,CAGA,SAAU,CACN,SAAS,oBAAoB,UAAW,KAAK,aAAa,EAC1D,SAAS,oBAAoB,QAAS,KAAK,WAAW,EAEtD,KAAK,OAAO,oBAAoB,YAAa,KAAK,eAAe,EACjE,KAAK,OAAO,oBAAoB,UAAW,KAAK,aAAa,EAC7D,KAAK,OAAO,oBAAoB,YAAa,KAAK,eAAe,EAEjE,KAAK,OAAO,oBAAoB,aAAc,KAAK,gBAAgB,EACnE,KAAK,OAAO,oBAAoB,WAAY,KAAK,cAAc,EAC/D,KAAK,OAAO,oBAAoB,YAAa,KAAK,eAAe,EAE7D,KAAK,iBACL,KAAK,gBAAgB,QAAO,EAGhC,QAAQ,IAAI,wBAAwB,CACxC,CACJ,CAKA,MAAMD,CAAgB,CAClB,YAAYd,EAAQ,CAChB,KAAK,OAASA,EACd,KAAK,OAAS,GACd,KAAK,QAAU,KAGf,KAAK,OAAS,IAAId,EAAQ,IAAKc,EAAO,OAAS,GAAG,EAClD,KAAK,aAAe,IAAId,EAAQ,IAAKc,EAAO,OAAS,GAAG,EACxD,KAAK,YAAc,GACnB,KAAK,SAAW,GAGhB,KAAK,WAAa,GAClB,KAAK,WAAa,GAClB,KAAK,UAAY,2BACjB,KAAK,UAAY,0BACrB,CAEA,iBAAiBgB,EAASC,EAAU,CAEfA,EAAS,SAAS,KAAK,MAAM,GAC9B,KAAK,aACjB,KAAK,OAAS,GACd,KAAK,QAAUD,EACf,KAAK,aAAa,cAAcC,CAAQ,EACxC,KAAK,kBAAiB,EAE9B,CAEA,gBAAgBD,EAASC,EAAU,CAC3B,KAAK,QAAU,KAAK,UAAYD,IAChC,KAAK,aAAa,cAAcC,CAAQ,EACxC,KAAK,kBAAiB,EAE9B,CAEA,eAAeD,EAAS,CAChB,KAAK,QAAU,KAAK,UAAYA,IAChC,KAAK,OAAS,GACd,KAAK,QAAU,KACf,KAAK,aAAa,cAAc,KAAK,MAAM,EAEnD,CAEA,mBAAoB,CAChB,MAAME,EAAS,KAAK,aAAa,SAAS,KAAK,MAAM,EACjDA,EAAO,YAAc,KAAK,cAC1BA,EAAO,iBAAgB,EAAG,gBAAgB,KAAK,WAAW,EAC1D,KAAK,aAAe,KAAK,OAAO,IAAIA,CAAM,EAElD,CAEA,UAAW,CACP,GAAI,CAAC,KAAK,OAAQ,OAAO,IAAIhC,EAAQ,EAAG,CAAC,EAEzC,MAAMgC,EAAS,KAAK,aAAa,SAAS,KAAK,MAAM,EAC/CzB,EAAYyB,EAAO,UAAS,EAAK,KAAK,YAE5C,OAAIzB,EAAY,KAAK,SACV,IAAIP,EAAQ,EAAG,CAAC,EAGpBgC,EAAO,YAAY,SAASzB,CAAS,CAChD,CAEA,UAAW,CACP,OAAO,KAAK,MAChB,CAEA,QAAS,CAEL,KAAK,OAAO,IAAI,IAAK,KAAK,OAAO,OAAS,GAAG,EACxC,KAAK,QACN,KAAK,aAAa,cAAc,KAAK,MAAM,CAEnD,CAEA,OAAOsB,EAAK,CAERA,EAAI,KAAI,EACRA,EAAI,UAAY,KAAK,UACrBA,EAAI,YAAc,2BAClBA,EAAI,UAAY,EAEhBA,EAAI,UAAS,EACbA,EAAI,IAAI,KAAK,OAAO,EAAG,KAAK,OAAO,EAAG,KAAK,WAAY,EAAG,KAAK,GAAK,CAAC,EACrEA,EAAI,KAAI,EACRA,EAAI,OAAM,EAGVA,EAAI,UAAY,KAAK,UACrBA,EAAI,UAAS,EACbA,EAAI,IAAI,KAAK,aAAa,EAAG,KAAK,aAAa,EAAG,KAAK,WAAY,EAAG,KAAK,GAAK,CAAC,EACjFA,EAAI,KAAI,EACRA,EAAI,OAAM,EAEVA,EAAI,QAAO,CACf,CAEA,SAAU,CAEV,CACJ,OChcO,IAAAI,GAAAC,EAAA,KAAiB,CACpB,YAAYjC,EAAI,EAAGC,EAAI,EAAG,CACtB,KAAK,SAAW,IAAIF,EAAQC,EAAGC,CAAC,EAChC,KAAK,SAAW,IAAIF,EAAQ,EAAG,CAAC,EAChC,KAAK,aAAe,IAAIA,EAAQ,EAAG,CAAC,EAGpC,KAAK,SAAW,EAChB,KAAK,MAAQ,IAAIA,EAAQ,EAAG,CAAC,EAG7B,KAAK,OAAS,GACd,KAAK,QAAU,GACf,KAAK,UAAY,GAGjB,KAAK,gBAAkB,EACvB,KAAK,gBAAkB,CAAE,EAAG,EAAG,EAAG,EAAG,MAAO,EAAG,OAAQ,CAAC,EAGxD,KAAK,GAAKkC,EAAW,WAAU,EAG/B,KAAK,KAAO,IAAI,GACpB,CAIA,OAAO,YAAa,CAChB,MAAO,EAAEA,EAAW,SACxB,CAGA,OAAOC,EAAW,CACT,KAAK,SAGV,KAAK,SAAS,WAAW,KAAK,aAAa,SAASA,EAAY,GAAI,CAAC,EACrE,KAAK,SAAS,WAAW,KAAK,SAAS,SAASA,EAAY,GAAI,CAAC,EAGjE,KAAK,sBAAqB,EAC9B,CAGA,OAAON,EAAKO,EAAgB,EAAG,CAC3B,GAAI,CAAC,KAAK,QAAS,OAGnB,MAAMC,EAAY,KAAK,SAAS,IAAI,KAAK,SAAS,SAASD,EAAgB,GAAI,CAAC,EAGhFP,EAAI,KAAI,EACRA,EAAI,UAAUQ,EAAU,EAAGA,EAAU,CAAC,EACtCR,EAAI,OAAO,KAAK,QAAQ,EACxBA,EAAI,MAAM,KAAK,MAAM,EAAG,KAAK,MAAM,CAAC,EAGhC,KAAK,gBAAkB,IACvBA,EAAI,YAAc,UAClBA,EAAI,UAAS,EACbA,EAAI,IAAI,EAAG,EAAG,KAAK,gBAAiB,EAAG,KAAK,GAAK,CAAC,EAClDA,EAAI,OAAM,GAGdA,EAAI,QAAO,CACf,CAGA,uBAAwB,CACpB,KAAK,gBAAgB,EAAI,KAAK,SAAS,EAAI,KAAK,gBAChD,KAAK,gBAAgB,EAAI,KAAK,SAAS,EAAI,KAAK,gBAChD,KAAK,gBAAgB,MAAQ,KAAK,gBAAkB,EACpD,KAAK,gBAAgB,OAAS,KAAK,gBAAkB,CACzD,CAGA,aAAa1B,EAAO,CAChB,MAAI,CAAC,KAAK,QAAU,CAACA,EAAM,OAAe,GAGtC,KAAK,gBAAkB,GAAKA,EAAM,gBAAkB,EACnC,KAAK,SAAS,SAASA,EAAM,QAAQ,EACnC,KAAK,gBAAkBA,EAAM,gBAG7C,EACX,CAGA,SAAU,CACN,KAAK,UAAY,GACjB,KAAK,OAAS,GACd,KAAK,QAAU,EACnB,CAEA,OAAQ,CACJ,KAAK,SAAS,IAAI,EAAG,CAAC,EACtB,KAAK,SAAS,IAAI,EAAG,CAAC,EACtB,KAAK,aAAa,IAAI,EAAG,CAAC,EAC1B,KAAK,SAAW,EAChB,KAAK,MAAM,IAAI,EAAG,CAAC,EACnB,KAAK,OAAS,GACd,KAAK,QAAU,GACf,KAAK,UAAY,GACjB,KAAK,KAAK,MAAK,CACnB,CAGA,OAAOmC,EAAK,CACR,KAAK,KAAK,IAAIA,CAAG,CACrB,CAEA,UAAUA,EAAK,CACX,KAAK,KAAK,OAAOA,CAAG,CACxB,CAEA,OAAOA,EAAK,CACR,OAAO,KAAK,KAAK,IAAIA,CAAG,CAC5B,CAGA,WAAWnC,EAAO,CACd,OAAO,KAAK,SAAS,SAASA,EAAM,QAAQ,CAChD,CAEA,YAAYA,EAAO,CACf,OAAOA,EAAM,SAAS,SAAS,KAAK,QAAQ,EAAE,UAAS,CAC3D,CAEA,OAAOoC,EAAQ,CACX,MAAMC,EAAY,KAAK,YAAYD,CAAM,EACzC,KAAK,SAAWC,EAAU,MAAK,CACnC,CAGA,YAAYD,EAAQE,EAAON,EAAW,CAElC,MAAMZ,EADY,KAAK,YAAYgB,CAAM,EACd,SAASE,EAAQN,EAAY,GAAI,EAC5D,KAAK,SAAS,WAAWZ,CAAQ,CACrC,CAEA,WAAWmB,EAAO,CACd,KAAK,aAAa,WAAWA,CAAK,CACtC,CAGA,cAAcC,EAAQ,CAClB,OAAO,KAAK,SAAS,EAAIA,EAAO,MACzB,KAAK,SAAS,EAAIA,EAAO,OACzB,KAAK,SAAS,EAAIA,EAAO,KACzB,KAAK,SAAS,EAAIA,EAAO,MACpC,CAEA,iBAAiBA,EAAQ,CACjB,KAAK,SAAS,EAAIA,EAAO,OAAM,KAAK,SAAS,EAAIA,EAAO,OACxD,KAAK,SAAS,EAAIA,EAAO,QAAO,KAAK,SAAS,EAAIA,EAAO,MACzD,KAAK,SAAS,EAAIA,EAAO,MAAK,KAAK,SAAS,EAAIA,EAAO,QACvD,KAAK,SAAS,EAAIA,EAAO,SAAQ,KAAK,SAAS,EAAIA,EAAO,IAClE,CAEA,cAAcA,EAAQ,CAClB,KAAK,SAAS,EAAI,KAAK,IAAIA,EAAO,KAAM,KAAK,IAAIA,EAAO,MAAO,KAAK,SAAS,CAAC,CAAC,EAC/E,KAAK,SAAS,EAAI,KAAK,IAAIA,EAAO,IAAK,KAAK,IAAIA,EAAO,OAAQ,KAAK,SAAS,CAAC,CAAC,CACnF,CACJ,EA1IIC,EA3BGV,EA2BI,YAAY,GA3BhBA,GCCA,MAAMW,UAAmBC,CAAW,CACvC,YAAY7C,EAAI,EAAGC,EAAI,EAAG,CACtB,MAAMD,EAAGC,CAAC,EAGV,KAAK,MAAQ,IACb,KAAK,OAAS,EACd,KAAK,SAAW,IAChB,KAAK,IAAM,EAGX,KAAK,MAAQ,EACb,KAAK,OAAS,GACd,KAAK,gBAAkB,EACvB,KAAK,MAAQ,UACb,KAAK,WAAa,UAGlB,KAAK,eAAiB,CAAA,EACtB,KAAK,eAAiB,EACtB,KAAK,cAAgB,GAGrB,KAAK,KAAO,SACZ,KAAK,MAAQ,KAGb,KAAK,OAAO,YAAY,EAGxB,KAAK,OAAS,GACd,KAAK,QAAU,EACnB,CAUA,WAAW6B,EAAUS,EAAWC,EAAQ,IAAKM,EAAO,SAAUC,EAAQ,KAAM,CACxE,YAAK,SAAS,cAAcjB,CAAQ,EACpC,KAAK,SAAWS,EAAU,UAAS,EAAG,SAASC,CAAK,EACpD,KAAK,MAAQA,EACb,KAAK,KAAOM,EACZ,KAAK,MAAQC,EACb,KAAK,IAAM,EAGX,KAAK,eAAiB,CAAA,EAGtB,KAAK,mBAAkB,EAGvB,KAAK,OAAS,GACd,KAAK,QAAU,GACf,KAAK,UAAY,GAGjB,KAAK,KAAK,MAAK,EACf,KAAK,OAAO,YAAY,EACxB,KAAK,OAAOD,EAAO,YAAY,EAExB,IACX,CAKA,oBAAqB,CACjB,OAAQ,KAAK,KAAI,CACb,IAAK,SACD,KAAK,MAAQ,UACb,KAAK,WAAa,UAClB,KAAK,MAAQ,EACb,KAAK,OAAS,GACd,MACJ,IAAK,QACD,KAAK,MAAQ,UACb,KAAK,WAAa,UAClB,KAAK,MAAQ,EACb,KAAK,OAAS,EACd,MACJ,QACI,KAAK,MAAQ,UACb,KAAK,WAAa,UAClB,KAChB,CACI,CAMA,OAAOZ,EAAW,CACd,GAAK,KAAK,OAMV,IAHA,KAAK,KAAOA,EAGR,KAAK,KAAO,KAAK,SAAU,CAC3B,KAAK,QAAO,EACZ,MACJ,CAGA,KAAK,YAAW,EAGhB,MAAM,OAAOA,CAAS,EAGtB,KAAK,sBAAqB,EAC9B,CAKA,aAAc,CAEV,KAAK,eAAe,QAAQ,KAAK,SAAS,MAAK,CAAE,EAG7C,KAAK,eAAe,OAAS,KAAK,gBAClC,KAAK,eAAe,IAAG,CAE/B,CAOA,OAAON,EAAKO,EAAgB,EAAG,CAC3B,GAAI,CAAC,KAAK,QAAS,OAGnB,MAAMC,EAAY,KAAK,SAAS,IAAI,KAAK,SAAS,SAASD,EAAgB,GAAI,CAAC,EAEhFP,EAAI,KAAI,EAGR,KAAK,YAAYA,EAAKO,CAAa,EAGnC,KAAK,iBAAiBP,EAAKQ,CAAS,EAGhC,OAAO,YACP,KAAK,gBAAgBR,EAAKQ,CAAS,EAGvCR,EAAI,QAAO,CACf,CAOA,YAAYA,EAAKO,EAAe,CAC5B,GAAI,OAAK,eAAe,OAAS,GAEjC,CAAAP,EAAI,YAAc,KAAK,WACvBA,EAAI,UAAY,EAChBA,EAAI,QAAU,QAGd,QAAS,EAAI,EAAG,EAAI,KAAK,eAAe,OAAS,EAAG,IAAK,CACrD,MAAMoB,EAAQ,KAAK,IAAI,KAAK,cAAe,CAAC,EACtCC,EAAa,KAAK,eAAe,CAAC,EAClCC,EAAU,KAAK,eAAe,EAAI,CAAC,EAGzC,IAAIC,EAAmBF,EACvB,GAAI,IAAM,EAAG,CACT,MAAMG,EAAqB,KAAK,SAAS,SAASjB,EAAgB,GAAI,EACtEgB,EAAmBF,EAAW,IAAIG,CAAkB,CACxD,CAEAxB,EAAI,YAAcoB,EAClBpB,EAAI,UAAS,EACbA,EAAI,OAAOuB,EAAiB,EAAGA,EAAiB,CAAC,EACjDvB,EAAI,OAAOsB,EAAQ,EAAGA,EAAQ,CAAC,EAC/BtB,EAAI,OAAM,CACd,CAEAA,EAAI,YAAc,EACtB,CAOA,iBAAiBA,EAAKQ,EAAW,CAC7BR,EAAI,UAAUQ,EAAU,EAAGA,EAAU,CAAC,EAGtCR,EAAI,UAAY,KAAK,MACrBA,EAAI,SAAS,CAAC,KAAK,MAAQ,EAAG,CAAC,KAAK,OAAS,EAAG,KAAK,MAAO,KAAK,MAAM,EAGvEA,EAAI,UAAY,UAChBA,EAAI,SAAS,GAAI,CAAC,KAAK,OAAS,EAAG,EAAG,KAAK,MAAM,CACrD,CAMA,qBAAqBA,EAAK,CAEtBA,EAAI,UAAS,EACbA,EAAI,OAAO,EAAG,CAAC,KAAK,OAAS,CAAC,EAC9BA,EAAI,OAAO,KAAK,MAAQ,EAAG,CAAC,EAC5BA,EAAI,OAAO,EAAG,KAAK,OAAS,CAAC,EAC7BA,EAAI,OAAO,CAAC,KAAK,MAAQ,EAAG,CAAC,EAC7BA,EAAI,UAAS,EACbA,EAAI,KAAI,EACRA,EAAI,OAAM,EAGVA,EAAI,UAAY,KAAK,aAAa,KAAK,MAAO,EAAG,EACjDA,EAAI,UAAS,EACbA,EAAI,OAAO,EAAG,CAAC,KAAK,OAAS,CAAC,EAC9BA,EAAI,OAAO,KAAK,MAAQ,EAAG,CAAC,EAC5BA,EAAI,OAAO,EAAG,KAAK,OAAS,CAAC,EAC7BA,EAAI,OAAO,CAAC,KAAK,MAAQ,EAAG,CAAC,EAC7BA,EAAI,UAAS,EACbA,EAAI,KAAI,CACZ,CAMA,oBAAoBA,EAAK,CAErBA,EAAI,UAAS,EACbA,EAAI,QAAQ,EAAG,EAAG,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACpEA,EAAI,KAAI,EACRA,EAAI,OAAM,EAGVA,EAAI,UAAY,KAAK,aAAa,KAAK,MAAO,EAAG,EACjDA,EAAI,UAAS,EACbA,EAAI,QAAQ,EAAG,CAAC,KAAK,OAAS,EAAG,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACnFA,EAAI,KAAI,CACZ,CAOA,gBAAgBA,EAAKQ,EAAW,CAC5BR,EAAI,eAAc,EAGlBA,EAAI,YAAc,UAClBA,EAAI,UAAY,EAChBA,EAAI,UAAS,EACbA,EAAI,IAAIQ,EAAU,EAAGA,EAAU,EAAG,KAAK,gBAAiB,EAAG,KAAK,GAAK,CAAC,EACtER,EAAI,OAAM,EAGVA,EAAI,YAAc,UAClBA,EAAI,UAAY,EAChBA,EAAI,UAAS,EACbA,EAAI,OAAOQ,EAAU,EAAGA,EAAU,CAAC,EACnC,MAAMiB,EAAcjB,EAAU,IAAI,KAAK,SAAS,SAAS,GAAI,CAAC,EAC9DR,EAAI,OAAOyB,EAAY,EAAGA,EAAY,CAAC,EACvCzB,EAAI,OAAM,EAGVA,EAAI,UAAY,UAChBA,EAAI,KAAO,aACXA,EAAI,SAAS,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC,KAAMQ,EAAU,EAAI,GAAIA,EAAU,EAAI,EAAE,CAChF,CAOA,cAAcM,EAAQ,CAClB,MAAMY,EAAS,KAAK,IAAI,KAAK,MAAO,KAAK,MAAM,EAC/C,OAAO,KAAK,SAAS,EAAIZ,EAAO,KAAOY,GAChC,KAAK,SAAS,EAAIZ,EAAO,MAAQY,GACjC,KAAK,SAAS,EAAIZ,EAAO,IAAMY,GAC/B,KAAK,SAAS,EAAIZ,EAAO,OAASY,CAC7C,CAMA,YAAYpD,EAAO,CAEf,KAAK,QAAO,CAChB,CAKA,OAAQ,CACJ,MAAM,MAAK,EACX,KAAK,IAAM,EACX,KAAK,eAAiB,CAAA,EACtB,KAAK,KAAO,SACZ,KAAK,MAAQ,KACb,KAAK,MAAQ,IACb,KAAK,OAAS,EACd,KAAK,SAAW,GACpB,CAQA,YAAYqD,EAAOC,EAAQ,CAEvB,MAAMC,EAAMF,EAAM,QAAQ,IAAK,EAAE,EAC3BG,EAAI,KAAK,MAAM,SAASD,EAAI,OAAO,EAAG,CAAC,EAAG,EAAE,GAAK,EAAID,EAAO,EAC5DG,EAAI,KAAK,MAAM,SAASF,EAAI,OAAO,EAAG,CAAC,EAAG,EAAE,GAAK,EAAID,EAAO,EAC5DI,EAAI,KAAK,MAAM,SAASH,EAAI,OAAO,EAAG,CAAC,EAAG,EAAE,GAAK,EAAID,EAAO,EAClE,MAAO,OAAOE,CAAC,KAAKC,CAAC,KAAKC,CAAC,GAC/B,CAQA,aAAaL,EAAOC,EAAQ,CACxB,MAAMC,EAAMF,EAAM,QAAQ,IAAK,EAAE,EAC3BG,EAAI,KAAK,IAAI,IAAK,KAAK,MAAM,SAASD,EAAI,OAAO,EAAG,CAAC,EAAG,EAAE,GAAK,EAAID,EAAO,CAAC,EAC3EG,EAAI,KAAK,IAAI,IAAK,KAAK,MAAM,SAASF,EAAI,OAAO,EAAG,CAAC,EAAG,EAAE,GAAK,EAAID,EAAO,CAAC,EAC3EI,EAAI,KAAK,IAAI,IAAK,KAAK,MAAM,SAASH,EAAI,OAAO,EAAG,CAAC,EAAG,EAAE,GAAK,EAAID,EAAO,CAAC,EACjF,MAAO,OAAOE,CAAC,KAAKC,CAAC,KAAKC,CAAC,GAC/B,CACJ,CC7VO,MAAMC,CAAa,CACtB,YAAYd,EAAOe,EAAmB,CAClC,KAAK,MAAQf,EACb,KAAK,kBAAoBe,EAGzB,KAAK,SAAW,IAChB,KAAK,aAAe,EACpB,KAAK,QAAU,GAGf,KAAK,gBAAkB,IACvB,KAAK,iBAAmB,GACxB,KAAK,mBAAqB,IAC1B,KAAK,eAAiB,SAGtB,KAAK,eAAiB,SACtB,KAAK,gBAAkB,SACvB,KAAK,YAAc,KAAK,GAAK,EAG7B,KAAK,oBAAsB,IAC3B,KAAK,gBAAkB,EACvB,KAAK,qBAAuB,CAAA,EAG5B,KAAK,UAAY,KACjB,KAAK,YAAc,GAGnB,KAAK,yBAAwB,EAE7B,QAAQ,IAAI,sCAAuCf,EAAM,YAAY,IAAI,CAC7E,CAKA,0BAA2B,CAElB,KAAK,kBAAkB,MAAM,IAAI,YAAY,GAC9C,KAAK,kBAAkB,WACnB,aACA,IAAM,IAAIH,EACTmB,GAAeA,EAAW,MAAK,EAChC,EAChB,CAEI,CAMA,OAAO7B,EAAW,CAET,KAAK,UACN,KAAK,cAAgBA,EACjB,KAAK,cAAgB,KAAK,WAC1B,KAAK,QAAU,GACf,KAAK,aAAe,IAKxB,KAAK,gBAAkB,IACvB,KAAK,iBAAmBA,EACpB,KAAK,iBAAmB,IACxB,KAAK,qBAAuB,CAAA,GAGxC,CAOA,KAAKK,EAAYxC,EAAQ,KAAM,CAE3B,GADA,QAAQ,IAAI,uCAAwC,KAAK,OAAO,EAC5D,CAAC,KAAK,QACN,eAAQ,IAAI,kCAAkC,EACvC,GAIX,OAAQ,KAAK,eAAc,CACvB,IAAK,SACD,KAAK,WAAWwC,CAAS,EACzB,MACJ,IAAK,SACD,KAAK,WAAWA,CAAS,EACzB,MACJ,IAAK,SACD,KAAK,WAAWA,CAAS,EACzB,MACJ,IAAK,SACD,KAAK,WAAWA,CAAS,EACzB,MACJ,QACI,KAAK,WAAWA,CAAS,EACzB,KAChB,CAGQ,YAAK,QAAU,GACf,KAAK,aAAe,EAGpB,KAAK,mBAAkB,EAGvB,KAAK,cAAa,EAEX,EACX,CAMA,WAAWA,EAAW,CAClB,MAAMyB,EAAe,KAAK,gBAAe,EACzC,KAAK,iBAAiBA,EAAczB,CAAS,CACjD,CAMA,WAAWA,EAAW,CAClB,MAAMyB,EAAe,KAAK,gBAAe,EACnCjC,EAASQ,EAAU,cAAa,EAAG,SAAS,CAAC,EAGnD,KAAK,iBAAiByB,EAAa,SAASjC,CAAM,EAAGQ,CAAS,EAE9D,KAAK,iBAAiByB,EAAa,IAAIjC,CAAM,EAAGQ,CAAS,CAC7D,CAMA,WAAWA,EAAW,CAClB,MAAMyB,EAAe,KAAK,gBAAe,EACnCjC,EAASQ,EAAU,cAAa,EAAG,SAAS,EAAE,EAGpD,KAAK,iBAAiByB,EAAczB,CAAS,EAE7C,KAAK,iBAAiByB,EAAa,SAASjC,CAAM,EAAGQ,CAAS,EAE9D,KAAK,iBAAiByB,EAAa,IAAIjC,CAAM,EAAGQ,CAAS,CAC7D,CAMA,WAAWA,EAAW,CAClB,MAAMyB,EAAe,KAAK,gBAAe,EACnCC,EAAY1B,EAAU,MAAK,EAC3B2B,EAAY,KAAK,YAAc,EAGrC,QAASC,EAAI,GAAIA,GAAK,EAAGA,IAAK,CAC1B,MAAM9D,EAAQ4D,EAAaE,EAAID,EACzBE,EAAkBrE,EAAQ,UAAUM,CAAK,EAC/C,KAAK,iBAAiB2D,EAAcI,CAAe,CACvD,CACJ,CAOA,iBAAiBtC,EAAUS,EAAW,CAElC,MAAMwB,EAAa,IAAInB,EAGvBmB,EAAW,WACPjC,EACAS,EACA,KAAK,gBACL,KAAK,eACL,KAAK,KACjB,EAGQwB,EAAW,OAAS,KAAK,iBACzBA,EAAW,SAAW,KAAK,mBAG3B,KAAK,kBAAkB,IAAIA,CAAU,EAErC,QAAQ,IAAI,yBAA0BA,EAAW,SAAS,SAAQ,EAAI,iBAAkBA,EAAW,SAAS,SAAQ,CAAE,CAC1H,CAMA,iBAAkB,CAEd,MAAMM,EAAW,KAAK,MAAM,SAAS,MAAK,EAGpCC,EAAgB,IAAIvE,EAAQ,EAAG,CAAC,KAAK,MAAM,OAAS,EAAI,CAAC,EAE/D,OAAOsE,EAAS,IAAIC,CAAa,CACrC,CAKA,oBAAqB,CAMjB,OALA,KAAK,gBAAkB,KAAK,oBAG5B,KAAK,qBAAuB,CAAA,EAEpB,KAAK,eAAc,CACvB,IAAK,SACD,KAAK,qBAAqB,KAAK,KAAK,gBAAe,CAAE,EACrD,MACJ,IAAK,SACD,MAAMC,EAAU,KAAK,gBAAe,EAC9BxC,EAAS,IAAIhC,EAAQ,EAAG,CAAC,EAC/B,KAAK,qBAAqB,KAAKwE,EAAQ,SAASxC,CAAM,CAAC,EACvD,KAAK,qBAAqB,KAAKwC,EAAQ,IAAIxC,CAAM,CAAC,EAClD,MACJ,IAAK,SACD,MAAMyC,EAAW,KAAK,gBAAe,EAC/BC,EAAU,IAAI1E,EAAQ,GAAI,CAAC,EACjC,KAAK,qBAAqB,KAAKyE,CAAQ,EACvC,KAAK,qBAAqB,KAAKA,EAAS,SAASC,CAAO,CAAC,EACzD,KAAK,qBAAqB,KAAKD,EAAS,IAAIC,CAAO,CAAC,EACpD,MACJ,IAAK,SAED,KAAK,qBAAqB,KAAK,KAAK,gBAAe,CAAE,EACrD,KAChB,CACI,CAKA,eAAgB,CAER,KAAK,WAAa,OAAO,KAAK,UAAU,MAAS,aACjD,KAAK,UAAU,OAAS,KAAK,YAC7B,KAAK,UAAU,YAAc,EAC7B,KAAK,UAAU,OAAO,MAAM,GAAK,CAE7B,QAAQ,KAAK,6BAA8B,CAAC,CAChD,CAAC,EAET,CAMA,OAAO7C,EAAK,CACJ,KAAK,gBAAkB,GACvB,KAAK,kBAAkBA,CAAG,CAElC,CAMA,kBAAkBA,EAAK,CACnB,MAAM8C,EAAa,KAAK,gBAAkB,KAAK,oBAE/C9C,EAAI,KAAI,EACRA,EAAI,YAAc8C,EAElB,UAAWC,KAAY,KAAK,qBAAsB,CAE9C,MAAMC,EAAWhD,EAAI,qBACjB+C,EAAS,EAAGA,EAAS,EAAG,EACxBA,EAAS,EAAGA,EAAS,EAAG,EACxC,EACYC,EAAS,aAAa,EAAG,SAAS,EAClCA,EAAS,aAAa,GAAK,SAAS,EACpCA,EAAS,aAAa,GAAK,SAAS,EACpCA,EAAS,aAAa,EAAG,uBAAuB,EAEhDhD,EAAI,UAAYgD,EAChBhD,EAAI,UAAS,EACbA,EAAI,IAAI+C,EAAS,EAAGA,EAAS,EAAG,GAAI,EAAG,KAAK,GAAK,CAAC,EAClD/C,EAAI,KAAI,EAGRA,EAAI,UAAY,UAChBA,EAAI,UAAS,EACbA,EAAI,IAAI+C,EAAS,EAAGA,EAAS,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACjD/C,EAAI,KAAI,CACZ,CAEAA,EAAI,QAAO,CACf,CAMA,WAAWiD,EAAS,CACM,CAAC,SAAU,SAAU,SAAU,QAAQ,EAC3C,SAASA,CAAO,GAC9B,KAAK,eAAiBA,EACtB,QAAQ,IAAI,8BAA8BA,CAAO,EAAE,GAEnD,QAAQ,KAAK,2BAA2BA,CAAO,EAAE,CAEzD,CAMA,YAAYC,EAAQ,CAChB,KAAK,SAAW,KAAK,IAAI,GAAIA,CAAM,CACvC,CAMA,mBAAmBtC,EAAO,CACtB,KAAK,gBAAkB,KAAK,IAAI,IAAKA,CAAK,CAC9C,CAMA,oBAAoBuC,EAAQ,CACxB,KAAK,iBAAmB,KAAK,IAAI,EAAGA,CAAM,CAC9C,CAMA,eAAeC,EAAc,CACzB,KAAK,YAAc,KAAK,IAAI,EAAG,KAAK,IAAI,KAAK,GAAIA,CAAY,CAAC,CAClE,CAMA,SAAU,CACN,OAAO,KAAK,OAChB,CAMA,qBAAsB,CAClB,OAAI,KAAK,QAAgB,EAClB,KAAK,aAAe,KAAK,QACpC,CAKA,eAAgB,CACZ,KAAK,QAAU,GACf,KAAK,aAAe,CACxB,CAMA,oBAAoBC,EAAS,CACzB,GAAIA,EACA,KAAK,gBAAkB,KAAK,eAC5B,KAAK,WAAW,QAAQ,MACrB,CAEH,MAAMC,EAAiB,KAAK,iBAAmB,SAC/C,KAAK,WAAWA,CAAc,CAClC,CACJ,CAMA,UAAW,CACP,MAAO,CACH,QAAS,KAAK,eACd,SAAU,KAAK,SACf,gBAAiB,KAAK,gBACtB,iBAAkB,KAAK,iBACvB,QAAS,KAAK,QACd,iBAAkB,KAAK,oBAAmB,CACtD,CACI,CACJ,CCzZO,MAAMC,UAAmBtC,CAAW,CACvC,YAAY7C,EAAGC,EAAGmF,EAAaC,EAAcvB,EAAoB,KAAM,CACnE,MAAM9D,EAAGC,CAAC,EAGV,KAAK,YAAcmF,EACnB,KAAK,aAAeC,EAGpB,KAAK,SAAW,IAChB,KAAK,aAAe,IACpB,KAAK,SAAW,IAGhB,KAAK,UAAY,IACjB,KAAK,OAAS,KAAK,UACnB,KAAK,SAAW,EAChB,KAAK,MAAQ,KAAK,SAClB,KAAK,eAAiB,GACtB,KAAK,wBAA0B,IAC/B,KAAK,qBAAuB,EAC5B,KAAK,YAAc,GAGnB,KAAK,iBAAmB,EACxB,KAAK,oBAAsB,IAC3B,KAAK,WAAa,GAGlB,KAAK,MAAQ,GACb,KAAK,OAAS,GACd,KAAK,gBAAkB,GAGvB,KAAK,gBAAkB,KAAK,IAAI,KAAK,MAAO,KAAK,MAAM,EAAI,EAG3D,KAAK,cAAgB,EACrB,KAAK,uBAAyB,EAG9B,KAAK,SAAW,GAChB,KAAK,cAAgB,IAAItF,EAAQ,EAAG,CAAC,EAGrC,KAAK,aAAe,KAChB+D,IACA,KAAK,aAAe,IAAID,EAAa,KAAMC,CAAiB,GAIhE,KAAK,OAAO,QAAQ,EAEpB,QAAQ,IAAI,kCAAmC,KAAK,SAAS,SAAQ,CAAE,CAC3E,CAOA,OAAO5B,EAAWoD,EAAgB,IAAIvF,EAAQ,EAAG,CAAC,EAAG,CACjD,GAAK,KAAK,OAOV,IAJA,KAAK,cAAgBuF,EAAc,MAAK,EACxC,KAAK,SAAWA,EAAc,UAAS,EAAK,GAGxC,KAAK,SAAU,CAMf,MAAMC,EAJiBD,EAAc,SAAS,KAAK,QAAQ,EAGvB,SAAS,KAAK,QAAQ,EACnB,SAAS,KAAK,aAAepD,EAAY,GAAI,EAEpF,KAAK,SAAS,WAAWqD,CAAiB,EAGtC,KAAK,SAAS,UAAS,EAAK,KAAK,WACjC,KAAK,SAAW,KAAK,SAAS,UAAS,EAAG,SAAS,KAAK,QAAQ,EAExE,MAEI,KAAK,SAAS,gBAAgB,KAAK,IAAI,KAAK,SAAUrD,EAAY,KAAK,CAAC,EAGpE,KAAK,SAAS,UAAS,EAAK,GAC5B,KAAK,SAAS,IAAI,EAAG,CAAC,EAK9B,KAAK,SAAS,WAAW,KAAK,SAAS,SAASA,EAAY,GAAI,CAAC,EAGjE,KAAK,gBAAe,EAGpB,KAAK,eAAiBA,EAAY,IAGlC,KAAK,mBAAmBA,CAAS,EAGjC,KAAK,sBAAqB,EAGtB,KAAK,cACL,KAAK,aAAa,OAAOA,CAAS,EAE1C,CAMA,iBAAkB,CACd,MAAMsD,EAAY,KAAK,gBACjBC,EAAa,KAAK,YAAc,KAAK,gBACrCC,EAAW,KAAK,gBAChBC,EAAc,KAAK,aAAe,KAAK,gBAKzC,KAAK,SAAS,EAAIH,GAClB,KAAK,SAAS,EAAIA,EAClB,KAAK,SAAS,EAAI,KAAK,IAAI,EAAG,KAAK,SAAS,CAAC,GAEtC,KAAK,SAAS,EAAIC,IACzB,KAAK,SAAS,EAAIA,EAClB,KAAK,SAAS,EAAI,KAAK,IAAI,EAAG,KAAK,SAAS,CAAC,GAK7C,KAAK,SAAS,EAAIC,GAClB,KAAK,SAAS,EAAIA,EAClB,KAAK,SAAS,EAAI,KAAK,IAAI,EAAG,KAAK,SAAS,CAAC,GAEtC,KAAK,SAAS,EAAIC,IACzB,KAAK,SAAS,EAAIA,EAClB,KAAK,SAAS,EAAI,KAAK,IAAI,EAAG,KAAK,SAAS,CAAC,EAQrD,CAOA,OAAO/D,EAAKO,EAAgB,EAAG,CAC3B,GAAI,CAAC,KAAK,QAAS,OAGnB,MAAMC,EAAY,KAAK,SAAS,IAAI,KAAK,SAAS,SAASD,EAAgB,GAAI,CAAC,EAEhFP,EAAI,KAAI,EACRA,EAAI,UAAUQ,EAAU,EAAGA,EAAU,CAAC,EACtCR,EAAI,OAAO,KAAK,QAAQ,EAGxB,KAAK,aAAaA,CAAG,EAGjB,KAAK,UACL,KAAK,oBAAoBA,CAAG,EAI5B,OAAO,YACP,KAAK,cAAcA,CAAG,EAG1BA,EAAI,QAAO,EAGP,KAAK,cACL,KAAK,aAAa,OAAOA,CAAG,CAEpC,CAMA,aAAaA,EAAK,CAEd,IAAIgE,EAAY,EACZC,EAAY,UACZC,EAAc,UAGlB,GAAI,KAAK,eAAgB,CAErB,MAAMC,EAAa,KAAK,IAAI,KAAK,qBAAuB,EAAa,KAAK,GAAK,GAAI,EACnFH,EAAY,GAAM,GAAM,KAAK,IAAIG,CAAU,CAC/C,CAGA,GAAI,KAAK,WAAY,CACjB,MAAMC,EAAiB,KAAK,iBAAmB,KAAK,oBACpDH,EAAY,KAAK,iBAAiB,UAAW,UAAWG,CAAc,EACtEF,EAAc,KAAK,iBAAiB,UAAW,UAAWE,CAAc,CAC5E,CAGApE,EAAI,YAAcgE,EAGlBhE,EAAI,UAAYiE,EAChBjE,EAAI,YAAckE,EAClBlE,EAAI,UAAY,EAEhBA,EAAI,UAAS,EAEbA,EAAI,OAAO,EAAG,CAAC,KAAK,OAAS,CAAC,EAC9BA,EAAI,OAAO,CAAC,KAAK,MAAQ,EAAG,KAAK,OAAS,CAAC,EAC3CA,EAAI,OAAO,KAAK,MAAQ,EAAG,KAAK,OAAS,CAAC,EAC1CA,EAAI,UAAS,EACbA,EAAI,KAAI,EACRA,EAAI,OAAM,EAGVA,EAAI,UAAY,UAChBA,EAAI,UAAS,EACbA,EAAI,QAAQ,EAAG,CAAC,KAAK,OAAS,EAAG,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACnFA,EAAI,KAAI,EAGRA,EAAI,UAAY,UAChBA,EAAI,SAAS,CAAC,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,EAAG,KAAK,OAAS,CAAC,EAC9EA,EAAI,SAAS,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,KAAK,MAAQ,EAAG,KAAK,OAAS,CAAC,EAG7EA,EAAI,UAAY,UAChBA,EAAI,UAAS,EACbA,EAAI,QAAQ,CAAC,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,EAAG,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACrEA,EAAI,QAAQ,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAG,EAAG,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACpEA,EAAI,KAAI,EAGRA,EAAI,YAAc,CACtB,CAMA,oBAAoBA,EAAK,CAErB,MAAMqE,EAAoB,KAAK,SAAS,UAAS,EAAK,KAAK,SACrDC,EAAiB,KAAK,IAAI,KAAK,cAAgB,KAAK,uBAAyB,KAAK,GAAK,CAAC,EAGxFC,EAAkB,GAAKF,EACvBG,EAAiB,EAAIF,EAAiBD,EACtCI,EAAcF,EAAkBC,EAElCC,EAAc,IAEd,KAAK,kBAAkBzE,EAAK,CAAC,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAGyE,CAAW,EAGzE,KAAK,kBAAkBzE,EAAK,KAAK,MAAQ,EAAG,KAAK,OAAS,EAAGyE,CAAW,GAI5E,KAAK,yBAAyBzE,EAAKqE,EAAmBC,CAAc,CACxE,CASA,kBAAkBtE,EAAK5B,EAAGC,EAAGqG,EAAQ,CACjC,MAAM1B,EAAWhD,EAAI,qBAAqB5B,EAAGC,EAAGD,EAAGC,EAAIqG,CAAM,EAC7D1B,EAAS,aAAa,EAAG,SAAS,EAClCA,EAAS,aAAa,GAAK,SAAS,EACpCA,EAAS,aAAa,EAAG,oBAAoB,EAE7ChD,EAAI,UAAYgD,EAChBhD,EAAI,UAAS,EACbA,EAAI,OAAO5B,EAAI,EAAGC,CAAC,EACnB2B,EAAI,OAAO5B,EAAI,EAAGC,CAAC,EACnB2B,EAAI,OAAO5B,EAAI,EAAGC,EAAIqG,CAAM,EAC5B1E,EAAI,OAAO5B,EAAI,EAAGC,EAAIqG,CAAM,EAC5B1E,EAAI,UAAS,EACbA,EAAI,KAAI,CACZ,CAQA,yBAAyBA,EAAK2E,EAAWL,EAAgB,CACrD,MAAMM,EAAe,EAAID,EACnBvD,EAAQ,GAAMuD,EAgBpB,GAbI,KAAK,IAAI,KAAK,cAAc,CAAC,EAAI,KACjC3E,EAAI,UAAY,uBAAuBoB,CAAK,IAExC,KAAK,cAAc,EAAI,EAEvBpB,EAAI,SAAS,CAAC,KAAK,MAAQ,EAAI4E,EAAc,GAAIA,EAAc,CAAC,EAGhE5E,EAAI,SAAS,KAAK,MAAQ,EAAG,GAAI4E,EAAc,CAAC,GAKpD,KAAK,cAAc,EAAI,IAAM,CAC7B5E,EAAI,UAAY,qBAAqBoB,CAAK,IAC1C,MAAMyD,EAAwB,EAAIF,GAAa,EAAI,GAAML,GACzDtE,EAAI,SAAS,GAAI,CAAC,KAAK,OAAS,EAAI6E,EAAuB,EAAGA,CAAqB,CACvF,CACJ,CAMA,cAAc7E,EAAK,CASf,GAPAA,EAAI,YAAc,UAClBA,EAAI,UAAY,EAChBA,EAAI,UAAS,EACbA,EAAI,IAAI,EAAG,EAAG,KAAK,gBAAiB,EAAG,KAAK,GAAK,CAAC,EAClDA,EAAI,OAAM,EAGN,KAAK,SAAS,UAAS,EAAK,EAAG,CAC/BA,EAAI,YAAc,UAClBA,EAAI,UAAY,EAChBA,EAAI,UAAS,EACbA,EAAI,OAAO,EAAG,CAAC,EACf,MAAM8E,EAAgB,GACtB9E,EAAI,OAAO,KAAK,SAAS,EAAI8E,EAAe,KAAK,SAAS,EAAIA,CAAa,EAC3E9E,EAAI,OAAM,CACd,CAGAA,EAAI,UAAY,UAChBA,EAAI,SAAS,GAAI,GAAI,EAAG,CAAC,CAC7B,CAMA,iBAAkB,CACd,OAAO,KAAK,SAAS,UAAS,CAClC,CAMA,mBAAoB,CAChB,MAAM4D,EAAY,KAAK,gBACjBC,EAAa,KAAK,YAAc,KAAK,gBACrCC,EAAW,KAAK,gBAChBC,EAAc,KAAK,aAAe,KAAK,gBAE7C,MAAO,CACH,KAAM,KAAK,SAAS,GAAKH,EACzB,MAAO,KAAK,SAAS,GAAKC,EAC1B,IAAK,KAAK,SAAS,GAAKC,EACxB,OAAQ,KAAK,SAAS,GAAKC,CACvC,CACI,CAOA,gBAAgB3F,EAAGC,EAAG,CAClB,KAAK,SAAS,IAAID,EAAGC,CAAC,EACtB,KAAK,SAAS,IAAI,EAAG,CAAC,EACtB,KAAK,SAAW,EAChB,KAAK,cAAgB,EACrB,KAAK,SAAW,GAChB,KAAK,cAAc,IAAI,EAAG,CAAC,EAC3B,KAAK,OAAS,GACd,KAAK,QAAU,GACf,KAAK,UAAY,EAGrB,CAOA,uBAAuB0G,EAAOC,EAAQ,CAClC,KAAK,YAAcD,EACnB,KAAK,aAAeC,EAGpB,KAAK,gBAAe,CACxB,CAOA,KAAKrE,EAAYxC,EAAQ,KAAM,CAE3B,GADA,QAAQ,IAAI,iDAAkD,CAAC,CAAC,KAAK,YAAY,EAC7E,KAAK,aAAc,CACnB,MAAM8G,EAAS,KAAK,aAAa,KAAKtE,CAAS,EAC/C,eAAQ,IAAI,gCAAiCsE,CAAM,EAC5CA,CACX,CACA,MAAO,EACX,CAMA,gBAAgBC,EAAc,CAC1B,KAAK,aAAeA,CACxB,CAMA,iBAAkB,CACd,OAAO,KAAK,YAChB,CAMA,SAAU,CACN,OAAO,KAAK,aAAe,KAAK,aAAa,QAAO,EAAK,EAC7D,CAMA,iBAAiBjC,EAAS,CAClB,KAAK,cACL,KAAK,aAAa,WAAWA,CAAO,CAE5C,CAMA,gBAAiB,CACb,OAAO,KAAK,aAAe,KAAK,aAAa,SAAQ,EAAK,IAC9D,CAMA,mBAAmB3C,EAAW,CAEtB,KAAK,iBACL,KAAK,sBAAwBA,EACzB,KAAK,sBAAwB,IAC7B,KAAK,eAAiB,GACtB,KAAK,qBAAuB,IAKhC,KAAK,aACL,KAAK,kBAAoBA,EACrB,KAAK,kBAAoB,IACzB,KAAK,WAAa,GAClB,KAAK,iBAAmB,GAGpC,CAOA,WAAW6C,EAAQ,CAEf,GAAI,KAAK,gBAAkB,KAAK,YAC5B,MAAO,CACH,YAAa,EACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,UAAW,KAAK,WAChC,EAIQ,MAAMgC,EAAe,KAAK,IAAIhC,EAAQ,KAAK,MAAM,EACjD,YAAK,QAAUgC,EAGf,KAAK,WAAa,GAClB,KAAK,iBAAmB,KAAK,oBAE7B,QAAQ,IAAI,mBAAmBA,CAAY,oBAAoB,KAAK,MAAM,IAAI,KAAK,SAAS,YAAY,KAAK,KAAK,EAAE,EAGhH,KAAK,QAAU,EACf,KAAK,YAAW,GAGhB,KAAK,eAAiB,GACtB,KAAK,qBAAuB,KAAK,yBAG9B,CACH,YAAaA,EACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,UAAW,KAAK,WAC5B,CACI,CAKA,aAAc,CACV,KAAK,OAAS,EACd,KAAK,QAEL,QAAQ,IAAI,0CAA0C,KAAK,KAAK,EAAE,EAE9D,KAAK,OAAS,GAEd,KAAK,YAAc,GACnB,KAAK,OAAS,GACd,QAAQ,IAAI,gCAAgC,GAG5C,KAAK,QAAO,CAEpB,CAKA,SAAU,CAEN,KAAK,OAAS,KAAK,UAGnB,MAAMC,EAAS,KAAK,YAAc,EAC5BC,EAAS,KAAK,aAAe,IACnC,KAAK,gBAAgBD,EAAQC,CAAM,EAGnC,KAAK,eAAiB,GACtB,KAAK,qBAAuB,KAAK,wBAA0B,EAG3D,KAAK,WAAa,GAClB,KAAK,iBAAmB,EAExB,QAAQ,IAAI,iDAAiD,KAAK,KAAK,EAAE,CAC7E,CAOA,KAAKC,EAAY,CACb,GAAI,KAAK,YAAa,MAAO,GAE7B,MAAMC,EAAa,KAAK,IAAID,EAAY,KAAK,UAAY,KAAK,MAAM,EACpE,YAAK,QAAUC,EAEf,QAAQ,IAAI,yBAAyBA,CAAU,aAAa,KAAK,MAAM,IAAI,KAAK,SAAS,EAAE,EACpFA,CACX,CAMA,SAASC,EAAY,CACjB,KAAK,OAASA,EACd,QAAQ,IAAI,qBAAqBA,CAAU,wBAAwB,KAAK,KAAK,EAAE,CACnF,CAMA,iBAAkB,CACd,MAAO,CACH,OAAQ,KAAK,OACb,UAAW,KAAK,UAChB,iBAAkB,KAAK,OAAS,KAAK,UACrC,MAAO,KAAK,MACZ,SAAU,KAAK,SACf,eAAgB,KAAK,eACrB,6BAA8B,KAAK,qBACnC,YAAa,KAAK,YAClB,WAAY,KAAK,UAC7B,CACI,CAKA,qBAAsB,CAClB,KAAK,OAAS,KAAK,UACnB,KAAK,MAAQ,KAAK,SAClB,KAAK,eAAiB,GACtB,KAAK,qBAAuB,EAC5B,KAAK,YAAc,GACnB,KAAK,WAAa,GAClB,KAAK,iBAAmB,EAExB,QAAQ,IAAI,8CAA8C,CAC9D,CASA,iBAAiBC,EAAQC,EAAQ9D,EAAQ,CAErCA,EAAS,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGA,CAAM,CAAC,EAGxC,MAAM+D,EAAOF,EAAO,QAAQ,IAAK,EAAE,EAC7BG,EAAOF,EAAO,QAAQ,IAAK,EAAE,EAE7BG,EAAK,SAASF,EAAK,OAAO,EAAG,CAAC,EAAG,EAAE,EACnCG,EAAK,SAASH,EAAK,OAAO,EAAG,CAAC,EAAG,EAAE,EACnCI,EAAK,SAASJ,EAAK,OAAO,EAAG,CAAC,EAAG,EAAE,EAEnCK,EAAK,SAASJ,EAAK,OAAO,EAAG,CAAC,EAAG,EAAE,EACnCK,EAAK,SAASL,EAAK,OAAO,EAAG,CAAC,EAAG,EAAE,EACnCM,EAAK,SAASN,EAAK,OAAO,EAAG,CAAC,EAAG,EAAE,EAGnC9D,EAAI,KAAK,MAAM+D,GAAMG,EAAKH,GAAMjE,CAAM,EACtC,EAAI,KAAK,MAAMkE,GAAMG,EAAKH,GAAMlE,CAAM,EACtCI,EAAI,KAAK,MAAM+D,GAAMG,EAAKH,GAAMnE,CAAM,EAGtCuE,EAASC,GAAM,CACjB,MAAMvE,EAAMuE,EAAE,SAAS,EAAE,EACzB,OAAOvE,EAAI,SAAW,EAAI,IAAMA,EAAMA,CAC1C,EAEA,MAAO,IAAIsE,EAAMrE,CAAC,CAAC,GAAGqE,EAAM,CAAC,CAAC,GAAGA,EAAMnE,CAAC,CAAC,EAC7C,CACJ,CC3qBO,MAAMqE,CAAW,CACpB,YAAYC,EAAUC,EAASC,EAAc,GAAI,CAC7C,KAAK,SAAWF,EAChB,KAAK,QAAUC,EACf,KAAK,KAAO,CAAA,EACZ,KAAK,OAAS,CAAA,EAGd,QAAShE,EAAI,EAAGA,EAAIiE,EAAajE,IAC7B,KAAK,KAAK,KAAK,KAAK,SAAQ,CAAE,CAEtC,CAGA,KAAM,CACF,IAAIkE,EACJ,OAAI,KAAK,KAAK,OAAS,EACnBA,EAAM,KAAK,KAAK,IAAG,EAEnBA,EAAM,KAAK,SAAQ,EAGvB,KAAK,OAAO,KAAKA,CAAG,EACbA,CACX,CAGA,QAAQA,EAAK,CACT,MAAMC,EAAQ,KAAK,OAAO,QAAQD,CAAG,EACjCC,IAAU,KACV,KAAK,OAAO,OAAOA,EAAO,CAAC,EAC3B,KAAK,QAAQD,CAAG,EAChB,KAAK,KAAK,KAAKA,CAAG,EAE1B,CAGA,YAAa,CACT,KAAO,KAAK,OAAO,OAAS,GAAG,CAC3B,MAAMA,EAAM,KAAK,OAAO,IAAG,EAC3B,KAAK,QAAQA,CAAG,EAChB,KAAK,KAAK,KAAKA,CAAG,CACtB,CACJ,CAGA,UAAW,CACP,MAAO,CACH,OAAQ,KAAK,KAAK,OAClB,OAAQ,KAAK,OAAO,OACpB,MAAO,KAAK,KAAK,OAAS,KAAK,OAAO,MAClD,CACI,CACJ,CCnDO,MAAME,CAAkB,CAC3B,aAAc,CACV,KAAK,QAAU,CAAA,EACf,KAAK,aAAe,CAAA,EACpB,KAAK,gBAAkB,CAAA,EACvB,KAAK,MAAQ,IAAI,GACrB,CAGA,IAAIC,EAAQ,CACR,KAAK,aAAa,KAAKA,CAAM,CACjC,CAGA,OAAOA,EAAQ,CACX,KAAK,gBAAgB,KAAKA,CAAM,CACpC,CAGA,WAAW1F,EAAMoF,EAAUC,EAASC,EAAc,GAAI,CAClD,KAAK,MAAM,IAAItF,EAAM,IAAImF,EAAWC,EAAUC,EAASC,CAAW,CAAC,CACvE,CAGA,YAAYtF,EAAM,CACd,MAAM2F,EAAO,KAAK,MAAM,IAAI3F,CAAI,EAChC,GAAI2F,EACA,OAAOA,EAAK,IAAG,EAEnB,MAAM,IAAI,MAAM,kBAAkB3F,CAAI,aAAa,CACvD,CAGA,aAAaA,EAAM0F,EAAQ,CACvB,MAAMC,EAAO,KAAK,MAAM,IAAI3F,CAAI,EAC5B2F,IACAA,EAAK,QAAQD,CAAM,EACnB,KAAK,OAAOA,CAAM,EAE1B,CAGA,OAAOtG,EAAW,CAEd,KAAK,iBAAgB,EACrB,KAAK,gBAAe,EAGpB,QAASiC,EAAI,KAAK,QAAQ,OAAS,EAAGA,GAAK,EAAGA,IAAK,CAC/C,MAAMqE,EAAS,KAAK,QAAQrE,CAAC,EAE7B,GAAIqE,EAAO,UAAW,CAClB,KAAK,gBAAgB,KAAKA,CAAM,EAChC,QACJ,CAEIA,EAAO,QACPA,EAAO,OAAOtG,CAAS,CAE/B,CACJ,CAGA,OAAON,EAAKO,EAAgB,EAAG,CAC3B,UAAWqG,KAAU,KAAK,QAClBA,EAAO,SAAW,CAACA,EAAO,WAC1BA,EAAO,OAAO5G,EAAKO,CAAa,CAG5C,CAGA,kBAAmB,CACX,KAAK,aAAa,OAAS,IAC3B,KAAK,QAAQ,KAAK,GAAG,KAAK,YAAY,EACtC,KAAK,aAAa,OAAS,EAEnC,CAGA,iBAAkB,CACd,GAAI,KAAK,gBAAgB,OAAS,EAAG,CACjC,UAAWuG,KAAkB,KAAK,gBAAiB,CAC/C,MAAMJ,EAAQ,KAAK,QAAQ,QAAQI,CAAc,EAC7CJ,IAAU,IACV,KAAK,QAAQ,OAAOA,EAAO,CAAC,CAEpC,CACA,KAAK,gBAAgB,OAAS,CAClC,CACJ,CAGA,UAAUjG,EAAK,CACX,OAAO,KAAK,QAAQ,OAAOgG,GAAOA,EAAI,OAAOhG,CAAG,CAAC,CACrD,CAGA,SAASsG,EAAI,CACT,OAAO,KAAK,QAAQ,KAAKN,GAAOA,EAAI,KAAOM,CAAE,CACjD,CAGA,WAAY,CACR,OAAO,KAAK,QAAQ,OAAON,GAAOA,EAAI,QAAU,CAACA,EAAI,SAAS,CAClE,CAGA,YAAa,CACT,OAAO,KAAK,QAAQ,OAAOA,GAAOA,EAAI,SAAW,CAACA,EAAI,SAAS,CACnE,CAGA,gBAAgBO,EAAWC,EAAWC,EAAU,CAC5C,MAAMC,EAAS,KAAK,UAAUH,CAAS,EACjCI,EAAS,KAAK,UAAUH,CAAS,EAEvC,UAAWI,KAAQF,EACf,GAAKE,EAAK,OAEV,UAAWC,KAAQF,EACVE,EAAK,QAEND,EAAK,aAAaC,CAAI,GACtBJ,EAASG,EAAMC,CAAI,CAInC,CAGA,yBAAyBN,EAAWC,EAAWC,EAAUK,EAAW,GAAI,CACpE,MAAMJ,EAAS,KAAK,UAAUH,CAAS,EACjCI,EAAS,KAAK,UAAUH,CAAS,EAGjCO,EAAc,IAAI,IAGxB,UAAWf,KAAOW,EAAQ,CACtB,GAAI,CAACX,EAAI,OAAQ,SAEjB,MAAMgB,EAAQ,KAAK,MAAMhB,EAAI,SAAS,EAAIc,CAAQ,EAC5CG,EAAQ,KAAK,MAAMjB,EAAI,SAAS,EAAIc,CAAQ,EAC5CpI,EAAM,GAAGsI,CAAK,IAAIC,CAAK,GAExBF,EAAY,IAAIrI,CAAG,GACpBqI,EAAY,IAAIrI,EAAK,EAAE,EAE3BqI,EAAY,IAAIrI,CAAG,EAAE,KAAKsH,CAAG,CACjC,CAGA,UAAWY,KAAQF,EAAQ,CACvB,GAAI,CAACE,EAAK,OAAQ,SAElB,MAAMI,EAAQ,KAAK,MAAMJ,EAAK,SAAS,EAAIE,CAAQ,EAC7CG,EAAQ,KAAK,MAAML,EAAK,SAAS,EAAIE,CAAQ,EAGnD,QAASI,EAAK,GAAIA,GAAM,EAAGA,IACvB,QAASC,EAAK,GAAIA,GAAM,EAAGA,IAAM,CAC7B,MAAMzI,EAAM,GAAGsI,EAAQE,CAAE,IAAID,EAAQE,CAAE,GACjCC,EAAgBL,EAAY,IAAIrI,CAAG,EAEzC,GAAI0I,EACA,UAAWP,KAAQO,EACXR,EAAK,aAAaC,CAAI,GACtBJ,EAASG,EAAMC,CAAI,CAInC,CAER,CACJ,CAGA,OAAQ,CACJ,KAAK,QAAQ,OAAS,EACtB,KAAK,aAAa,OAAS,EAC3B,KAAK,gBAAgB,OAAS,EAG9B,UAAWT,KAAQ,KAAK,MAAM,OAAM,EAChCA,EAAK,WAAU,CAEvB,CAGA,UAAW,CACP,MAAMiB,EAAY,CAAA,EAClB,SAAW,CAAC5G,EAAM2F,CAAI,IAAK,KAAK,MAAM,UAClCiB,EAAU5G,CAAI,EAAI2F,EAAK,SAAQ,EAGnC,MAAO,CACH,aAAc,KAAK,QAAQ,OAC3B,cAAe,KAAK,UAAS,EAAG,OAChC,eAAgB,KAAK,WAAU,EAAG,OAClC,iBAAkB,KAAK,aAAa,OACpC,gBAAiB,KAAK,gBAAgB,OACtC,MAAOiB,CACnB,CACI,CACJ,CChNO,MAAMC,EAAc,CAEvB,aAAc,IACd,cAAe,IAQf,kBAAmB,GACnB,eAAgB,CACZ,cAAe,IACf,WAAY,IACZ,YAAa,EACrB,CAgBA,EAEaC,EAAoB,CAC7B,MAAO,QACP,WAAY,aACZ,SAAU,WACV,QAAS,UACT,OAAQ,SACR,OAAQ,SACR,IAAK,KACT,EAEaC,EAAc,CACvB,MAAO,QACP,KAAM,OACN,IAAK,MACL,MAAO,QACP,QAAS,UACT,OAAQ,QACZ,EC/CO,MAAMC,CAAa,CACtB,YAAYhG,EAAoB,KAAM,CAClC,KAAK,kBAAoBA,EAGzB,KAAK,aAAe,EACpB,KAAK,gBAAkB,GACvB,KAAK,eAAiB,EACtB,KAAK,oBAAsB,EAG3B,KAAK,YAAc,KACnB,KAAK,UAAY,GAGjB,KAAK,aAAe,EACpB,KAAK,WAAa,EAClB,KAAK,gBAAkB,EACvB,KAAK,qBAAuB,EAC5B,KAAK,WAAa,EAGlB,KAAK,gBAAkB,EACvB,KAAK,kBAAoB,GACzB,KAAK,WAAa,EAClB,KAAK,cAAgB,EAGrB,KAAK,wBAA0B,EAC/B,KAAK,eAAiB,EACtB,KAAK,cAAgB,EAGrB,KAAK,qBAAuB,KAC5B,KAAK,wBAA0B,KAC/B,KAAK,sBAAwB,KAE7B,QAAQ,IAAI,0BAA0B,CAC1C,CAOA,WAAWiG,EAAc,KAAM,CAC3B,OAAIA,IAAgB,OAChB,KAAK,aAAeA,GAIxB,KAAK,YAAc,KAAK,oBAAoB,KAAK,YAAY,EAG7D,KAAK,gBAAkB,GACvB,KAAK,eAAiB,YAAY,IAAG,EACrC,KAAK,oBAAsB,EAC3B,KAAK,WAAa,EAClB,KAAK,qBAAuB,EAC5B,KAAK,gBAAkB,KAAK,aAC5B,KAAK,kBAAoB,GACzB,KAAK,WAAa,EAClB,KAAK,cAAgB,EAGrB,KAAK,wBAA0B,KAAK,YAAY,aAChD,KAAK,cAAgB,KAAK,YAAY,WACtC,KAAK,eAAiB,EAEtB,QAAQ,IAAI,kBAAkB,KAAK,YAAY,IAAK,KAAK,WAAW,EAGhE,KAAK,sBACL,KAAK,qBAAqB,KAAK,aAAc,KAAK,WAAW,EAG1D,KAAK,WAChB,CAOA,oBAAoBA,EAAa,CAE7B,MAAMC,EAAuB,KAAK,IAAI,EAAK,GAAOD,EAAc,GAAK,EAAG,EAGlEE,EAAiB,EACjBC,EAAqB,KAAK,OAAOH,EAAc,GAAK,CAAC,EACrDI,EAAe,KAAK,OAAOF,EAAiBC,GAAsBF,CAAoB,EAGtFI,EAAgB,EAChBC,EAAe,KAAK,OAAON,EAAc,GAAK,CAAC,EAC/CO,EAAa,KAAK,IAAI,EAAGF,EAAgBC,CAAY,EAGrDE,EAAc,KAAK,uBAAuBR,CAAW,EAGrDS,EAAUT,EAAc,KAAO,EAG/BU,EAAsBV,EAAc,IAAM,EAG1CW,EAAgB,IAChBC,EAAsB,KAAK,IAAI,GAAIZ,EAAc,CAAC,EAClDa,EAAYF,EAAgBC,EAI5BE,EAAc,KAAK,MADD,IACyBb,EAAuBD,CAAW,EAEnF,MAAO,CACH,YAAaA,EACb,WAAY,KAAK,IAAI,GAAI,KAAK,MAAMA,EAAc,CAAC,EAAI,CAAC,EACxD,aAAcI,EACd,WAAYG,EACZ,YAAaC,EACb,QAASC,EACT,oBAAqBC,EACrB,UAAWG,EACX,YAAaC,EACb,iBAAkB,KAAK,yBAAyBd,CAAW,EAC3D,qBAAsBC,EAGtB,kBAAmB,KAAK,0BAA0BD,EAAaQ,CAAW,EAG1E,WAAY,KAAK,wBAAwBR,CAAW,EAGpD,gBAAiB,KAAK,sBAAsBA,EAAaQ,CAAW,EACpE,cAAe,KAAK,oBAAoBR,EAAaQ,CAAW,CAC5E,CACI,CAOA,uBAAuBR,EAAa,CAEhC,GAAIA,GAAe,EACf,OAAOH,EAAkB,MAI7B,MAAMkB,EAAe,CACjBlB,EAAkB,MAClBA,EAAkB,WAClBA,EAAkB,SAClBA,EAAkB,QAClBA,EAAkB,OAClBA,EAAkB,OAClBA,EAAkB,GAC9B,EAGQ,GAAIG,EAAc,KAAO,EAAG,CACxB,MAAMgB,EAAmB,CACrBnB,EAAkB,SAClBA,EAAkB,QAClBA,EAAkB,GAClC,EACY,OAAOmB,EAAiB,KAAK,OAAOhB,EAAc,GAAK,GAAKgB,EAAiB,MAAM,CAAC,CACxF,CAGA,MAAMC,EAAY,KAAK,OAAOjB,EAAc,GAAK,CAAC,EAAIe,EAAa,OAC7DG,GAAalB,EAAc,GAAK,EAChCmB,GAAcF,EAAYC,GAAaH,EAAa,OAE1D,OAAOA,EAAaI,CAAU,CAClC,CAQA,0BAA0BnB,EAAaQ,EAAa,CAChD,MAAMY,EAAe,CAAA,EAGrB,OAAIpB,GAAe,GAEfoB,EAAa,MAAQ,GACrBA,EAAa,SAAW,GACxBA,EAAa,MAAQ,GACdpB,GAAe,IAEtBoB,EAAa,MAAQ,GACrBA,EAAa,SAAW,GACxBA,EAAa,MAAQ,KAGrBA,EAAa,MAAQ,GACrBA,EAAa,SAAW,GACxBA,EAAa,MAAQ,IAIzBA,EAAa,mBAAqB,KAAK,2BAA2BZ,CAAW,EAEtEY,CACX,CAOA,2BAA2BZ,EAAa,CAWpC,MAVgB,CACZ,CAACX,EAAkB,KAAK,EAAG,CAAE,IAAK,IAAK,QAAS,GAAG,EACnD,CAACA,EAAkB,UAAU,EAAG,CAAE,MAAO,IAAK,MAAO,EAAG,EACxD,CAACA,EAAkB,QAAQ,EAAG,CAAE,KAAM,IAAK,MAAO,GAAG,EACrD,CAACA,EAAkB,OAAO,EAAG,CAAE,QAAS,IAAK,OAAQ,GAAG,EACxD,CAACA,EAAkB,MAAM,EAAG,CAAE,MAAO,IAAK,OAAQ,GAAG,EACrD,CAACA,EAAkB,MAAM,EAAG,CAAE,KAAM,IAAK,MAAO,GAAG,EACnD,CAACA,EAAkB,GAAG,EAAG,CAAE,MAAO,IAAK,IAAK,EAAG,CAC3D,EAEuBW,CAAW,GAAK,CAAA,CACnC,CAOA,wBAAwBR,EAAa,CACjC,MAAMqB,EAAa,CACf,QAAS,qBACT,UAAW,CAAA,EACX,MAAO,CAAA,CACnB,EAGQ,OAAIrB,GAAe,GACfqB,EAAW,UAAU,KAAK,2BAA2B,EAGrDrB,GAAe,GACfqB,EAAW,UAAU,KAAK,4BAA4B,EAGtDrB,GAAe,GACfqB,EAAW,UAAU,KAAK,qBAAqB,EAInDA,EAAW,MAAM,KAAK,kBAAkB,EACxCA,EAAW,MAAM,KAAK,kBAAkB,EACxCA,EAAW,MAAM,KAAK,iBAAiB,EAEhCA,CACX,CAQA,sBAAsBrB,EAAaQ,EAAa,CAE5C,OAAIR,EAAc,KAAO,EACd,aAIM,CACb,CAACH,EAAkB,KAAK,EAAG,gBAC3B,CAACA,EAAkB,UAAU,EAAG,mBAChC,CAACA,EAAkB,QAAQ,EAAG,qBAC9B,CAACA,EAAkB,OAAO,EAAG,kBAC7B,CAACA,EAAkB,MAAM,EAAG,iBAC5B,CAACA,EAAkB,MAAM,EAAG,eAC5B,CAACA,EAAkB,GAAG,EAAG,aACrC,EAEwBW,CAAW,GAAK,eACpC,CAQA,oBAAoBR,EAAaQ,EAAa,CAC1C,MAAMc,EAAU,CAAA,EAGhB,OAAQd,EAAW,CACf,KAAKX,EAAkB,WACnByB,EAAQ,KAAK,gBAAiB,YAAY,EAC1C,MACJ,KAAKzB,EAAkB,SACnByB,EAAQ,KAAK,iBAAkB,iBAAiB,EAChD,MACJ,KAAKzB,EAAkB,QACnByB,EAAQ,KAAK,sBAAuB,eAAe,EACnD,MACJ,KAAKzB,EAAkB,OACnByB,EAAQ,KAAK,kBAAmB,eAAe,EAC/C,MACJ,KAAKzB,EAAkB,OACnByB,EAAQ,KAAK,iBAAkB,cAAc,EAC7C,MACJ,KAAKzB,EAAkB,IACnByB,EAAQ,KAAK,iBAAkB,cAAc,EAC7C,MACJ,QACIA,EAAQ,KAAK,aAAc,eAAe,CAC1D,CAGQ,OAAItB,GAAe,IACfsB,EAAQ,KAAK,mBAAmB,EAG7BA,CACX,CAOA,OAAOnJ,EAAWoJ,EAAY,GAAI,CACzB,KAAK,kBAGV,KAAK,oBAAsB,YAAY,IAAG,EAAK,KAAK,eAGpD,KAAK,qBAAqBA,CAAS,EAGnC,KAAK,yBAAyBA,CAAS,EAC3C,CAMA,qBAAqBA,EAAW,CAC5B,GAAI,CAAC,KAAK,iBAAmB,CAAC,KAAK,YAAa,OAGhD,MAAMC,EAAmB,KAAK,sBAAwB,KAAK,wBACrDC,EAAiB,KAAK,gBAAkB,KAAK,cAG/CD,GAAoBC,GACpB,KAAK,cAAa,EAItB,MAAMC,EAAc,KAAK,oBAAuB,KAAK,YAAY,UAAY,IACvEC,EAAkBJ,EAAU,iBAAmB,IAEjDG,GAAeC,IACf,KAAK,UAAUD,EAAc,eAAiB,kBAAkB,CAExE,CAKA,eAAgB,CACZ,GAAI,CAAC,KAAK,gBAAiB,OAE3B,KAAK,gBAAkB,GACvB,MAAME,EAAwB,KAAK,oBAAsB,IAGnDC,EAAY,KAAK,oBAAoBD,CAAqB,EAEhE,QAAQ,IAAI,SAAS,KAAK,YAAY,cAAe,CACjD,KAAMA,EAAsB,QAAQ,CAAC,EAAI,IACzC,MAAOC,EAAU,WACjB,QAAS,KAAK,oBAC1B,CAAS,EAGD,KAAK,cAAgBA,EAAU,WAC/B,KAAK,WAAa,KAAK,aAGvB,MAAMC,EAAiB,CACnB,YAAa,KAAK,aAClB,UAAW,GACX,eAAgBF,EAChB,MAAOC,EACP,gBAAiB,KAAK,qBACtB,kBAAmB,KAAK,kBACxB,QAASA,EAAU,QACnB,UAAW,KAAK,aAAe,CAC3C,EAGQ,OAAI,KAAK,yBACL,KAAK,wBAAwBC,CAAc,EAGxCA,CACX,CAMA,UAAUC,EAAQ,CACd,GAAI,CAAC,KAAK,gBAAiB,OAE3B,KAAK,gBAAkB,GACvB,MAAMH,EAAwB,KAAK,oBAAsB,IAEzD,QAAQ,IAAI,SAAS,KAAK,YAAY,YAAYG,CAAM,EAAE,EAG1D,MAAMC,EAAc,CAChB,YAAa,KAAK,aAClB,UAAW,GACX,OAAQD,EACR,eAAgBH,EAChB,MAAO,KAAK,WACZ,gBAAiB,KAAK,qBACtB,SAAU,EACtB,EAGQ,OAAI,KAAK,yBACL,KAAK,wBAAwBI,CAAW,EAGrCA,CACX,CAOA,oBAAoBC,EAAgB,CAChC,MAAMC,EAAS,KAAK,YAGdC,EAAa,KAAK,WAGlBC,EAAY,KAAK,mBAAmBH,EAAgBC,EAAO,SAAS,EAGpEG,EAAgB,KAAK,uBAAsB,EAG3CC,EAAe,KAAK,kBAAoB,KAAK,MAAMH,EAAa,EAAG,EAAI,EAGvEI,EAAkBL,EAAO,iBAGzBjC,EAAuBiC,EAAO,qBAG9BM,EAAYL,EAAaC,EAAYC,EAAgBC,EAAeC,EAGpEE,EAAa,KAAK,MAAMD,EAAYvC,CAAoB,EAE9D,MAAO,CACH,WAAYkC,EACZ,UAAWC,EACX,cAAeC,EACf,aAAcC,EACd,gBAAiBC,EACjB,qBAAsBtC,EACtB,WAAYwC,EACZ,QAAS,CACL,MAAOL,EAAY,EACnB,SAAUC,EAAgB,EAC1B,QAASC,EAAe,CACxC,CACA,CACI,CAQA,mBAAmBL,EAAgBpB,EAAW,CAC1C,MAAM6B,EAAa7B,EAAY,GAE/B,GAAIoB,GAAkBS,EAAY,CAG9B,MAAMC,EAAYV,EAAiBS,EACnC,OAAO,KAAK,MAAM,KAAY,EAAIC,EAAU,CAChD,KAAO,IAAIV,GAAkBpB,EAAY,GAErC,MAAO,KACJ,GAAIoB,GAAkBpB,EAEzB,MAAO,IAGX,MAAO,EACX,CAMA,wBAAyB,CAGrB,OAAO,KAAK,aAChB,CAMA,yBAAyBU,EAAW,CAOhC,GALIA,EAAU,oBACV,KAAK,kBAAoB,IAIzBA,EAAU,YAAcA,EAAU,SAAU,CAC5C,MAAMqB,EAAWrB,EAAU,SAAWA,EAAU,WAC5CqB,GAAY,GACZ,KAAK,cAAgB,IACdA,GAAY,GACnB,KAAK,cAAgB,IACdA,GAAY,KACnB,KAAK,cAAgB,GAE7B,CACJ,CAOA,kBAAkBC,EAAOC,EAAY,CACjC,KAAK,kBACL,KAAK,uBACL,KAAK,YAAcA,EACnB,KAAK,cAAgBA,EAGjB,KAAK,uBACL,KAAK,sBAAsB,CACvB,gBAAiB,KAAK,gBACtB,qBAAsB,KAAK,qBAC3B,aAAc,KAAK,aACnB,WAAY,KAAK,WACjB,YAAaA,CAC7B,CAAa,CAET,CAOA,qBAAqBC,EAAYC,EAAY,EAAG,CAC5C,KAAK,iBAEDA,EAAY,IACZ,KAAK,YAAcA,EACnB,KAAK,cAAgBA,GAGzB,QAAQ,IAAI,QAAQD,CAAU,sBAAsB,KAAK,cAAc,IAAI,KAAK,aAAa,EAAE,EAG3F,KAAK,uBACL,KAAK,sBAAsB,CACvB,eAAgB,KAAK,eACrB,aAAc,KAAK,aACnB,WAAY,KAAK,WACjB,YAAaC,CAC7B,CAAa,CAET,CAOA,yBAAyBhD,EAAa,CAClC,MAAMiD,EAAarD,EAAY,kBACzBsD,EAAkB,KAAK,MAAMlD,EAAc,CAAC,EAAI,EACtD,OAAOiD,EAAaC,CACxB,CAMA,gBAAiB,CACb,MAAO,CACH,aAAc,KAAK,aACnB,gBAAiB,KAAK,gBACtB,YAAa,KAAK,YAClB,eAAgB,KAAK,oBACrB,MAAO,CACH,QAAS,KAAK,aACd,MAAO,KAAK,WACZ,MAAO,KAAK,UAC5B,EACY,SAAU,CACN,gBAAiB,KAAK,qBACtB,gBAAiB,KAAK,wBACtB,eAAgB,KAAK,eACrB,cAAe,KAAK,aACpC,EACY,YAAa,CACT,kBAAmB,KAAK,kBACxB,WAAY,KAAK,WACjB,cAAe,KAAK,aACpC,CACA,CACI,CAKA,OAAQ,CACJ,KAAK,aAAe,EACpB,KAAK,gBAAkB,GACvB,KAAK,eAAiB,EACtB,KAAK,oBAAsB,EAC3B,KAAK,aAAe,EACpB,KAAK,WAAa,EAClB,KAAK,gBAAkB,EACvB,KAAK,qBAAuB,EAC5B,KAAK,WAAa,EAClB,KAAK,kBAAoB,GACzB,KAAK,WAAa,EAClB,KAAK,cAAgB,EACrB,KAAK,eAAiB,EAEtB,QAAQ,IAAI,oBAAoB,CACpC,CAMA,gBAAgBnE,EAAU,CACtB,KAAK,qBAAuBA,CAChC,CAMA,mBAAmBA,EAAU,CACzB,KAAK,wBAA0BA,CACnC,CAMA,iBAAiBA,EAAU,CACvB,KAAK,sBAAwBA,CACjC,CACJ,CCprBO,MAAMoE,CAAS,CAElB,OAAO,MAAMC,EAAOC,EAAKC,EAAK,CAC1B,OAAO,KAAK,IAAI,KAAK,IAAIF,EAAOC,CAAG,EAAGC,CAAG,CAC7C,CAGA,OAAO,KAAKC,EAAOC,EAAKC,EAAG,CACvB,OAAOF,GAASC,EAAMD,GAASE,CACnC,CAGA,OAAO,YAAYC,EAAOxM,EAAM,CAC5B,OAAOwM,EAAM,GAAKxM,EAAK,GAChBwM,EAAM,GAAKxM,EAAK,EAAIA,EAAK,OACzBwM,EAAM,GAAKxM,EAAK,GAChBwM,EAAM,GAAKxM,EAAK,EAAIA,EAAK,MACpC,CAGA,OAAO,cAAcyM,EAAOC,EAAO,CAC/B,OAAOD,EAAM,EAAIC,EAAM,EAAIA,EAAM,OAC1BD,EAAM,EAAIA,EAAM,MAAQC,EAAM,GAC9BD,EAAM,EAAIC,EAAM,EAAIA,EAAM,QAC1BD,EAAM,EAAIA,EAAM,OAASC,EAAM,CAC1C,CAGA,OAAO,gBAAgBC,EAASC,EAAS,CACrC,MAAMtE,EAAKqE,EAAQ,EAAIC,EAAQ,EACzBrE,EAAKoE,EAAQ,EAAIC,EAAQ,EAE/B,OADiB,KAAK,KAAKtE,EAAKA,EAAKC,EAAKA,CAAE,EAC1BoE,EAAQ,OAASC,EAAQ,MAC/C,CAGA,OAAO,OAAOT,EAAKC,EAAK,CACpB,OAAO,KAAK,OAAM,GAAMA,EAAMD,GAAOA,CACzC,CAGA,OAAO,UAAUA,EAAKC,EAAK,CACvB,OAAO,KAAK,MAAM,KAAK,OAAM,GAAMA,EAAMD,EAAM,EAAE,EAAIA,CACzD,CAGA,OAAO,SAASU,EAAS,CACrB,OAAOA,GAAW,KAAK,GAAK,IAChC,CAGA,OAAO,SAASC,EAAS,CACrB,OAAOA,GAAW,IAAM,KAAK,GACjC,CAGA,OAAO,oBAAoBC,EAAQ/M,EAAM,CAErC,MAAMgN,EAAWf,EAAS,MAAMc,EAAO,EAAG/M,EAAK,EAAGA,EAAK,EAAIA,EAAK,KAAK,EAC/DiN,EAAWhB,EAAS,MAAMc,EAAO,EAAG/M,EAAK,EAAGA,EAAK,EAAIA,EAAK,MAAM,EAGhEsI,EAAKyE,EAAO,EAAIC,EAChBzE,EAAKwE,EAAO,EAAIE,EAGtB,OAFiB,KAAK,KAAK3E,EAAKA,EAAKC,EAAKA,CAAE,EAE1BwE,EAAO,MAC7B,CAGA,OAAO,oBAAoBG,EAAWC,EAASJ,EAAQ,CACnD,MAAMzE,EAAK6E,EAAQ,EAAID,EAAU,EAC3B3E,EAAK4E,EAAQ,EAAID,EAAU,EAC3BE,EAAKF,EAAU,EAAIH,EAAO,EAC1BM,EAAKH,EAAU,EAAIH,EAAO,EAE1BO,EAAIhF,EAAKA,EAAKC,EAAKA,EACnB5F,EAAI,GAAKyK,EAAK9E,EAAK+E,EAAK9E,GACxBgF,EAAKH,EAAKA,EAAKC,EAAKA,EAAMN,EAAO,OAASA,EAAO,OAEjDS,EAAe7K,EAAIA,EAAI,EAAI2K,EAAIC,EAErC,GAAIC,EAAe,EAAG,MAAO,GAE7B,MAAMC,EAAmB,KAAK,KAAKD,CAAY,EACzCE,GAAM,CAAC/K,EAAI8K,IAAqB,EAAIH,GACpCK,GAAM,CAAChL,EAAI8K,IAAqB,EAAIH,GAE1C,OAAQI,GAAM,GAAKA,GAAM,GAAOC,GAAM,GAAKA,GAAM,CACrD,CAGA,OAAO,WAAWC,EAAOC,EAAO9O,EAAG,CAC/B,MAAMwN,EAAIN,EAAS,OAAOlN,EAAI6O,IAAUC,EAAQD,GAAQ,EAAG,CAAC,EAC5D,OAAOrB,EAAIA,GAAK,EAAI,EAAIA,EAC5B,CAGA,OAAO,WAAWA,EAAG,CACjB,OAAOA,EAAIA,CACf,CAEA,OAAO,YAAYA,EAAG,CAClB,OAAOA,GAAK,EAAIA,EACpB,CAEA,OAAO,cAAcA,EAAG,CACpB,OAAOA,EAAI,GAAM,EAAIA,EAAIA,EAAI,IAAM,EAAI,EAAIA,GAAKA,CACpD,CAGA,OAAO,UAAUnN,EAAO,CACpB,KAAOA,EAAQ,KAAK,IAAIA,GAAS,EAAI,KAAK,GAC1C,KAAOA,EAAQ,CAAC,KAAK,IAAIA,GAAS,EAAI,KAAK,GAC3C,OAAOA,CACX,CAGA,OAAO,gBAAgB0O,EAAQC,EAAQ,CACnC,IAAIC,EAAOD,EAASD,EACpB,OAAO7B,EAAS,UAAU+B,CAAI,CAClC,CAGA,OAAO,YAAYC,EAAS5M,EAAQ6M,EAAS,CACzC,MAAMF,EAAO3M,EAAS4M,EACtB,OAAI,KAAK,IAAID,CAAI,GAAKE,EACX7M,EAEJ4M,EAAU,KAAK,KAAKD,CAAI,EAAIE,CACvC,CAGA,OAAO,cAAcZ,EAAG3K,EAAGjD,EAAY,KAAQ,CAC3C,OAAO,KAAK,IAAI4N,EAAI3K,CAAC,EAAIjD,CAC7B,CAGA,OAAO,IAAIwM,EAAOiC,EAASC,EAASC,EAAOC,EAAO,CAC9C,OAAQpC,EAAQiC,IAAYG,EAAQD,IAAUD,EAAUD,GAAWE,CACvE,CACJ,CCvIO,MAAME,UAAc3M,CAAW,CAClC,YAAY7C,EAAGC,EAAG6C,EAAO+G,EAAY,IAAKzE,EAAc,IAAKC,EAAe,IAAK,CAC7E,MAAMrF,EAAGC,CAAC,EAGV,KAAK,YAAcmF,EACnB,KAAK,aAAeC,EAGpB,KAAK,KAAOvC,EACZ,KAAK,UAAY,KAAK,iBAAiBA,CAAI,EAC3C,KAAK,OAAS,KAAK,UACnB,KAAK,UAAY,KAAK,iBAAiBA,CAAI,EAC3C,KAAK,aAAe,KAAK,UAGzB,KAAK,MAAQ,GACb,KAAK,OAAS,GACd,KAAK,gBAAkB,GAGvB,KAAK,gBAAkB,SACvB,KAAK,cAAgB,EACrB,KAAK,cAAgB,KAAK,OAAM,EAAK,KAAK,GAAK,EAC/C,KAAK,UAAY,GACjB,KAAK,UAAY,EAGjB,KAAK,kBAAoB,KACzB,KAAK,gBAAkB,EACvB,KAAK,gBAAkB,EACvB,KAAK,gBAAkB,IAAI/C,EAAQC,EAAGC,CAAC,EAGvC,KAAK,gBAAkB,IAAIF,EAAQ,EAAG,CAAC,EACvC,KAAK,gBAAkB,IAAIA,EAAQC,EAAGC,CAAC,EACvC,KAAK,cAAgB,GAGrB,KAAK,eAAiB,EACtB,KAAK,eAAiB,KAAK,kBAAkB6C,CAAI,EACjD,KAAK,kBAAoB,KAAK,eAC9B,KAAK,UAAY,GAGjB,KAAK,2BAA6B,EAClC,KAAK,mBAAqB,QAG1B,KAAK,cAAgB,EACrB,KAAK,eAAiB,EACtB,KAAK,YAAc,EACnB,KAAK,YAAc,EAGnB,KAAK,YAAc,GACnB,KAAK,mBAAqB,EAC1B,KAAK,uBAAyB,IAG9B,KAAK,WAAa,KAAK,kBAAkBA,CAAI,EAG7C,KAAK,OAAO,OAAO,EACnB,KAAK,OAAOA,CAAI,EAGhB,KAAK,mBAAmB,QAAQ,EAEhC,QAAQ,IAAI,uBAAuBA,CAAI,YAAY,KAAK,MAAM,cAAc,KAAK,SAAS,SAAQ,CAAE,EAAE,CAC1G,CAOA,iBAAiBA,EAAM,CASnB,MARkB,CACd,CAAC+G,EAAY,GAAG,EAAG,GACnB,CAACA,EAAY,KAAK,EAAG,GACrB,CAACA,EAAY,IAAI,EAAG,GACpB,CAACA,EAAY,KAAK,EAAG,GACrB,CAACA,EAAY,OAAO,EAAG,GACvB,CAACA,EAAY,MAAM,EAAG,EAClC,EACyB/G,CAAI,GAAK,EAC9B,CAOA,iBAAiBA,EAAM,CASnB,MARiB,CACb,CAAC+G,EAAY,GAAG,EAAG,IACnB,CAACA,EAAY,KAAK,EAAG,GACrB,CAACA,EAAY,IAAI,EAAG,IACpB,CAACA,EAAY,KAAK,EAAG,GACrB,CAACA,EAAY,OAAO,EAAG,GACvB,CAACA,EAAY,MAAM,EAAG,GAClC,EACwB/G,CAAI,GAAK,GAC7B,CAOA,kBAAkBA,EAAM,CASpB,MARsB,CAClB,CAAC+G,EAAY,GAAG,EAAG,IACnB,CAACA,EAAY,KAAK,EAAG,EACrB,CAACA,EAAY,IAAI,EAAG,EACpB,CAACA,EAAY,KAAK,EAAG,GACrB,CAACA,EAAY,OAAO,EAAG,IACvB,CAACA,EAAY,MAAM,EAAG,GAClC,EAC6B/G,CAAI,GAAK,CAClC,CAOA,kBAAkBA,EAAM,CASpB,MARiB,CACb,CAAC+G,EAAY,GAAG,EAAG,IACnB,CAACA,EAAY,KAAK,EAAG,IACrB,CAACA,EAAY,IAAI,EAAG,IACpB,CAACA,EAAY,KAAK,EAAG,IACrB,CAACA,EAAY,OAAO,EAAG,IACvB,CAACA,EAAY,MAAM,EAAG,GAClC,EACwB/G,CAAI,GAAK,GAC7B,CAOA,OAAOZ,EAAWuN,EAAiB,KAAM,CACrC,GAAK,KAAK,OAOV,IAJA,KAAK,eAAiBvN,EAAY,IAClC,KAAK,eAAiBA,EAAY,IAG9B,KAAK,YAAa,CAClB,KAAK,qBAAqBA,CAAS,EACnC,MACJ,CAGA,KAAK,eAAeA,EAAWuN,CAAc,EAG7C,KAAK,qBAAqBvN,CAAS,EAGnC,KAAK,gBAAgBA,CAAS,EAG9B,KAAK,eAAc,EAGnB,KAAK,sBAAqB,EAC9B,CAOA,eAAeA,EAAWuN,EAAgB,CACtC,MAAMC,EAAKxN,EAAY,IAEvB,OAAQ,KAAK,gBAAe,CACxB,IAAK,SACD,KAAK,qBAAqBwN,CAAE,EAC5B,MACJ,IAAK,OACD,KAAK,mBAAmBA,CAAE,EAC1B,MACJ,IAAK,SACD,KAAK,qBAAqBA,CAAE,EAC5B,MACJ,IAAK,OACD,KAAK,mBAAmBA,EAAID,CAAc,EAC1C,MACJ,IAAK,YACD,KAAK,wBAAwBC,CAAE,EAC/B,MACJ,IAAK,SACD,KAAK,qBAAqBA,CAAE,EAC5B,MACJ,IAAK,SACD,KAAK,qBAAqBA,CAAE,EAC5B,MACJ,IAAK,QACD,KAAK,oBAAoBA,EAAID,CAAc,EAC3C,MACJ,IAAK,aACD,KAAK,yBAAyBC,EAAID,CAAc,EAChD,KAChB,CAGQ,MAAME,EAAiB,KAAK,aAAe,KAAK,2BAChD,KAAK,SAAS,gBAAgBA,EAAiB,KAAK,SAAS,WAAW,EAGxE,KAAK,SAAS,WAAW,KAAK,SAAS,SAASD,CAAE,CAAC,CACvD,CAMA,qBAAqBA,EAAI,CACrB,KAAK,SAAS,IAAI,EAAG,KAAK,YAAY,CAC1C,CAMA,mBAAmBA,EAAI,CACnB,MAAME,EAAa,KAAK,IAAI,KAAK,cAAgB,KAAK,UAAY,KAAK,aAAa,EAAI,KAAK,UACvFC,EAAU,KAAK,gBAAgB,EAAID,EAGnCE,EAAa,KAAK,KAAKD,EAAU,KAAK,SAAS,CAAC,EACtD,KAAK,SAAS,IAAIC,EAAa,KAAK,aAAe,GAAK,KAAK,aAAe,EAAG,CACnF,CAMA,qBAAqBJ,EAAI,CACrB,MAAMK,EAAS,KAAK,WAAa,EAAI,KAAK,cAAgB,IACpD1P,EAAQ,KAAK,cAAgB,KAAK,UAAY,KAAK,cAEnDwP,EAAU,KAAK,gBAAgB,EAAI,KAAK,IAAIxP,CAAK,EAAI0P,EACrDC,EAAU,KAAK,gBAAgB,EAAI,KAAK,cAAgB,KAAK,aAAe,GAE5EzN,EAAY,IAAIxC,EAAQ8P,EAAU,KAAK,SAAS,EAAGG,EAAU,KAAK,SAAS,CAAC,EAAE,UAAS,EAC7F,KAAK,SAAWzN,EAAU,SAAS,KAAK,YAAY,CACxD,CAOA,mBAAmBmN,EAAID,EAAgB,CACnC,GAAI,CAACA,EAAgB,CACjB,KAAK,qBAAqBC,CAAE,EAC5B,MACJ,CAEA,GAAI,KAAK,cAAgB,EAAK,CAE1B,MAAMO,EAAiB,KAAK,cAAgB,KAAK,GAAK,GAAK,EAC3D,KAAK,SAAS,IAAIA,EAAiB,KAAK,aAAc,KAAK,aAAe,EAAG,CACjF,SAAW,KAAK,cAAgB,EAAK,CAEjC,MAAM1N,EAAYkN,EAAe,SAAS,KAAK,QAAQ,EAAE,UAAS,EAClE,KAAK,SAAWlN,EAAU,SAAS,KAAK,aAAe,GAAG,CAC9D,MAEI,KAAK,SAAS,IAAI,EAAG,KAAK,YAAY,CAE9C,CAMA,wBAAwBmN,EAAI,CAExB,MAAMnN,EADY,KAAK,gBAAgB,IAAI,KAAK,eAAe,EACnC,SAAS,KAAK,QAAQ,EAE9CA,EAAU,UAAS,EAAK,EACxB,KAAK,SAAWA,EAAU,UAAS,EAAG,SAAS,KAAK,YAAY,EAEhE,KAAK,SAAS,IAAI,EAAG,KAAK,aAAe,EAAG,CAEpD,CAMA,qBAAqBmN,EAAI,CAErB,MAAMnN,EADc,KAAK,MAAM,KAAK,cAAgB,KAAK,SAAS,EAAI,IACpC,EAAI,EAAI,GAE1C,KAAK,SAAS,IAAIA,EAAY,KAAK,aAAe,GAAK,KAAK,aAAe,EAAG,CAClF,CAMA,qBAAqBmN,EAAI,CACrB,MAAMrP,EAAQ,KAAK,cAAgB,KAAK,UAAY,KAAK,cACnD6P,EAAU,KAAK,gBAAgB,EAC/BC,EAAU,KAAK,gBAAgB,EAAI,KAAK,cAAgB,KAAK,aAAe,GAE5EN,EAAUK,EAAU,KAAK,IAAI7P,CAAK,EAAI,KAAK,UAC3C2P,EAAUG,EAAU,KAAK,IAAI9P,CAAK,EAAI,KAAK,UAAY,GAEvDkC,EAAY,IAAIxC,EAAQ8P,EAAU,KAAK,SAAS,EAAGG,EAAU,KAAK,SAAS,CAAC,EAAE,UAAS,EAC7F,KAAK,SAAWzN,EAAU,SAAS,KAAK,YAAY,CACxD,CAOA,oBAAoBmN,EAAID,EAAgB,CACpC,GAAI,CAACA,EAAgB,CACjB,KAAK,qBAAqBC,CAAE,EAC5B,MACJ,CAGA,MAAMU,EAAgB,IAChBC,EAAmB,KAAK,SAAS,SAASZ,CAAc,EAE9D,GAAIY,EAAmBD,EAAgB,GAAI,CAEvC,MAAM7N,EAAYkN,EAAe,SAAS,KAAK,QAAQ,EAAE,UAAS,EAClE,KAAK,SAAWlN,EAAU,SAAS,KAAK,aAAe,EAAG,CAC9D,SAAW8N,EAAmBD,EAAgB,GAAI,CAE9C,MAAM7N,EAAY,KAAK,SAAS,SAASkN,CAAc,EAAE,UAAS,EAClE,KAAK,SAAWlN,EAAU,SAAS,KAAK,aAAe,EAAG,CAC9D,KAAO,CAEH,MAAM+N,EAAgBb,EAAe,SAAS,KAAK,QAAQ,EAAE,cAAa,EAAG,UAAS,EACtF,KAAK,SAAWa,EAAc,SAAS,KAAK,aAAe,EAAG,CAClE,CACJ,CAOA,yBAAyBZ,EAAID,EAAgB,CAGzC,GAFA,KAAK,iBAAmBC,EAEpB,CAAC,KAAK,kBAAmB,CACzB,KAAK,qBAAqBA,CAAE,EAC5B,MACJ,CAEA,OAAQ,KAAK,kBAAiB,CAC1B,IAAK,gBACD,KAAK,0BAA0BA,CAAE,EACjC,MACJ,IAAK,sBACD,KAAK,+BAA+BA,CAAE,EACtC,MACJ,IAAK,kBACD,KAAK,4BAA4BA,CAAE,EACnC,MACJ,IAAK,eACD,KAAK,yBAAyBA,CAAE,EAChC,MACJ,IAAK,yBACD,KAAK,8BAA8BA,EAAI,EAAE,EACzC,MACJ,IAAK,0BACD,KAAK,8BAA8BA,EAAI,CAAC,EACxC,MACJ,IAAK,iBACD,KAAK,2BAA2BA,CAAE,EAClC,MACJ,IAAK,cACD,KAAK,wBAAwBA,EAAID,CAAc,EAC/C,MACJ,IAAK,iBACD,KAAK,2BAA2BC,CAAE,EAClC,MACJ,IAAK,cACD,KAAK,oBAAoBA,EAAI,EAAE,EAC/B,MACJ,IAAK,eACD,KAAK,oBAAoBA,EAAI,CAAC,EAC9B,MACJ,IAAK,cACD,KAAK,wBAAwBA,EAAID,CAAc,EAC/C,MACJ,IAAK,gBACD,KAAK,0BAA0BC,CAAE,EACjC,MACJ,IAAK,aACD,KAAK,uBAAuBA,CAAE,EAC9B,MACJ,QACI,KAAK,qBAAqBA,CAAE,EAC5B,KAChB,CACI,CAMA,qBAAqBxN,EAAW,CACxB,KAAK,eAAiB,IACtB,KAAK,gBAAkBA,EACvB,KAAK,UAAY,KAAK,gBAAkB,EAEhD,CAMA,gBAAgBA,EAAW,CACvB,MAAMqO,EAAY,KAAQ,KAAK,eAAiB,KAAK,aACjD,KAAK,cAAgB,IAAOA,EAAYrO,IACxC,KAAK,aAAe,KAAK,YAAc,GAAK,KAAK,YAEzD,CAMA,0BAA0BwN,EAAI,CAC1B,KAAK,SAAS,IAAI,EAAG,KAAK,YAAY,CAC1C,CAMA,+BAA+BA,EAAI,CAI/B,OAFqB,KAAK,MAAM,KAAK,gBAAkB,CAAS,EAAI,EAEhD,CAChB,IAAK,GACD,KAAK,SAAS,IAAI,CAAC,KAAK,aAAe,GAAK,KAAK,aAAe,EAAG,EACnE,MACJ,IAAK,GACD,KAAK,SAAS,IAAI,KAAK,aAAe,IAAK,KAAK,aAAe,EAAG,EAClE,MACJ,IAAK,GACD,KAAK,SAAS,IAAI,CAAC,KAAK,aAAe,GAAK,KAAK,aAAe,EAAG,EACnE,KAChB,CACI,CAMA,4BAA4BA,EAAI,CAE5B,MAAMnN,EAAY,KAAK,IAAI,KAAK,gBAAkB,GAAU,EAAI,EAAI,EAAI,GACxE,KAAK,SAAS,IAAIA,EAAY,KAAK,aAAe,GAAK,KAAK,aAAe,EAAG,CAClF,CAMA,yBAAyBmN,EAAI,CACzB,MAAME,EAAa,KAAK,IAAI,KAAK,gBAAkB,CAAC,EAAI,GAClDC,EAAU,KAAK,gBAAgB,EAAID,EACnCE,EAAa,KAAK,KAAKD,EAAU,KAAK,SAAS,CAAC,EACtD,KAAK,SAAS,IAAIC,EAAa,KAAK,aAAe,GAAK,KAAK,aAAe,EAAG,CACnF,CAOA,8BAA8BJ,EAAInN,EAAW,CACzC,MAAMiO,EAAa,KAAK,IAAI,KAAK,gBAAkB,EAAG,EAAIjO,EAC1D,KAAK,SAAS,IAAIiO,EAAa,KAAK,aAAe,GAAK,KAAK,aAAe,EAAG,CACnF,CAMA,2BAA2Bd,EAAI,CAC3B,MAAMe,EAAS,KAAK,IAAI,KAAK,gBAAkB,GAAG,EAAI,KAAK,aAAe,GACpEC,EAAS,KAAK,IAAI,KAAK,gBAAkB,GAAG,EAAI,KAAK,aAAe,GAC1E,KAAK,SAAS,IAAID,EAAQ,KAAK,aAAe,GAAMC,CAAM,CAC9D,CAOA,wBAAwBhB,EAAID,EAAgB,CACxC,GAAI,CAACA,EAAgB,CACjB,KAAK,qBAAqBC,CAAE,EAC5B,MACJ,CAEA,GAAI,KAAK,gBAAkB,IAEvB,KAAK,SAAS,IAAI,EAAG,KAAK,aAAe,EAAG,UACrC,KAAK,gBAAkB,EAAK,CAEnC,MAAMnN,EAAYkN,EAAe,SAAS,KAAK,QAAQ,EAAE,UAAS,EAClE,KAAK,SAAWlN,EAAU,SAAS,KAAK,aAAe,GAAG,CAC9D,MAEI,KAAK,SAAS,IAAI,EAAG,KAAK,aAAe,GAAG,CAEpD,CAMA,2BAA2BmN,EAAI,CAC3B,MAAMK,EAAS,GAAM,KAAK,gBAAkB,GACtC1P,EAAQ,KAAK,gBAAkB,EAC/BsQ,EAAU,KAAK,IAAItQ,CAAK,EAAI,KAAK,IAAI,EAAG0P,CAAM,EAC9Ca,EAAU,KAAK,IAAIvQ,CAAK,EAAI,KAAK,IAAI,EAAG0P,CAAM,EAAI,GAElDF,EAAU,KAAK,gBAAgB,EAAIc,EACnCX,EAAU,KAAK,gBAAgB,EAAI,KAAK,gBAAkB,KAAK,aAAe,GAAMY,EAEpFrO,EAAY,IAAIxC,EAAQ8P,EAAU,KAAK,SAAS,EAAGG,EAAU,KAAK,SAAS,CAAC,EAAE,UAAS,EAC7F,KAAK,SAAWzN,EAAU,SAAS,KAAK,YAAY,CACxD,CAOA,oBAAoBmN,EAAImB,EAAM,CAC1B,MAAMC,EAAiB,KAAK,IAAI,KAAK,gBAAkB,GAAG,EAAID,EAC9D,KAAK,SAAS,IAAIC,EAAiB,KAAK,aAAe,GAAK,KAAK,aAAe,EAAG,CACvF,CAOA,wBAAwBpB,EAAID,EAAgB,CACxC,GAAI,CAACA,EAAgB,CACjB,KAAK,SAAS,IAAI,EAAG,KAAK,YAAY,EACtC,MACJ,CAGA,MAAMsB,EAAe,IAAIhR,EAAQ,KAAK,YAAc,EAAG,KAAK,aAAe,CAAC,EAEtEwC,GADS,KAAK,gBAAkB,EAAMwO,EAAetB,GAClC,SAAS,KAAK,QAAQ,EAAE,UAAS,EAC1D,KAAK,SAAWlN,EAAU,SAAS,KAAK,aAAe,GAAG,CAC9D,CAMA,0BAA0BmN,EAAI,CAE1B,MAAMsB,EAAQ,KAAK,IAAI,KAAK,gBAAkB,CAAC,EAAI,GAC7CC,EAAe,EAAK,KAAK,gBAAkB,GACjD,KAAK,SAAS,IAAID,EAAO,KAAK,aAAeC,CAAY,CAC7D,CAMA,uBAAuBvB,EAAI,CACvB,MAAMwB,EAAiB,KAAK,SAAS,EAAI,KAAK,YAAc,EAAI,EAAI,GACpE,KAAK,SAAS,IAAIA,EAAiB,KAAK,aAAe,IAAK,KAAK,aAAe,EAAG,CACvF,CAMA,qBAAqBhP,EAAW,CAC5B,KAAK,oBAAsBA,EAG3B,KAAK,SAAS,gBAAgB,GAAI,EAE9B,KAAK,oBAAsB,KAAK,wBAChC,KAAK,QAAO,CAEpB,CAKA,gBAAiB,EAET,KAAK,SAAS,EAAI,KAClB,KAAK,SAAS,EAAI,KAAK,YAAc,IACrC,KAAK,SAAS,EAAI,KAAK,aAAe,KACtC,KAAK,QAAO,CAEpB,CAOA,OAAON,EAAKO,EAAgB,EAAG,CAC3B,GAAI,CAAC,KAAK,QAAS,OAGnB,MAAMC,EAAY,KAAK,SAAS,IAAI,KAAK,SAAS,SAASD,EAAgB,GAAI,CAAC,EAEhFP,EAAI,KAAI,EACRA,EAAI,UAAUQ,EAAU,EAAGA,EAAU,CAAC,EACtCR,EAAI,OAAO,KAAK,QAAQ,EAGpB,KAAK,YACL,KAAK,qBAAqBA,CAAG,EAE7B,KAAK,kBAAkBA,CAAG,EAI1B,OAAO,YACP,KAAK,gBAAgBA,CAAG,EAG5BA,EAAI,QAAO,CACf,CAMA,kBAAkBA,EAAK,CAEnB,MAAMuP,EAAqB,GAAO,KAAK,2BAA6B,GACpEvP,EAAI,YAAcuP,EAGlB,MAAMC,EAAS,KAAK,cAAa,EAG3BC,EAAa,KAAK,IAAI,KAAK,cAAgB,KAAK,eAAiB,KAAK,GAAK,CAAC,EAC5EC,EAAQ,EAAID,EAAa,GAK/B,OAHAzP,EAAI,MAAM0P,EAAOA,CAAK,EAGd,KAAK,KAAI,CACb,KAAKzH,EAAY,IACb,KAAK,aAAajI,EAAKwP,EAAQC,CAAU,EACzC,MACJ,KAAKxH,EAAY,MACb,KAAK,eAAejI,EAAKwP,EAAQC,CAAU,EAC3C,MACJ,KAAKxH,EAAY,KACb,KAAK,cAAcjI,EAAKwP,EAAQC,CAAU,EAC1C,MACJ,KAAKxH,EAAY,MACb,KAAK,eAAejI,EAAKwP,EAAQC,CAAU,EAC3C,MACJ,KAAKxH,EAAY,QACb,KAAK,iBAAiBjI,EAAKwP,EAAQC,CAAU,EAC7C,MACJ,KAAKxH,EAAY,OACb,KAAK,gBAAgBjI,EAAKwP,EAAQC,CAAU,EAC5C,MACJ,QACI,KAAK,iBAAiBzP,EAAKwP,EAAQC,CAAU,EAC7C,KAChB,CAEQzP,EAAI,YAAc,CACtB,CAMA,eAAgB,CACZ,MAAM2P,EAAe,CACjB,CAAC1H,EAAY,GAAG,EAAG,CACf,QAAS,UACT,UAAW,UACX,OAAQ,SACxB,EACY,CAACA,EAAY,KAAK,EAAG,CACjB,QAAS,UACT,UAAW,UACX,OAAQ,SACxB,EACY,CAACA,EAAY,IAAI,EAAG,CAChB,QAAS,UACT,UAAW,UACX,OAAQ,SACxB,EACY,CAACA,EAAY,KAAK,EAAG,CACjB,QAAS,UACT,UAAW,UACX,OAAQ,SACxB,EACY,CAACA,EAAY,OAAO,EAAG,CACnB,QAAS,UACT,UAAW,UACX,OAAQ,SACxB,EACY,CAACA,EAAY,MAAM,EAAG,CAClB,QAAS,UACT,UAAW,UACX,OAAQ,SACxB,CACA,EAEQ,OAAO0H,EAAa,KAAK,IAAI,GAAKA,EAAa1H,EAAY,GAAG,CAClE,CAQA,aAAajI,EAAKwP,EAAQC,EAAY,CAElC,MAAMG,EAAW,KAAK,IAAI,KAAK,cAAgB,CAAC,EAAI,GAGpD5P,EAAI,UAAYwP,EAAO,QACvBxP,EAAI,UAAS,EACbA,EAAI,QAAQ,EAAG,EAAG,KAAK,MAAQ,GAAK,KAAK,OAAS,GAAK,EAAG,EAAG,KAAK,GAAK,CAAC,EACxEA,EAAI,KAAI,EAGRA,EAAI,UAAYwP,EAAO,UACvBxP,EAAI,UAAS,EACbA,EAAI,QAAQ,CAAC,KAAK,MAAQ,GAAK4P,EAAW,EAAG,KAAK,MAAQ,GAAK,KAAK,OAAS,GAAK,IAAM,EAAG,KAAK,GAAK,CAAC,EACtG5P,EAAI,QAAQ,KAAK,MAAQ,GAAK4P,EAAW,EAAG,KAAK,MAAQ,GAAK,KAAK,OAAS,GAAK,GAAK,EAAG,KAAK,GAAK,CAAC,EACpG5P,EAAI,KAAI,EAGRA,EAAI,UAAYwP,EAAO,OACvBxP,EAAI,UAAS,EACbA,EAAI,IAAI,GAAI,GAAI,EAAG,EAAG,KAAK,GAAK,CAAC,EACjCA,EAAI,IAAI,EAAG,GAAI,EAAG,EAAG,KAAK,GAAK,CAAC,EAChCA,EAAI,KAAI,CACZ,CAQA,eAAeA,EAAKwP,EAAQC,EAAY,CAEpC,MAAMI,EAAY,EAAIJ,EAAa,GAGnCzP,EAAI,UAAYwP,EAAO,QACvBxP,EAAI,UAAS,EACbA,EAAI,QAAQ,EAAG,CAAC,KAAK,OAAS,GAAK,KAAK,MAAQ,GAAM6P,EAAW,KAAK,OAAS,GAAMA,EAAW,EAAG,EAAG,KAAK,GAAK,CAAC,EACjH7P,EAAI,KAAI,EAGRA,EAAI,YAAcwP,EAAO,UACzBxP,EAAI,UAAY,EAChB,QAASuC,EAAI,EAAGA,EAAI,EAAGA,IAAK,CACxB,MAAM9D,EAAS8D,EAAI,EAAK,KAAK,GAAK,EAC5BuN,EAAe,KAAK,IAAI,KAAK,cAAgB,EAAIvN,CAAC,EAAI,EAE5DvC,EAAI,UAAS,EACbA,EAAI,OAAO,KAAK,IAAIvB,CAAK,EAAI,EAAG,KAAK,OAAS,EAAG,EACjDuB,EAAI,iBACA,KAAK,IAAIvB,CAAK,EAAI,EAAIqR,EACtB,KAAK,OAAS,GACd,KAAK,IAAIrR,CAAK,EAAI,EAAIqR,EAAe,EACrC,KAAK,OAAS,EAC9B,EACY9P,EAAI,OAAM,CACd,CAGAA,EAAI,UAAYwP,EAAO,OACvBxP,EAAI,UAAS,EACbA,EAAI,QAAQ,EAAG,CAAC,KAAK,OAAS,GAAK,KAAK,MAAQ,GAAK,KAAK,OAAS,IAAM,EAAG,EAAG,KAAK,GAAK,CAAC,EAC1FA,EAAI,KAAI,CACZ,CAQA,cAAcA,EAAKwP,EAAQC,EAAY,CAEnC,MAAMM,EAAW,KAAK,IAAI,KAAK,cAAgB,EAAE,EAAI,GAC/CC,EAAW,KAAK,IAAI,KAAK,cAAgB,GAAK,CAAC,EAAI,IAGzDhQ,EAAI,UAAYwP,EAAO,OACvBxP,EAAI,UAAS,EACbA,EAAI,QAAQ,EAAG,KAAK,OAAS,GAAK,KAAK,MAAQ,GAAK,KAAK,OAAS,GAAK,EAAG,EAAG,KAAK,GAAK,CAAC,EACxFA,EAAI,KAAI,EAGRA,EAAI,UAAYwP,EAAO,QACvBxP,EAAI,UAAS,EACbA,EAAI,OAAO,EAAG,CAAC,KAAK,OAAS,GAAM+P,EAAW,CAAC,EAC/C/P,EAAI,iBAAiB,CAAC,KAAK,MAAQ,GAAMgQ,EAAW,EAAG,EAAG,CAAC,KAAK,MAAQ,GAAK,KAAK,OAAS,EAAG,EAC9FhQ,EAAI,iBAAiB,EAAG,KAAK,OAAS,GAAK,KAAK,MAAQ,GAAK,KAAK,OAAS,EAAG,EAC9EA,EAAI,iBAAiB,KAAK,MAAQ,GAAM+P,EAAW,EAAG,EAAG,EAAG,CAAC,KAAK,OAAS,GAAMA,EAAW,CAAC,EAC7F/P,EAAI,KAAI,EAGRA,EAAI,UAAYwP,EAAO,UACvBxP,EAAI,UAAS,EACbA,EAAI,QAAQ,EAAG,EAAG,KAAK,MAAQ,IAAOgQ,EAAW,EAAG,KAAK,OAAS,GAAMD,EAAW,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACvG/P,EAAI,KAAI,CACZ,CAQA,eAAeA,EAAKwP,EAAQC,EAAY,CAEpCzP,EAAI,UAAYwP,EAAO,QACvBxP,EAAI,YAAcwP,EAAO,UACzBxP,EAAI,UAAY,EAEhBA,EAAI,UAAS,EACbA,EAAI,OAAO,EAAG,CAAC,KAAK,OAAS,EAAG,EAChCA,EAAI,OAAO,KAAK,MAAQ,GAAK,CAAC,KAAK,OAAS,EAAG,EAC/CA,EAAI,OAAO,KAAK,MAAQ,GAAK,KAAK,OAAS,EAAG,EAC9CA,EAAI,OAAO,KAAK,MAAQ,GAAK,KAAK,OAAS,EAAG,EAC9CA,EAAI,OAAO,CAAC,KAAK,MAAQ,GAAK,KAAK,OAAS,EAAG,EAC/CA,EAAI,OAAO,CAAC,KAAK,MAAQ,GAAK,KAAK,OAAS,EAAG,EAC/CA,EAAI,OAAO,CAAC,KAAK,MAAQ,GAAK,CAAC,KAAK,OAAS,EAAG,EAChDA,EAAI,UAAS,EACbA,EAAI,KAAI,EACRA,EAAI,OAAM,EAGVA,EAAI,UAAYwP,EAAO,OACvBxP,EAAI,UAAS,EACbA,EAAI,IAAI,CAAC,KAAK,MAAQ,GAAK,CAAC,KAAK,OAAS,GAAK,EAAG,EAAG,KAAK,GAAK,CAAC,EAChEA,EAAI,IAAI,KAAK,MAAQ,IAAM,KAAK,OAAS,GAAK,EAAG,EAAG,KAAK,GAAK,CAAC,EAC/DA,EAAI,IAAI,CAAC,KAAK,MAAQ,GAAK,KAAK,OAAS,GAAK,EAAG,EAAG,KAAK,GAAK,CAAC,EAC/DA,EAAI,KAAI,CACZ,CAQA,iBAAiBA,EAAKwP,EAAQC,EAAY,CAEtC,MAAMQ,EAAgB,GAAMR,EAAa,GAGnCzM,EAAWhD,EAAI,qBAAqB,EAAG,EAAG,EAAG,EAAG,EAAG,KAAK,MAAQ,EAAG,EACzEgD,EAAS,aAAa,EAAGwM,EAAO,OAAS,IAAI,EAC7CxM,EAAS,aAAa,EAAG,aAAa,EACtChD,EAAI,UAAYgD,EAChBhD,EAAI,UAAS,EACbA,EAAI,IAAI,EAAG,EAAG,KAAK,MAAQ,GAAMiQ,EAAe,EAAG,KAAK,GAAK,CAAC,EAC9DjQ,EAAI,KAAI,EAGRA,EAAI,UAAYwP,EAAO,QACvBxP,EAAI,YAAcwP,EAAO,UACzBxP,EAAI,UAAY,EAGhBA,EAAI,UAAS,EACbA,EAAI,OAAO,EAAG,CAAC,KAAK,OAAS,EAAG,EAChCA,EAAI,OAAO,KAAK,MAAQ,GAAK,CAAC,KAAK,OAAS,EAAG,EAC/CA,EAAI,OAAO,KAAK,MAAQ,GAAK,CAAC,EAC9BA,EAAI,OAAO,KAAK,MAAQ,GAAK,KAAK,OAAS,EAAG,EAC9CA,EAAI,OAAO,EAAG,KAAK,OAAS,EAAG,EAC/BA,EAAI,OAAO,CAAC,KAAK,MAAQ,GAAK,KAAK,OAAS,EAAG,EAC/CA,EAAI,OAAO,CAAC,KAAK,MAAQ,GAAK,CAAC,EAC/BA,EAAI,OAAO,CAAC,KAAK,MAAQ,GAAK,CAAC,KAAK,OAAS,EAAG,EAChDA,EAAI,UAAS,EACbA,EAAI,KAAI,EACRA,EAAI,OAAM,EAGVA,EAAI,YAAcwP,EAAO,OACzBxP,EAAI,UAAS,EACbA,EAAI,OAAO,EAAG,CAAC,KAAK,OAAS,EAAG,EAChCA,EAAI,OAAO,EAAG,KAAK,OAAS,EAAG,EAC/BA,EAAI,OAAO,CAAC,KAAK,MAAQ,GAAK,CAAC,EAC/BA,EAAI,OAAO,KAAK,MAAQ,GAAK,CAAC,EAC9BA,EAAI,OAAM,CACd,CAQA,gBAAgBA,EAAKwP,EAAQC,EAAY,CAErC,MAAMS,EAAY,KAAK,IAAI,KAAK,cAAgB,CAAC,EAAI,GAGrDlQ,EAAI,YAAc,GAAMyP,EAAa,GAGrCzP,EAAI,UAAYwP,EAAO,QACvBxP,EAAI,UAAS,EACbA,EAAI,QAAQ,EAAG,EAAG,KAAK,MAAQ,GAAMkQ,EAAY,EAAG,KAAK,OAAS,GAAMA,EAAY,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACxGlQ,EAAI,KAAI,EAGRA,EAAI,YAAcwP,EAAO,UACzBxP,EAAI,UAAY,EAChBA,EAAI,QAAU,QAEd,QAASuC,EAAI,EAAGA,EAAI,EAAGA,IAAK,CACxB,MAAM9D,EAAS8D,EAAI,EAAK,KAAK,GAAK,EAC5B4N,EAAc,KAAK,IAAI,KAAK,cAAgB,EAAI5N,CAAC,EAAI,EACrD6C,EAAS,KAAK,IAAI3G,CAAK,EAAI,KAAK,MAAQ,GACxC4G,EAAS,KAAK,IAAI5G,CAAK,EAAI,KAAK,OAAS,GAE/CuB,EAAI,UAAS,EACbA,EAAI,OAAOoF,EAAQC,CAAM,EACzBrF,EAAI,OACAoF,EAAS,KAAK,IAAI3G,CAAK,GAAK,GAAK0R,GACjC9K,EAAS,KAAK,IAAI5G,CAAK,GAAK,GAAK0R,EACjD,EACYnQ,EAAI,OAAM,CACd,CAGAA,EAAI,UAAYwP,EAAO,OACvBxP,EAAI,UAAS,EACbA,EAAI,IAAI,GAAI,GAAI,EAAG,EAAG,KAAK,GAAK,CAAC,EACjCA,EAAI,IAAI,EAAG,GAAI,EAAG,EAAG,KAAK,GAAK,CAAC,EAChCA,EAAI,KAAI,CACZ,CAQA,iBAAiBA,EAAKwP,EAAQC,EAAY,CAEtCzP,EAAI,UAAYwP,EAAO,QACvBxP,EAAI,YAAcwP,EAAO,UACzBxP,EAAI,UAAY,EAEhBA,EAAI,UAAS,EACbA,EAAI,IAAI,EAAG,EAAG,KAAK,MAAQ,GAAK,EAAG,KAAK,GAAK,CAAC,EAC9CA,EAAI,KAAI,EACRA,EAAI,OAAM,EAGVA,EAAI,UAAYwP,EAAO,OACvBxP,EAAI,UAAS,EACbA,EAAI,IAAI,GAAI,GAAI,EAAG,EAAG,KAAK,GAAK,CAAC,EACjCA,EAAI,IAAI,EAAG,GAAI,EAAG,EAAG,KAAK,GAAK,CAAC,EAChCA,EAAI,KAAI,CACZ,CAMA,qBAAqBA,EAAK,CACtB,MAAMoQ,EAAW,KAAK,mBAAqB,KAAK,uBAC1ChP,EAAQ,EAAIgP,EACZV,EAAQ,EAAIU,EAAW,EAE7BpQ,EAAI,YAAcoB,EAClBpB,EAAI,MAAM0P,EAAOA,CAAK,EAGtB,MAAMF,EAAS,KAAK,cAAa,EAC3Ba,EAAgB,EAEtB,QAAS9N,EAAI,EAAGA,EAAI8N,EAAe9N,IAAK,CACpC,MAAM9D,EAAS8D,EAAI8N,EAAiB,KAAK,GAAK,EACxCC,EAAWF,EAAW,GACtBhS,EAAI,KAAK,IAAIK,CAAK,EAAI6R,EACtBjS,EAAI,KAAK,IAAII,CAAK,EAAI6R,EAE5BtQ,EAAI,UAAYwP,EAAO,OACvBxP,EAAI,UAAS,EACbA,EAAI,IAAI5B,EAAGC,EAAG,GAAK,EAAI+R,GAAW,EAAG,KAAK,GAAK,CAAC,EAChDpQ,EAAI,KAAI,CACZ,CAEAA,EAAI,YAAc,CACtB,CAMA,gBAAgBA,EAAK,CASjB,GAPAA,EAAI,YAAc,UAClBA,EAAI,UAAY,EAChBA,EAAI,UAAS,EACbA,EAAI,IAAI,EAAG,EAAG,KAAK,gBAAiB,EAAG,KAAK,GAAK,CAAC,EAClDA,EAAI,OAAM,EAGN,KAAK,SAAS,UAAS,EAAK,EAAG,CAC/BA,EAAI,YAAc,UAClBA,EAAI,UAAY,EAChBA,EAAI,UAAS,EACbA,EAAI,OAAO,EAAG,CAAC,EACf,MAAM8E,EAAgB,GACtB9E,EAAI,OAAO,KAAK,SAAS,EAAI8E,EAAe,KAAK,SAAS,EAAIA,CAAa,EAC3E9E,EAAI,OAAM,CACd,CAGA,MAAMuQ,EAAW,KAAK,MAChBC,EAAY,EACZC,EAAgB,KAAK,OAAS,KAAK,UAEzCzQ,EAAI,UAAY,UAChBA,EAAI,SAAS,CAACuQ,EAAW,EAAG,CAAC,KAAK,OAAS,EAAI,GAAIA,EAAUC,CAAS,EACtExQ,EAAI,UAAY,UAChBA,EAAI,SAAS,CAACuQ,EAAW,EAAG,CAAC,KAAK,OAAS,EAAI,GAAIA,EAAWE,EAAeD,CAAS,EAGtFxQ,EAAI,UAAY,UAChBA,EAAI,KAAO,YACXA,EAAI,UAAY,SAChBA,EAAI,SAAS,GAAG,KAAK,IAAI,KAAK,KAAK,2BAA2B,QAAQ,CAAC,CAAC,KAAM,EAAG,KAAK,OAAS,EAAI,EAAE,CACzG,CAOA,mBAAmBiD,EAASyN,EAAU,GAAI,CAKtC,OAJA,KAAK,gBAAkBzN,EACvB,KAAK,cAAgB,EAGbA,EAAO,CACX,IAAK,OACD,KAAK,UAAYyN,EAAQ,WAAa,GACtC,KAAK,UAAYA,EAAQ,WAAa,EACtC,MACJ,IAAK,SACD,KAAK,UAAYA,EAAQ,WAAa,GACtC,KAAK,UAAYA,EAAQ,WAAa,EACtC,MACJ,IAAK,YACD,KAAK,cAAgB,GACrB,KAAK,gBAAkBA,EAAQ,QAAU,IAAIvS,EAAQ,EAAG,CAAC,EACzD,MACJ,IAAK,SACD,KAAK,UAAYuS,EAAQ,WAAa,IACtC,MACJ,IAAK,SACD,KAAK,UAAYA,EAAQ,QAAU,GACnC,KAAK,UAAYA,EAAQ,WAAa,EACtC,MACJ,IAAK,aACD,KAAK,kBAAoBA,EAAQ,mBAAqB,gBACtD,KAAK,gBAAkB,EACvB,KAAK,gBAAkB,EACvB,KAAK,gBAAkB,KAAK,SAAS,MAAK,EAC1C,KAChB,CAEQ,QAAQ,IAAI,SAAS,KAAK,EAAE,6BAA6BzN,CAAO,EAAE,CACtE,CAOA,mBAAmBvC,EAAQP,EAAS,IAAIhC,EAAQ,EAAG,CAAC,EAAG,CACnD,KAAK,gBAAkBuC,EAAO,MAAK,EACnC,KAAK,gBAAkBP,EAAO,MAAK,EACnC,KAAK,cAAgB,EACzB,CAOA,yBAAyBwI,EAAagI,EAAgB,EAAK,CACvD,KAAK,mBAAqBhI,EAC1B,KAAK,2BAA6B2C,EAAS,MAAMqF,EAAe,GAAK,CAAG,EAGxE,KAAK,aAAe,KAAK,UAAY,KAAK,2BAC1C,KAAK,kBAAoB,KAAK,eAAiB,KAAK,2BAGhD,KAAK,6BAA+BA,GACpC,QAAQ,IAAI,SAAS,KAAK,EAAE,yCAAyC,KAAK,0BAA0B,OAAOA,CAAa,OAAOhI,CAAW,EAAE,CAEpJ,CAOA,WAAWxF,EAAQ,CACf,GAAI,KAAK,YACL,MAAO,CACH,YAAa,EACb,OAAQ,KAAK,OACb,UAAW,GACX,WAAY,CAC5B,EAGQ,MAAMgC,EAAe,KAAK,IAAIhC,EAAQ,KAAK,MAAM,EAKjD,OAJA,KAAK,QAAUgC,EAEf,QAAQ,IAAI,SAAS,KAAK,EAAE,SAASA,CAAY,oBAAoB,KAAK,MAAM,IAAI,KAAK,SAAS,EAAE,EAEhG,KAAK,QAAU,GACf,KAAK,oBAAmB,EACjB,CACH,YAAaA,EACb,OAAQ,EACR,UAAW,GACX,WAAY,KAAK,UACjC,GAGe,CACH,YAAaA,EACb,OAAQ,KAAK,OACb,UAAW,GACX,WAAY,CACxB,CACI,CAKA,qBAAsB,CAClB,KAAK,YAAc,GACnB,KAAK,mBAAqB,EAC1B,KAAK,UAAY,GAEjB,QAAQ,IAAI,SAAS,KAAK,EAAE,4BAA4B,KAAK,UAAU,EAAE,CAC7E,CAOA,gBAAgB0I,EAAgB,CAC5B,GAAI,CAAC,KAAK,WAAa,KAAK,aAAe,CAACA,EACxC,MAAO,GAIX,MAAM+C,EAAc,KAAK,eAAc,EAGvC,OAFyB,KAAK,SAAS,SAAS/C,CAAc,GAEnC+C,CAC/B,CAMA,gBAAiB,CASb,MARiB,CACb,CAAC3I,EAAY,GAAG,EAAG,IACnB,CAACA,EAAY,KAAK,EAAG,IACrB,CAACA,EAAY,IAAI,EAAG,IACpB,CAACA,EAAY,KAAK,EAAG,IACrB,CAACA,EAAY,OAAO,EAAG,IACvB,CAACA,EAAY,MAAM,EAAG,GAClC,EACwB,KAAK,IAAI,GAAK,GAClC,CAOA,OAAO4F,EAAgB,CACnB,GAAI,CAAC,KAAK,gBAAgBA,CAAc,EACpC,OAAO,KAIX,KAAK,eAAkB,IAAO,KAAK,kBAAqBvC,EAAS,OAAO,KAAM,GAAG,EACjF,KAAK,UAAY,GAGjB,MAAM3K,EAAYkN,EAAe,SAAS,KAAK,QAAQ,EAAE,UAAS,EAElE,MAAO,CACH,SAAU,KAAK,SAAS,MAAK,EAC7B,UAAWlN,EACX,KAAM,KAAK,KACX,OAAQ,KAAK,gBAAe,CACxC,CACI,CAMA,iBAAkB,CASd,MARkB,CACd,CAACsH,EAAY,GAAG,EAAG,GACnB,CAACA,EAAY,KAAK,EAAG,GACrB,CAACA,EAAY,IAAI,EAAG,GACpB,CAACA,EAAY,KAAK,EAAG,GACrB,CAACA,EAAY,OAAO,EAAG,GACvB,CAACA,EAAY,MAAM,EAAG,EAClC,EACyB,KAAK,IAAI,GAAK,EACnC,CAMA,WAAY,CACR,MAAO,CACH,GAAI,KAAK,GACT,KAAM,KAAK,KACX,SAAU,KAAK,SAAS,MAAK,EAC7B,OAAQ,KAAK,OACb,UAAW,KAAK,UAChB,gBAAiB,KAAK,gBACtB,2BAA4B,KAAK,2BACjC,mBAAoB,KAAK,mBACzB,UAAW,KAAK,UAChB,YAAa,KAAK,YAClB,WAAY,KAAK,UAC7B,CACI,CACJ,CCjwCO,MAAM4I,CAAa,CACtB,YAAYrN,EAAcuE,EAAY,aAActE,EAAesE,EAAY,cAAe7F,EAAoB,KAAM,CACpH,KAAK,YAAcsB,EACnB,KAAK,aAAeC,EACpB,KAAK,kBAAoBvB,EAGzB,KAAK,cAAgB,CAAA,EACrB,KAAK,UAAY,GACjB,KAAK,WAAa,GAGlB,KAAK,YAAc,EACnB,KAAK,eAAiB,GACtB,KAAK,cAAgB,EACrB,KAAK,WAAa,KAClB,KAAK,qBAAuB,EAC5B,KAAK,oBAAsB,EAC3B,KAAK,qBAAuB,EAC5B,KAAK,mBAAqB,GAG1B,KAAK,cAAgB,EACrB,KAAK,cAAgB,IACrB,KAAK,WAAa,EAGlB,KAAK,WAAa,CAAA,EAClB,KAAK,oBAAsB,CAAA,EAG3B,KAAK,mBAAqB,QAC1B,KAAK,qBAAuB,KAAK,+BAA8B,EAG/D,KAAK,oBAAsB,EAC3B,KAAK,mBAAqB,EAC1B,KAAK,WAAa,EAGlB,KAAK,cAAgB,KACrB,KAAK,SAAW,GAChB,KAAK,UAAY,KAAK,KAAKsB,EAAc,KAAK,QAAQ,EACtD,KAAK,WAAa,KAAK,KAAKC,EAAe,KAAK,QAAQ,EAKxD,QAAQ,IAAI,0BAA0B,CAC1C,CAOA,OAAOnD,EAAWuN,EAAiB,KAAM,CAErC,KAAK,YAAcvN,EAGnB,KAAK,qBAAqBA,CAAS,EAGnC,KAAK,oBAAoBA,EAAWuN,CAAc,EAGlD,KAAK,oBAAoBvN,EAAWuN,CAAc,EAGlD,KAAK,wBAAuB,EAG5B,KAAK,iBAAiBvN,CAAS,EAS/B,KAAK,oBAAmB,CAC5B,CAMA,qBAAqBA,EAAW,CACxB,CAAC,KAAK,gBAAkB,KAAK,cAAc,SAAW,GAEtD,KAAK,cAAa,EAGlB,KAAK,iBACL,KAAK,eAAiBA,EAE9B,CAOA,oBAAoBA,EAAWuN,EAAgB,CACvC,CAAC,KAAK,gBAAkB,CAAC,KAAK,aAG9B,KAAK,YAAc,KAAK,eACxB,KAAK,cAAc,OAAS,KAAK,YACjC,KAAK,qBAAuB,KAAK,WAAW,eAE5C,KAAK,mBAAkB,EACvB,KAAK,WAAa,GAItB,KAAK,2BAA2BvN,CAAS,EAC7C,CAOA,oBAAoBA,EAAWuN,EAAgB,CAC3C,QAAS,EAAI,KAAK,cAAc,OAAS,EAAG,GAAK,EAAG,IAAK,CACrD,MAAM7C,EAAQ,KAAK,cAAc,CAAC,EAE9BA,EAAM,SACNA,EAAM,OAAO1K,EAAWuN,CAAc,EAGtC,KAAK,0BAA0B7C,CAAK,EAE5C,CACJ,CAKA,yBAA0B,SACtB,QAAS,EAAI,KAAK,cAAc,OAAS,EAAG,GAAK,EAAG,IAAK,CACrD,MAAMA,EAAQ,KAAK,cAAc,CAAC,GAE9BA,EAAM,WAAa,CAACA,EAAM,UAEtBA,EAAM,aAEN,KAAK,sBACL,KAAK,qBACL,KAAK,YAAcA,EAAM,WACzB,QAAQ,IAAI,SAASA,EAAM,EAAE,2BAA2B,KAAK,oBAAsB,KAAK,oBAAoB,MAAI3K,EAAA,KAAK,aAAL,YAAAA,EAAiB,eAAgB,CAAC,EAAE,GAC7I2K,EAAM,YAEb,KAAK,uBACL,QAAQ,IAAI,SAASA,EAAM,EAAE,4BAA4B,KAAK,oBAAsB,KAAK,oBAAoB,MAAI8F,EAAA,KAAK,aAAL,YAAAA,EAAiB,eAAgB,CAAC,EAAE,EAGrJ,KAAK,eAAe9F,CAAK,GAI7B,KAAK,kBAAkBA,CAAK,EAC5B,KAAK,cAAc,OAAO,EAAG,CAAC,EAEtC,CACJ,CAKA,eAAgB,CAaZ,GAZA,KAAK,cACL,KAAK,eAAiB,GACtB,KAAK,cAAgB,EACrB,KAAK,qBAAuB,EAC5B,KAAK,oBAAsB,EAC3B,KAAK,qBAAuB,EAC5B,KAAK,mBAAqB,GAG1B,KAAK,WAAa,KAAK,mBAAmB,KAAK,WAAW,EAGtD,KAAK,WAAW,cAChB,QAASzI,EAAI,EAAGA,EAAI,KAAK,WAAW,cAAc,OAAQA,IACtD,KAAK,mBAAmBA,CAAC,EAAI,EAKrC,KAAK,cAAgB,KAAK,IAAI,IAAK,KAAQ,KAAK,YAAc,EAAG,EAEjE,QAAQ,IAAI,iBAAiB,KAAK,WAAW,IAAK,KAAK,UAAU,CACrE,CAOA,mBAAmB2I,EAAY,CAG3B,MAAM3C,EAAe,EADM,KAAK,MAAM2C,EAAa,CAAC,EAI9C6F,EAAa,KAAK,kBAAkB7F,CAAU,EAG9C8F,EAAgB,KAAK,sBAAsB9F,EAAY3C,CAAY,EAEzE,MAAO,CACH,WAAY2C,EACZ,aAAc3C,EACd,WAAYwI,EACZ,cAAeC,EACf,WAAY,KAAK,IAAI,GAAI,KAAK,MAAM9F,EAAa,CAAC,EAAI,CAAC,EACvD,aAAcA,EAAa,IAAM,EACjC,QAASA,EAAa,KAAO,CACzC,CACI,CAOA,kBAAkBA,EAAY,CAC1B,MAAM+F,EAAQ,CAAA,EAGd,OAAI/F,GAAc,GACd+F,EAAM,KAAK,CAAE,KAAMhJ,EAAY,IAAK,OAAQ,GAAK,EACjDgJ,EAAM,KAAK,CAAE,KAAMhJ,EAAY,MAAO,OAAQ,GAAK,GAG9CiD,GAAc,GACnB+F,EAAM,KAAK,CAAE,KAAMhJ,EAAY,IAAK,OAAQ,GAAK,EACjDgJ,EAAM,KAAK,CAAE,KAAMhJ,EAAY,MAAO,OAAQ,GAAK,EACnDgJ,EAAM,KAAK,CAAE,KAAMhJ,EAAY,KAAM,OAAQ,GAAK,EAClDgJ,EAAM,KAAK,CAAE,KAAMhJ,EAAY,MAAO,OAAQ,GAAK,IAInDgJ,EAAM,KAAK,CAAE,KAAMhJ,EAAY,IAAK,OAAQ,GAAK,EACjDgJ,EAAM,KAAK,CAAE,KAAMhJ,EAAY,MAAO,OAAQ,GAAK,EACnDgJ,EAAM,KAAK,CAAE,KAAMhJ,EAAY,KAAM,OAAQ,GAAK,EAClDgJ,EAAM,KAAK,CAAE,KAAMhJ,EAAY,MAAO,OAAQ,IAAM,EACpDgJ,EAAM,KAAK,CAAE,KAAMhJ,EAAY,QAAS,OAAQ,IAAM,EACtDgJ,EAAM,KAAK,CAAE,KAAMhJ,EAAY,OAAQ,OAAQ,GAAK,GAGjDgJ,CACX,CAQA,sBAAsB/F,EAAY3C,EAAc,CAC5C,OAAO,KAAK,yBAAyB2C,EAAY3C,CAAY,CACjE,CAQA,yBAAyB2C,EAAY3C,EAAc,CAC/C,MAAM2I,EAAW,CAAA,EAEjB,OAAQhG,EAAU,CACd,IAAK,GAEDgG,EAAS,KAAK,CACV,KAAM,YACN,MAAO3I,EACP,UAAW,OACX,gBAAiB,aACjB,kBAAmB,gBACnB,QAAS,IAC7B,CAAiB,EACD,MAEJ,IAAK,GAED2I,EAAS,KAAK,CACV,KAAM,YACN,MAAO3I,EACP,UAAW,cACX,gBAAiB,aACjB,kBAAmB,sBACnB,QAAS,IAC7B,CAAiB,EACD,MAEJ,IAAK,GAED2I,EAAS,KAAK,CACV,KAAM,YACN,MAAO,KAAK,MAAM3I,EAAe,EAAG,EACpC,UAAW,WACX,gBAAiB,aACjB,kBAAmB,iBACvC,CAAiB,EACD2I,EAAS,KAAK,CACV,KAAM,YACN,MAAO,KAAK,KAAK3I,EAAe,EAAG,EACnC,gBAAiB,aACjB,kBAAmB,YACvC,CAAiB,EACD,MAEJ,IAAK,GAED2I,EAAS,KAAK,CACV,KAAM,YACN,MAAO3I,EACP,UAAW,UACX,gBAAiB,aACjB,kBAAmB,cACvC,CAAiB,EACD,MAEJ,IAAK,GAED2I,EAAS,KAAK,CACV,KAAM,YACN,MAAO,KAAK,MAAM3I,EAAe,EAAG,EACpC,UAAW,OACX,gBAAiB,aACjB,kBAAmB,wBACvC,CAAiB,EACD2I,EAAS,KAAK,CACV,KAAM,YACN,MAAO,KAAK,KAAK3I,EAAe,EAAG,EACnC,UAAW,OACX,gBAAiB,aACjB,kBAAmB,yBACvC,CAAiB,EACD,MAEJ,QAGI,QADsB2C,EAAa,GAAK,EACpB,CAChB,IAAK,GACDgG,EAAS,KAAK,CACV,KAAM,YACN,MAAO,KAAK,MAAM3I,EAAe,EAAG,EACpC,UAAW,cACX,gBAAiB,aACjB,kBAAmB,gBAC/C,CAAyB,EACD2I,EAAS,KAAK,CACV,KAAM,OACN,MAAO,KAAK,KAAK3I,EAAe,EAAG,EACnC,gBAAiB,aACjB,kBAAmB,aAC/C,CAAyB,EACD,MACJ,IAAK,GACD2I,EAAS,KAAK,CACV,KAAM,YACN,MAAO3I,EACP,UAAW,SACX,gBAAiB,aACjB,kBAAmB,gBAC/C,CAAyB,EACD,MACJ,IAAK,GACD2I,EAAS,KAAK,CACV,KAAM,YACN,MAAO,KAAK,MAAM3I,EAAe,EAAG,EACpC,UAAW,OACX,gBAAiB,aACjB,kBAAmB,aAC/C,CAAyB,EACD2I,EAAS,KAAK,CACV,KAAM,YACN,MAAO,KAAK,MAAM3I,EAAe,EAAG,EACpC,UAAW,OACX,gBAAiB,aACjB,kBAAmB,cAC/C,CAAyB,EACD2I,EAAS,KAAK,CACV,KAAM,YACN,MAAO,KAAK,KAAK3I,EAAe,EAAG,EACnC,gBAAiB,aACjB,kBAAmB,aAC/C,CAAyB,EACD,MACJ,IAAK,GACD2I,EAAS,KAAK,CACV,KAAM,YACN,MAAO3I,EACP,UAAW,QACX,gBAAiB,aACjB,kBAAmB,eAC/C,CAAyB,EACD,KACxB,CACgB,KAChB,CAEQ,OAAO2I,CACX,CAKA,oBAAqB,CACjB,GAAI,CAAC,KAAK,YAAc,KAAK,sBAAwB,KAAK,WAAW,aACjE,OAIJ,MAAMC,EAAY,KAAK,gBAAgB,KAAK,WAAW,UAAU,EAI3DlO,EADc,KAAK,mBAAmB,KAAK,WAAW,aAAa,EAC7C,QAGtBmO,EAAW,KAAK,sBAAsBnO,CAAO,EAG7C+H,EAAQ,KAAK,WAAWoG,EAAS,EAAGA,EAAS,EAAGD,CAAS,EAE/D,GAAInG,EAAO,CAEP,MAAMqG,EAAiB,CACnB,UAAW/F,EAAS,OAAO,GAAI,EAAE,EACjC,UAAWA,EAAS,OAAO,EAAG,CAAC,CAC/C,EAGgBrI,EAAQ,oBACRoO,EAAe,kBAAoBpO,EAAQ,mBAG/C+H,EAAM,mBAAmB/H,EAAQ,gBAAiBoO,CAAc,EAG5DpO,EAAQ,OAAS,aACjB,KAAK,oBAAoB+H,EAAO/H,CAAO,EAG3C,KAAK,sBACT,CACJ,CAOA,gBAAgB8N,EAAY,CACxB,MAAMO,EAAS,KAAK,OAAM,EAC1B,IAAIC,EAAmB,EAEvB,UAAWC,KAAYT,EAEnB,GADAQ,GAAoBC,EAAS,OACzBF,GAAUC,EACV,OAAOC,EAAS,KAKxB,OAAOT,EAAW,CAAC,EAAE,IACzB,CAOA,mBAAmBG,EAAU,CAEzB,MAAMO,EAAoB,CAAA,EAE1B,QAASlP,EAAI,EAAGA,EAAI2O,EAAS,OAAQ3O,IAAK,CACtC,MAAMU,EAAUiO,EAAS3O,CAAC,GACL,KAAK,mBAAmBA,CAAC,GAAK,GAEhCU,EAAQ,OACvBwO,EAAkB,KAAK,CAAE,QAAAxO,EAAS,MAAOV,CAAC,CAAE,CAEpD,CAEA,GAAIkP,EAAkB,SAAW,EAE7B,eAAQ,KAAK,gEAAgE,EACtE,CAAE,QAASP,EAAS,CAAC,EAAG,MAAO,CAAC,EAI3C,MAAMQ,EAAWD,EAAkB,KAAK,MAAM,KAAK,OAAM,EAAKA,EAAkB,MAAM,CAAC,EAGvF,YAAK,mBAAmBC,EAAS,KAAK,IAE/BA,CACX,CAOA,sBAAsBzO,EAAS,CAC3B,OAAQA,EAAQ,KAAI,CAChB,IAAK,SACD,OAAO,IAAI9E,EACPmN,EAAS,OAAO,GAAI,KAAK,YAAc,EAAE,EACzC,GACpB,EAEY,IAAK,YACD,OAAO,IAAInN,EACP,KAAK,YAAc,EACnB,GACpB,EAEY,IAAK,YACD,OAAO,IAAIA,EACPmN,EAAS,OAAO,GAAI,KAAK,YAAc,EAAE,EACzCA,EAAS,OAAO,IAAK,GAAG,CAC5C,EAEY,IAAK,OAED,MAAM2D,EAAO,KAAK,OAAM,EAAK,GAAM,OAAS,QAC5C,OAAO,IAAI9Q,EACP8Q,IAAS,OAAS,IAAM,KAAK,YAAc,GAC3C3D,EAAS,OAAO,GAAI,GAAG,CAC3C,EAEY,QACI,OAAO,IAAInN,EACPmN,EAAS,OAAO,GAAI,KAAK,YAAc,EAAE,EACzC,GACpB,CACA,CACI,CASA,WAAWlN,EAAGC,EAAG6C,EAAO+G,EAAY,IAAK,CAErC,IAAI+C,EAAQ,KAAK,iBAAgB,EAEjC,OAAKA,GAKDA,EAAM,MAAK,EACXA,EAAM,SAAS,IAAI5M,EAAGC,CAAC,EACvB2M,EAAM,KAAO9J,EACb8J,EAAM,UAAYA,EAAM,iBAAiB9J,CAAI,EAC7C8J,EAAM,OAASA,EAAM,UACrBA,EAAM,UAAYA,EAAM,iBAAiB9J,CAAI,EAC7C8J,EAAM,aAAeA,EAAM,UAC3BA,EAAM,WAAaA,EAAM,kBAAkB9J,CAAI,GAV/C8J,EAAQ,IAAI4C,EAAMxP,EAAGC,EAAG6C,EAAM,KAAK,YAAa,KAAK,YAAY,EAcrE,KAAK,0BAA0B8J,CAAK,EAGpC,KAAK,cAAc,KAAKA,CAAK,EAC7B,KAAK,sBAGD,KAAK,mBACL,KAAK,kBAAkB,IAAIA,CAAK,EAGpC,QAAQ,IAAI,WAAW9J,CAAI,cAAc9C,CAAC,KAAKC,CAAC,sBAAsB,KAAK,cAAc,MAAM,EAAE,EAC1F2M,CACX,CAMA,kBAAmB,CACf,OAAO,KAAK,UAAU,OAAS,EAAI,KAAK,UAAU,IAAG,EAAK,IAC9D,CAMA,kBAAkBA,EAAO,CACjB,KAAK,UAAU,OAAS,KAAK,aAC7BA,EAAM,MAAK,EACX,KAAK,UAAU,KAAKA,CAAK,GAIzB,KAAK,mBACL,KAAK,kBAAkB,OAAOA,CAAK,CAE3C,CAOA,oBAAoBA,EAAO/H,EAAS,CAChC,MAAM0O,EAAY,KAAK,qBAAqB1O,EAAQ,SAAS,EAGvD2O,EAAiBD,EAAU,QAAQ,OACnCxR,EAAS,KAAK,yBAAyB8C,EAAQ,UAAW2O,CAAc,EAE9E5G,EAAM,mBAAmB2G,EAAU,OAAQxR,CAAM,EACjDwR,EAAU,QAAQ,KAAK3G,CAAK,EAE5B,QAAQ,IAAI,kBAAkB/H,EAAQ,SAAS,+BAA+B0O,EAAU,QAAQ,MAAM,EAAE,CAC5G,CAOA,qBAAqBE,EAAe,CAChC,IAAIF,EAAY,KAAK,WAAW,KAAKG,GAAKA,EAAE,OAASD,GAAiBC,EAAE,QAAQ,OAAS,CAAC,EAE1F,OAAKH,IACDA,EAAY,CACR,KAAME,EACN,OAAQ,IAAI1T,EAAQ,KAAK,YAAc,EAAG,GAAG,EAC7C,QAAS,CAAA,EACT,cAAe,CAC/B,EACY,KAAK,WAAW,KAAKwT,CAAS,GAG3BA,CACX,CAQA,yBAAyBE,EAAenL,EAAO,CAC3C,OAAQmL,EAAa,CACjB,IAAK,OACD,OAAO,IAAI1T,GAASuI,EAAQ,GAAK,GAAI,CAAC,EAE1C,IAAK,cACD,MAAMuI,EAAOvI,EAAQ,IAAM,EAAI,GAAK,EAC9BqL,EAAM,KAAK,MAAMrL,EAAQ,CAAC,EAChC,OAAO,IAAIvI,EAAQ8Q,GAAQ8C,EAAM,GAAK,GAAIA,EAAM,EAAE,EAEtD,IAAK,WACD,MAAMC,EAAc,KAAK,OAAO,GAAK,KAAK,KAAK,EAAI,EAAItL,CAAK,GAAK,CAAC,EAC5DuL,EAAWvL,EAASsL,GAAeA,EAAc,GAAM,EACvDE,EAAWF,EAAc,EAC/B,OAAO,IAAI7T,GAAS8T,EAAWC,EAAW,EAAI,IAAO,GAAIF,EAAc,EAAE,EAE7E,IAAK,UACD,MAAMG,EAAc,EACpB,GAAIzL,EAAQyL,EAER,OAAO,IAAIhU,GAASuI,EAAQ,GAAK,GAAI,GAAG,EACrC,GAAIA,EAAQyL,EAAc,EAAI,EAAG,CAEpC,MAAMC,EAAW1L,EAAQyL,EACzB,OAAO,IAAIhU,GAASiU,EAAW,GAAK,GAAI,CAAC,CAC7C,KAAO,CAEH,MAAMC,EAAW3L,GAASyL,EAAc,EAAI,GAC5C,OAAO,IAAIhU,GAASkU,EAAW,GAAK,GAAI,EAAE,CAC9C,CAEJ,IAAK,SACD,MAAM5T,EAASiI,EAAQ,EAAK,KAAK,GAAK,EAChCyH,EAAS,GACf,OAAO,IAAIhQ,EAAQ,KAAK,IAAIM,CAAK,EAAI0P,EAAQ,KAAK,IAAI1P,CAAK,EAAI0P,CAAM,EAEzE,IAAK,QACD,MAAMmE,EAAW,KAAK,MAAM5L,EAAQ,CAAC,EAC/B6L,EAAW7L,EAAQ,EACzB,OAAO,IAAIvI,GAASoU,EAAW,IAAM,GAAKD,EAAW,IAAKA,EAAW,EAAE,EAE3E,QAEI,OAAO,IAAInU,GAASuI,EAAQ,GAAK,GAAI,CAAC,CACtD,CACI,CAMA,iBAAiBpG,EAAW,CACxB,QAASiC,EAAI,KAAK,WAAW,OAAS,EAAGA,GAAK,EAAGA,IAAK,CAClD,MAAMoP,EAAY,KAAK,WAAWpP,CAAC,EAOnC,GANAoP,EAAU,eAAiBrR,EAAY,IAGvCqR,EAAU,QAAUA,EAAU,QAAQ,OAAO3G,GAASA,EAAM,QAAU,CAACA,EAAM,SAAS,EAGlF2G,EAAU,QAAQ,SAAW,EAAG,CAChC,KAAK,WAAW,OAAOpP,EAAG,CAAC,EAC3B,QACJ,CAGAoP,EAAU,OAAO,GAAK,IAAMrR,EAAY,KAGxCqR,EAAU,OAAO,GAAK,KAAK,IAAIA,EAAU,cAAgB,EAAG,EAAI,IAAMrR,EAAY,KAGlFqR,EAAU,OAAO,EAAIrG,EAAS,MAAMqG,EAAU,OAAO,EAAG,IAAK,KAAK,YAAc,GAAG,CACvF,CACJ,CAMA,2BAA2BrR,EAAW,CAGtC,CAKA,qBAAsB,CAClB,GAAI,KAAK,gBAAkB,KAAK,WAAY,CACxC,MAAMkS,EAAiB,KAAK,oBAAsB,KAAK,qBACjDC,EAAoB,KAAK,sBAAwB,KAAK,WAAW,aACjEC,EAAsBF,GAAkB,KAAK,WAAW,aACxDG,EAAkB,KAAK,cAAc,SAAW,EAElDF,IAAsBC,GAAuBC,IAC7C,KAAK,aAAY,CAEzB,CACJ,CAKA,cAAe,CACX,KAAK,eAAiB,GAEtB,QAAQ,IAAI,QAAQ,KAAK,WAAW,+BAA+B,KAAK,mBAAmB,IAAI,KAAK,WAAW,YAAY,sBAAsB,KAAK,oBAAoB,IAAI,KAAK,WAAW,YAAY,EAAE,EAG5M,MAAMjI,EAAkB,KAAK,mBAAkB,EAC/C,KAAK,YAAcA,EAGnB,KAAK,WAAa,CAAA,EAGlB,KAAK,eAAe,KAAK,YAAaA,CAAe,CACzD,CAMA,oBAAqB,CAEjB,MAAMkI,EAAiB,KAAK,YACtBC,EAAkB,KAAK,oBAAsB,KAAK,WAAW,aAEnE,OAAO,KAAK,MAAM,IAAYD,EAAiBC,CAAe,CAClE,CAOA,eAAe3H,EAAY4H,EAAO,CAE9B,QAAQ,IAAI,QAAQ5H,CAAU,yBAAyB4H,CAAK,EAAE,CAClE,CAMA,eAAe9H,EAAO,CAElB,QAAQ,IAAI,SAASA,EAAM,EAAE,UAAU,CAC3C,CAOA,OAAOhL,EAAKO,EAAgB,EAAG,CAE3B,UAAWyK,KAAS,KAAK,cACjBA,EAAM,SACNA,EAAM,OAAOhL,EAAKO,CAAa,EAQnC,OAAO,YACP,KAAK,gBAAgBP,CAAG,CAEhC,CAMA,gBAAgBA,EAAK,OACjBA,EAAI,UAAY,UAChBA,EAAI,KAAO,aACXA,EAAI,UAAY,OAEhB,MAAM+S,EAAY,CACd,SAAS,KAAK,WAAW,GACzB,mBAAmB,KAAK,cAAc,MAAM,GAC5C,YAAY,KAAK,oBAAoB,MAAI1S,EAAA,KAAK,aAAL,YAAAA,EAAiB,eAAgB,CAAC,GAC3E,WAAW,KAAK,mBAAmB,GACnC,gBAAgB,KAAK,UAAU,GAC/B,eAAe,KAAK,WAAW,MAAM,EACjD,EAEQ,QAASkC,EAAI,EAAGA,EAAIwQ,EAAU,OAAQxQ,IAClCvC,EAAI,SAAS+S,EAAUxQ,CAAC,EAAG,GAAI,GAAKA,EAAI,EAAE,EAI9CvC,EAAI,YAAc,UAClBA,EAAI,UAAY,EAChB,UAAW2R,KAAa,KAAK,WACzB3R,EAAI,UAAS,EACbA,EAAI,IAAI2R,EAAU,OAAO,EAAGA,EAAU,OAAO,EAAG,EAAG,EAAG,KAAK,GAAK,CAAC,EACjE3R,EAAI,OAAM,CAElB,CAOA,sBAAsBgT,EAAQ,CAC1B,MAAMC,EAAa,CAAA,EAEnB,GAAI,CAACD,EAAO,QAAUA,EAAO,eACzB,OAAOC,EAGX,UAAWjI,KAAS,KAAK,cACjBA,EAAM,QAAU,CAACA,EAAM,aAAeA,EAAM,aAAagI,CAAM,GAC/DC,EAAW,KAAKjI,CAAK,EAI7B,OAAOiI,CACX,CAOA,0BAA0BC,EAAa,CACnC,MAAMD,EAAa,CAAA,EAEnB,UAAW9Q,KAAc+Q,EACrB,GAAI,GAAC/Q,EAAW,QAAUA,EAAW,OAAO,iBAAiB,IAI7D,UAAW6I,KAAS,KAAK,cACrB,GAAIA,EAAM,QAAU,CAACA,EAAM,aAAeA,EAAM,aAAa7I,CAAU,EAAG,CACtE8Q,EAAW,KAAK,CACZ,WAAY9Q,EACZ,MAAO6I,EACP,OAAQ7I,EAAW,QAAU,EACrD,CAAqB,EACD,KACJ,EAIR,OAAO8Q,CACX,CAOA,2BAA2BD,EAAQhI,EAAO,CAEtC,MAAMmI,EAAeH,EAAO,WAAWhI,EAAM,gBAAe,CAAE,EAG9D,OAAAA,EAAM,WAAWA,EAAM,MAAM,EAE7B,QAAQ,IAAI,uCAAuCmI,EAAa,WAAW,0BAA0B,EAE9F,CACH,aAAcA,EAAa,YAC3B,eAAgB,GAChB,YAAanI,EAAM,UAC/B,CACI,CAQA,+BAA+B7I,EAAY6I,EAAO7H,EAAQ,CAEtD,MAAMgQ,EAAenI,EAAM,WAAW7H,CAAM,EAG5C,OAAAhB,EAAW,QAAO,EAElB,QAAQ,IAAI,0CAA0CgR,EAAa,WAAW,SAAS,EAEhF,CACH,eAAgBA,EAAa,UAC7B,YAAaA,EAAa,WAC1B,YAAaA,EAAa,WACtC,CACI,CAMA,0BAA0BnI,EAAO,CAC7B,MAAM2F,EAAgB,KAAK,oCAAoC3F,EAAM,KAAM,KAAK,kBAAkB,EAClGA,EAAM,yBAAyB,KAAK,mBAAoB2F,CAAa,CACzE,CAQA,oCAAoCQ,EAAWxI,EAAa,CACxD,MAAMyK,EAAmB,KAAK,qBAAqBzK,CAAW,EAC9D,OAAOyK,GAAoBA,EAAiBjC,CAAS,GAAK,CAC9D,CAOA,eAAexI,EAAac,EAAU,KAAM,CACxC,KAAK,mBAAqBd,EAEtBc,IACA,KAAK,qBAAqBd,CAAW,EAAIc,GAI7C,UAAWuB,KAAS,KAAK,cACrB,KAAK,0BAA0BA,CAAK,EAGxC,QAAQ,IAAI,2BAA2BrC,CAAW,EAAE,CACxD,CAMA,gCAAiC,CAC7B,MAAO,CACH,MAAO,CACH,CAACV,EAAY,GAAG,EAAG,IACnB,CAACA,EAAY,KAAK,EAAG,GACrB,CAACA,EAAY,IAAI,EAAG,EACpB,CAACA,EAAY,KAAK,EAAG,GACrB,CAACA,EAAY,OAAO,EAAG,IACvB,CAACA,EAAY,MAAM,EAAG,CACtC,EACY,WAAY,CACR,CAACA,EAAY,GAAG,EAAG,GACnB,CAACA,EAAY,KAAK,EAAG,IACrB,CAACA,EAAY,IAAI,EAAG,GACpB,CAACA,EAAY,KAAK,EAAG,GACrB,CAACA,EAAY,OAAO,EAAG,EACvB,CAACA,EAAY,MAAM,EAAG,EACtC,EACY,SAAU,CACN,CAACA,EAAY,GAAG,EAAG,GACnB,CAACA,EAAY,KAAK,EAAG,GACrB,CAACA,EAAY,IAAI,EAAG,IACpB,CAACA,EAAY,KAAK,EAAG,IACrB,CAACA,EAAY,OAAO,EAAG,GACvB,CAACA,EAAY,MAAM,EAAG,EACtC,EACY,QAAS,CACL,CAACA,EAAY,GAAG,EAAG,EACnB,CAACA,EAAY,KAAK,EAAG,GACrB,CAACA,EAAY,IAAI,EAAG,GACpB,CAACA,EAAY,KAAK,EAAG,IACrB,CAACA,EAAY,OAAO,EAAG,IACvB,CAACA,EAAY,MAAM,EAAG,GACtC,EACY,OAAQ,CACJ,CAACA,EAAY,GAAG,EAAG,GACnB,CAACA,EAAY,KAAK,EAAG,IACrB,CAACA,EAAY,IAAI,EAAG,GACpB,CAACA,EAAY,KAAK,EAAG,IACrB,CAACA,EAAY,OAAO,EAAG,GACvB,CAACA,EAAY,MAAM,EAAG,GACtC,CACA,CACI,CAOA,mBAAmB3H,EAAWuN,EAAgB,CAC1C,GAAKA,EAEL,UAAW7C,KAAS,KAAK,cACjBA,EAAM,QAAU,CAACA,EAAM,aAAeA,EAAM,gBAAgB6C,CAAc,GACvD7C,EAAM,OAAO6C,CAAc,CAY1D,CAOA,wBAAwBA,EAAgB,CACpC,OAAO,KAAK,cAAc,OAAO7C,GAC7BA,EAAM,gBAAgB6C,CAAc,CAChD,CACI,CAOA,+BAA+BmF,EAAQ,CAEnC,MAAO,CAAA,CAEX,CAQA,+BAA+B7Q,EAAY6Q,EAAQ,CAE/C,MAAO,CAAE,OAAQ,EAAG,UAAW,EAAK,CAExC,CAOA,oBAAoBnF,EAAgB,CAChC,MAAMwF,EAAU,CAAA,EACVC,EAAmB,KAAK,wBAAwBzF,CAAc,EAEpE,UAAW7C,KAASsI,EAAkB,CAClC,MAAMC,EAAavI,EAAM,OAAO6C,CAAc,EAC1C0F,GACAF,EAAQ,KAAKE,CAAU,CAE/B,CAEA,OAAOF,CACX,CAMA,eAAgB,OACZ,MAAO,CACH,YAAa,KAAK,YAClB,eAAgB,KAAK,eACrB,eAAgB,KAAK,qBACrB,cAAe,KAAK,oBACpB,eAAchT,EAAA,KAAK,aAAL,YAAAA,EAAiB,eAAgB,EAC/C,cAAe,KAAK,cAAc,OAClC,WAAY,KAAK,WACjB,aAAc,KAAK,WAAc,KAAK,qBAAuB,KAAK,WAAW,aAAgB,CACzG,CACI,CAKA,OAAQ,CAEJ,UAAW2K,KAAS,KAAK,cACrBA,EAAM,QAAO,EAEjB,KAAK,cAAgB,CAAA,EAGrB,KAAK,YAAc,EACnB,KAAK,eAAiB,GACtB,KAAK,WAAa,KAClB,KAAK,qBAAuB,EAC5B,KAAK,oBAAsB,EAC3B,KAAK,qBAAuB,EAC5B,KAAK,mBAAqB,CAAA,EAG1B,KAAK,WAAa,EAClB,KAAK,cAAgB,EAGrB,KAAK,WAAa,CAAA,EAClB,KAAK,oBAAsB,CAAA,EAG3B,KAAK,oBAAsB,EAC3B,KAAK,mBAAqB,EAC1B,KAAK,WAAa,EAGlB,KAAK,mBAAqB,QAGtB,OAAO,WAAe,KAAe,WAAW,YAChD,WAAW,UAAY,GAM3B,QAAQ,IAAI,oBAAoB,CACpC,CAOA,uBAAuBjG,EAAOC,EAAQ,CAClC,KAAK,YAAcD,EACnB,KAAK,aAAeC,EAGpB,KAAK,UAAY,KAAK,KAAKD,EAAQ,KAAK,QAAQ,EAChD,KAAK,WAAa,KAAK,KAAKC,EAAS,KAAK,QAAQ,EAMlD,UAAWgG,KAAS,KAAK,cACrBA,EAAM,YAAcjG,EACpBiG,EAAM,aAAehG,CAE7B,CAMA,eAAgB,CACZ,MAAO,CACH,YAAa,KAAK,YAClB,oBAAqB,KAAK,oBAC1B,mBAAoB,KAAK,mBACzB,WAAY,KAAK,WACjB,cAAe,KAAK,cAAc,OAClC,WAAY,KAAK,WAAW,OAC5B,UAAW,KAAK,oBAAsB,EAAK,KAAK,mBAAqB,KAAK,oBAAuB,CAC7G,CACI,CACJ,CC/sCO,MAAMwO,CAAoB,CAC7B,aAAc,CAEV,KAAK,cAAgB,EACrB,KAAK,YAAc,EACnB,KAAK,WAAa,EAGlB,KAAK,mBAAqB,CAAA,EAC1B,KAAK,eAAiB,IAGtB,KAAK,mBAAqB,CACtB,gBAAiB,EACjB,WAAY,EACZ,sBAAuB,EACvB,mBAAoB,EACpB,aAAc,EACd,gBAAiB,CAC7B,EAGQ,KAAK,kBAAoB,CACrB,KAAM,EACN,MAAO,IACP,SAAU,IACV,QAAS,EACT,WAAY,CACxB,EAGQ,KAAK,eAAiB,CAAA,EACtB,KAAK,iBAAmB,CAAA,EAGxB,KAAK,wBAA0B,KAC/B,KAAK,sBAAwB,KAC7B,KAAK,uBAAyB,KAE9B,QAAQ,IAAI,iCAAiC,CACjD,CAOA,qBAAqBvJ,EAAgB,CACjC,GAAI,CAACA,EAAe,UAChB,MAAO,CAAE,YAAa,EAAG,UAAW,CAAE,OAAQ,sBAAuB,EAGzE,MAAM9B,EAAc8B,EAAe,YAC7BG,EAAiBH,EAAe,eAChCD,EAAYC,EAAe,MAG3BmB,EAAa,KAAK,oBAAoBjD,CAAW,EAGjDoC,EAAY,KAAK,mBAAmBH,EAAgBjC,CAAW,EAG/DsL,EAAa,KAAK,oBAAoBzJ,EAAU,WAAY7B,CAAW,EAGvEuL,EAAazJ,EAAe,QAAQ,MAAQ,KAAK,oBAAoBmB,CAAU,EAAI,EACnFZ,EAAgBP,EAAe,QAAQ,SAAW,KAAK,uBAAuBmB,CAAU,EAAI,EAC5FX,EAAeR,EAAe,QAAQ,QAAU,KAAK,sBAAsBmB,CAAU,EAAI,EAGzFhD,EAAuB,KAAK,8BAA8BD,CAAW,EAGrEwL,EAAWvI,EAAab,EAAYkJ,EAAaC,EAAalJ,EAAgBC,EAG9EmJ,EAAc,KAAK,MAAMD,EAAWvL,CAAoB,EAExDyL,EAAY,CACd,WAAAzI,EACA,UAAAb,EACA,WAAAkJ,EACA,WAAAC,EACA,cAAAlJ,EACA,aAAAC,EACA,qBAAArC,EACA,SAAAuL,EACA,YAAAC,CACZ,EAEQ,eAAQ,IAAI,SAASzL,CAAW,4BAA6B0L,CAAS,EAE/D,CAAE,YAAAD,EAAa,UAAAC,CAAS,CACnC,CAOA,oBAAoB1L,EAAa,CAC7B,MAAM2L,EAAa/L,EAAY,kBACzBsD,EAAkB,KAAK,OAAOlD,EAAc,GAAK,CAAC,EAAI,EAC5D,OAAO2L,EAAazI,CACxB,CAQA,mBAAmBjB,EAAgBjC,EAAa,CAE5C,MAAM4L,EAAe,GAAM5L,EAAc,GACnC6L,EAAWD,EAAe,GAEhC,OAAI3J,GAAkB4J,EAEX,KAAK,MAAM,GAAM7L,EAAc,CAAE,EACjCiC,GAAkB2J,EAAe,IAEjC,KAAK,MAAM,GAAM5L,EAAc,CAAE,EACjCiC,GAAkB2J,EAElB,KAAK,MAAM,GAAK5L,CAAW,EAG/B,CACX,CAQA,oBAAoB8L,EAAO9L,EAAa,CAGpC,MAAM+L,EAAe,KAAK,IAAI,EAAK,EAAO/L,EAAc,GAAK,EAE7D,OAAO,KAAK,MAAM8L,EAAQ,IAAiBC,CAAY,CAC3D,CAOA,oBAAoB9I,EAAY,CAC5B,OAAO,KAAK,MAAMA,GAAc,KAAK,kBAAkB,MAAQ,EAAI,CACvE,CAOA,uBAAuBA,EAAY,CAC/B,OAAO,KAAK,MAAMA,GAAc,KAAK,kBAAkB,SAAW,EAAI,CAC1E,CAOA,sBAAsBA,EAAY,CAC9B,OAAO,KAAK,MAAMA,GAAc,KAAK,kBAAkB,QAAU,EAAI,CACzE,CAOA,8BAA8BjD,EAAa,CAMvC,MAAMgM,EAAa,EAAkB,KAAK,OAAOhM,EAAc,GAAK,EAAE,EAAI,GAC1E,OAAO,KAAK,IAAI,EAAegM,CAAU,CAC7C,CASA,YAAYC,EAAQlK,EAAQmK,EAAW,CAAA,EAAI,CACvC,GAAID,GAAU,EACV,eAAQ,KAAK,gDAAiDA,CAAM,EAC7D,CAAE,QAAS,GAAO,OAAQ,gBAAgB,EAIrD,KAAK,eAAiBA,EACtB,KAAK,aAAeA,EAGpB,MAAME,EAAc,CAChB,GAAI,KAAK,sBAAqB,EAC9B,KAAM,SACN,OAAQF,EACR,OAAQlK,EACR,UAAW,KAAK,IAAG,EACnB,aAAc,KAAK,cACnB,SAAUmK,CACtB,EAGQ,YAAK,eAAeC,CAAW,EAG/B,KAAK,yBAAyBpK,EAAQmK,CAAQ,EAG9C,KAAK,iBAAiBD,EAAQlK,CAAM,EAEpC,QAAQ,IAAI,WAAWkK,CAAM,oBAAoBlK,CAAM,kBAAkB,KAAK,aAAa,EAAE,EAG7F,KAAK,qBAAoB,EACzB,KAAK,oBAAoBkK,EAAQlK,EAAQmK,CAAQ,EAE1C,CACH,QAAS,GACT,YAAaC,EACb,WAAY,KAAK,aAC7B,CACI,CASA,YAAYF,EAAQlK,EAAQmK,EAAW,CAAA,EAAI,CACvC,GAAID,GAAU,EACV,eAAQ,KAAK,gDAAiDA,CAAM,EAC7D,CAAE,QAAS,GAAO,OAAQ,gBAAgB,EAGrD,GAAI,KAAK,cAAgBA,EACrB,eAAQ,KAAK,kCAAkCA,CAAM,gBAAgB,KAAK,aAAa,EAAE,EAClF,CAAE,QAAS,GAAO,OAAQ,uBAAwB,SAAUA,EAAQ,UAAW,KAAK,aAAa,EAI5G,KAAK,eAAiBA,EACtB,KAAK,YAAcA,EAGnB,MAAME,EAAc,CAChB,GAAI,KAAK,sBAAqB,EAC9B,KAAM,QACN,OAAQF,EACR,OAAQlK,EACR,UAAW,KAAK,IAAG,EACnB,aAAc,KAAK,cACnB,SAAUmK,CACtB,EAGQ,YAAK,eAAeC,CAAW,EAE/B,QAAQ,IAAI,SAASF,CAAM,oBAAoBlK,CAAM,kBAAkB,KAAK,aAAa,EAAE,EAG3F,KAAK,qBAAoB,EACzB,KAAK,mBAAmBoK,CAAW,EAE5B,CACH,QAAS,GACT,YAAaA,EACb,WAAY,KAAK,aAC7B,CACI,CAOA,UAAUF,EAAQ,CACd,OAAO,KAAK,eAAiBA,CACjC,CAMA,YAAa,CACT,OAAO,KAAK,aAChB,CAMA,eAAgB,CACZ,MAAMG,EAAY,KAAK,YAAc,KAAK,WACpCC,EAAyB,KAAK,mBAAmB,gBAAkB,EACnE,KAAK,YAAc,KAAK,mBAAmB,gBAC3C,EAEN,MAAO,CACH,eAAgB,KAAK,cACrB,YAAa,KAAK,YAClB,WAAY,KAAK,WACjB,UAAWD,EACX,iBAAkB,KAAK,mBAAmB,OAC1C,uBAAwB,KAAK,MAAMC,CAAsB,EACzD,mBAAoB,CAAE,GAAG,KAAK,kBAAkB,CAC5D,CACI,CAOA,sBAAsBC,EAAQ,GAAI,CAC9B,OAAO,KAAK,mBACP,MAAM,CAACA,CAAK,EACZ,SACT,CAMA,eAAeH,EAAa,CACxB,KAAK,mBAAmB,KAAKA,CAAW,EAGpC,KAAK,mBAAmB,OAAS,KAAK,gBACtC,KAAK,mBAAmB,MAAK,CAErC,CAMA,uBAAwB,CACpB,MAAO,MAAM,KAAK,IAAG,CAAE,IAAI,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAAC,EACtE,CAOA,yBAAyBpK,EAAQmK,EAAU,CACvC,GAAInK,IAAW,mBAAoB,CAO/B,GANA,KAAK,mBAAmB,kBAEpBmK,EAAS,QACT,KAAK,mBAAmB,YAAcA,EAAS,OAG/CA,EAAS,eAAgB,CAEzB,MAAMK,EAAa,KAAK,mBAAmB,sBACrCD,EAAQ,KAAK,mBAAmB,gBACtC,KAAK,mBAAmB,uBACnBC,GAAcD,EAAQ,GAAKJ,EAAS,gBAAkBI,CAC/D,CAEIJ,EAAS,UACLA,EAAS,QAAQ,SAAS,KAAK,mBAAmB,qBAClDA,EAAS,QAAQ,OAAO,KAAK,mBAAmB,eAChDA,EAAS,QAAQ,UAAU,KAAK,mBAAmB,kBAE/D,CACJ,CAOA,iBAAiBD,EAAQlK,EAAQ,CAC7B,MAAMyK,EAAS,CACX,GAAI,KAAK,sBAAqB,EAC9B,OAAQP,EACR,OAAQlK,EACR,UAAW,KAAK,IAAG,EACnB,UAAW,EACvB,EAEQ,KAAK,eAAe,KAAKyK,CAAM,CACnC,CAMA,mBAAoB,CAChB,OAAO,KAAK,eAAe,OAAOA,GAAU,CAACA,EAAO,SAAS,CACjE,CAMA,oBAAoBC,EAAU,CAC1B,MAAMD,EAAS,KAAK,eAAe,KAAK7S,GAAKA,EAAE,KAAO8S,CAAQ,EAC1DD,IACAA,EAAO,UAAY,IAIvB,MAAME,EAAa,KAAK,IAAG,EAAK,IAChC,KAAK,eAAiB,KAAK,eAAe,OACtCF,GAAU,CAACA,EAAO,WAAaA,EAAO,UAAYE,CAC9D,CACI,CAMA,uBAAuBvU,EAAW,CAE9B,QAAS,EAAI,KAAK,iBAAiB,OAAS,EAAG,GAAK,EAAG,IAAK,CACxD,MAAMwU,EAAY,KAAK,iBAAiB,CAAC,EACzCA,EAAU,SAAWxU,EAGjBwU,EAAU,SAAWA,EAAU,UAC/B,KAAK,iBAAiB,OAAO,EAAG,CAAC,CAEzC,CAGA,MAAMC,EAAiB,KAAK,kBAAiB,EAC7C,UAAWJ,KAAUI,EACjB,KAAK,iBAAiB,KAAK,CACvB,GAAIJ,EAAO,GACX,OAAQA,EAAO,OACf,OAAQA,EAAO,OACf,UAAW,KAAK,IAAG,EACnB,QAAS,EACT,SAAU,IACV,OAAQ,IACR,KAAM,GACN,MAAO,CACvB,CAAa,EAED,KAAK,oBAAoBA,EAAO,EAAE,CAE1C,CAOA,OAAO3U,EAAKM,EAAW,CAEnB,KAAK,uBAAuBA,CAAS,EAGrC,KAAK,mBAAmBN,CAAG,EAG3B,KAAK,uBAAuBA,CAAG,CACnC,CAMA,mBAAmBA,EAAK,CACpB,MAAM5B,EAAI4B,EAAI,OAAO,MAAQ,GACvB3B,EAAI,GAGV2B,EAAI,UAAY,qBAChBA,EAAI,SAAS5B,EAAI,IAAKC,EAAI,GAAI,IAAK,EAAE,EAGrC2B,EAAI,YAAc,UAClBA,EAAI,UAAY,EAChBA,EAAI,WAAW5B,EAAI,IAAKC,EAAI,GAAI,IAAK,EAAE,EAGvC2B,EAAI,UAAY,UAChBA,EAAI,KAAO,aACXA,EAAI,UAAY,OAChBA,EAAI,SAAS,IAAK5B,EAAI,IAAKC,EAAI,CAAC,EAGhC2B,EAAI,UAAY,UAChBA,EAAI,KAAO,aACXA,EAAI,UAAY,QAChBA,EAAI,SAAS,GAAG,KAAK,cAAc,gBAAgB,QAAS5B,EAAI,GAAIC,EAAI,CAAC,CAC7E,CAMA,uBAAuB2B,EAAK,CACxB,UAAW8U,KAAa,KAAK,iBAAkB,CAC3C,MAAM1E,EAAW0E,EAAU,QAAUA,EAAU,SACzCzW,EAAIyW,EAAU,QAAUA,EAAU,KAAOA,EAAU,QAAU1E,EAC7DhP,EAAQ,KAAK,IAAI,EAAG,EAAMgP,CAAQ,EAGxCpQ,EAAI,KAAI,EACRA,EAAI,YAAcoB,EAClBpB,EAAI,UAAY,UAChBA,EAAI,KAAO,kBACXA,EAAI,UAAY,SAChBA,EAAI,SAAS,IAAI8U,EAAU,MAAM,QAAS9U,EAAI,OAAO,MAAQ,EAAG3B,CAAC,EAGjE2B,EAAI,UAAY,UAChBA,EAAI,KAAO,aACXA,EAAI,SAAS,KAAK,mBAAmB8U,EAAU,MAAM,EAAG9U,EAAI,OAAO,MAAQ,EAAG3B,EAAI,EAAE,EACpF2B,EAAI,QAAO,CACf,CACJ,CAOA,mBAAmBkK,EAAQ,CAUvB,MATkB,CACd,iBAAoB,iBACpB,YAAe,cACf,eAAkB,iBAClB,cAAiB,cACjB,aAAgB,iBAChB,gBAAmB,eAC/B,EAEyBA,CAAM,GAAKA,EAAO,QAAQ,KAAM,GAAG,EAAE,YAAW,CACrE,CAKA,OAAQ,CACJ,KAAK,cAAgB,EACrB,KAAK,YAAc,EACnB,KAAK,WAAa,EAClB,KAAK,mBAAqB,CAAA,EAC1B,KAAK,mBAAqB,CACtB,gBAAiB,EACjB,WAAY,EACZ,sBAAuB,EACvB,mBAAoB,EACpB,aAAc,EACd,gBAAiB,CAC7B,EACQ,KAAK,eAAiB,CAAA,EACtB,KAAK,iBAAmB,CAAA,EAExB,QAAQ,IAAI,2BAA2B,CAC3C,CAKA,sBAAuB,CACf,KAAK,yBACL,KAAK,wBAAwB,KAAK,cAAe,KAAK,cAAa,CAAE,CAE7E,CAMA,mBAAmBoK,EAAa,CACxB,KAAK,uBACL,KAAK,sBAAsBA,CAAW,CAE9C,CAQA,oBAAoBF,EAAQlK,EAAQmK,EAAU,CACtC,KAAK,wBACL,KAAK,uBAAuBD,EAAQlK,EAAQmK,CAAQ,CAE5D,CAMA,mBAAmBnN,EAAU,CACzB,KAAK,wBAA0BA,CACnC,CAMA,iBAAiBA,EAAU,CACvB,KAAK,sBAAwBA,CACjC,CAMA,kBAAkBA,EAAU,CACxB,KAAK,uBAAyBA,CAClC,CACJ,CCrnBO,MAAM8N,EAAN,MAAMA,CAAQ,CACjB,YAAY9T,EAAM+T,EAAMC,EAAW,KAAMC,EAAc,GAAI,CACvD,KAAK,KAAOjU,EACZ,KAAK,KAAO+T,EACZ,KAAK,SAAWC,EAChB,KAAK,YAAcC,EACnB,KAAK,SAAW,GAChB,KAAK,cAAgBD,EACrB,KAAK,UAAY,KACjB,KAAK,GAAKF,EAAQ,WAAU,EAG5B,KAAK,KAAO,KACZ,KAAK,MAAQ,UACb,KAAK,UAAY,SACrB,CAIA,OAAO,YAAa,CAChB,MAAO,WAAW,EAAEA,EAAQ,SAAS,EACzC,CAOA,MAAMI,EAAY,CACd,OAAI,KAAK,UACL,QAAQ,KAAK,WAAW,KAAK,IAAI,oBAAoB,EAC9C,KAGX,KAAK,SAAW,GAChB,KAAK,UAAY,KAAK,IAAG,EACzB,KAAK,cAAgB,KAAK,SAE1B,QAAQ,IAAI,qBAAqB,KAAK,IAAI,EAAE,EACrC,KAAK,YAAYA,CAAU,EACtC,CAOA,OAAOA,EAAY,CACf,OAAK,KAAK,UAKV,KAAK,SAAW,GAChB,KAAK,cAAgB,EAErB,QAAQ,IAAI,qBAAqB,KAAK,IAAI,EAAE,EACrC,KAAK,aAAaA,CAAU,IAR/B,QAAQ,KAAK,WAAW,KAAK,IAAI,gBAAgB,EAC1C,GAQf,CAQA,OAAO9U,EAAW8U,EAAY,CAC1B,OAAK,KAAK,SAGN,KAAK,WAAa,MAAQ,KAAK,cAAgB,IAC/C,KAAK,eAAiB9U,EAGlB,KAAK,eAAiB,IACtB,KAAK,OAAO8U,CAAU,EACf,IAIR,GAboB,EAc/B,CAMA,4BAA6B,CACzB,OAAI,KAAK,WAAa,KAAa,EAC9B,KAAK,SACH,KAAK,IAAI,EAAG,KAAK,cAAgB,KAAK,QAAQ,EAD1B,CAE/B,CAMA,2BAA4B,CACxB,OAAI,KAAK,WAAa,KAAa,YAC9B,KAAK,SAGH,GADS,KAAK,KAAK,KAAK,cAAgB,GAAI,CAClC,IAHU,UAI/B,CAQA,YAAYA,EAAYC,EAAc,CAClC,MAAO,CACH,YAAaA,GAAgB,KAAK,MAAQ,CAAC,KAAK,SAChD,OAAQA,EAAe,KAAK,KAAO,sBAC5B,KAAK,SAAW,iBAAmB,WACtD,CACI,CAOA,YAAYD,EAAY,CAEpB,MAAO,EACX,CAOA,aAAaA,EAAY,CAErB,MAAO,EACX,CAMA,gBAAiB,CACb,MAAO,CACH,GAAI,KAAK,GACT,KAAM,KAAK,KACX,KAAM,KAAK,KACX,SAAU,KAAK,SACf,YAAa,KAAK,YAClB,SAAU,KAAK,SACf,cAAe,KAAK,cACpB,wBAAyB,KAAK,2BAA0B,EACxD,uBAAwB,KAAK,0BAAyB,EACtD,KAAM,KAAK,KACX,MAAO,KAAK,MACZ,UAAW,KAAK,SAC5B,CACI,CACJ,EA7IIrU,EAlBSiU,EAkBF,YAAY,GAlBhB,IAAMM,EAANN,EAoKA,MAAMO,UAAyBD,CAAQ,CAC1C,aAAc,CACV,MACI,aACAvN,EAAY,eAAe,WAC3B,KACA,6CACZ,EAEQ,KAAK,KAAO,KACZ,KAAK,MAAQ,UACb,KAAK,UAAY,SACrB,CAEA,YAAYqN,EAAY,CACpB,OAAAA,EAAW,SAAS,CAAC,EACd,EACX,CAEA,YAAYA,EAAYC,EAAc,CAElC,MAAO,CACH,YAAaA,GAAgB,KAAK,KAClC,OAAQA,EAAe,KAAK,KAAO,sBAAwB,WACvE,CACI,CACJ,CAKO,MAAMG,UAA0BF,CAAQ,CAC3C,aAAc,CACV,MACI,cACAvN,EAAY,eAAe,YAC3B,IACA,0DACZ,EAEQ,KAAK,KAAO,KACZ,KAAK,MAAQ,UACb,KAAK,UAAY,SACrB,CAEA,YAAYqN,EAAY,CACpB,OAAIA,EAAW,cACXA,EAAW,aAAa,oBAAoB,EAAI,EACzC,IAEJ,EACX,CAEA,aAAaA,EAAY,CACrB,OAAIA,EAAW,cACXA,EAAW,aAAa,oBAAoB,EAAK,EAC1C,IAEJ,EACX,CACJ,CAKO,MAAMK,UAA4BH,CAAQ,CAC7C,aAAc,CACV,MACI,gBACAvN,EAAY,eAAe,cAC3B,KACA,gDACZ,EAEQ,KAAK,KAAO,KACZ,KAAK,MAAQ,UACb,KAAK,UAAY,UACjB,KAAK,YAAc,IACvB,CAEA,YAAYqN,EAAY,CAGpB,eAAQ,IAAI,mDAAmD,EACxD,EACX,CAEA,aAAaA,EAAY,CAErB,OAAI,KAAK,cACL,KAAK,YAAY,QAAO,EACxB,KAAK,YAAc,MAEvB,QAAQ,IAAI,mDAAmD,EACxD,EACX,CACJ,CAKO,MAAMM,CAAe,CACxB,OAAO,cAAcxU,EAAM,CACvB,OAAQA,EAAI,CACR,IAAK,aACD,OAAO,IAAIqU,EACf,IAAK,cACD,OAAO,IAAIC,EACf,IAAK,gBACD,OAAO,IAAIC,EACf,QACI,MAAM,IAAI,MAAM,0BAA0BvU,CAAI,EAAE,CAChE,CACI,CAEA,OAAO,oBAAqB,CACxB,MAAO,CAAC,aAAc,cAAe,eAAe,CACxD,CAEA,OAAO,mBAAoB,CACvB,OAAO,KAAK,mBAAkB,EAAG,IAAIA,GAAQ,KAAK,cAAcA,CAAI,CAAC,CACzE,CACJ,CC9RO,MAAMyU,CAAe,CACxB,YAAYC,EAAcC,EAAY,CAClC,KAAK,aAAeD,EACpB,KAAK,WAAaC,EAGlB,KAAK,UAAY,GACjB,KAAK,cAAgB,GACrB,KAAK,UAAY,KAGjB,KAAK,kBAAoBH,EAAe,kBAAiB,EACzD,KAAK,eAAiB,IAAI,IAG1B,KAAK,mBAAqB,KAC1B,KAAK,gBAAkB,KACvB,KAAK,QAAU,KAGf,KAAK,eAAiB,KACtB,KAAK,cAAgB,EAErB,QAAQ,IAAI,wBAAwB,CACxC,CAKA,MAAM,YAAa,CACf,GAAI,MAAK,cAET,GAAI,CAEA,KAAK,UAAY,SAAS,cAAc,KAAK,EAC7C,KAAK,UAAU,GAAK,kBACpB,KAAK,UAAU,UAAY,yBAG3B,SAAS,KAAK,YAAY,KAAK,SAAS,EAGxC,KAAK,oBAAmB,EAExB,KAAK,cAAgB,GACrB,QAAQ,IAAI,yCAAyC,CAEzD,OAASI,EAAO,CACZ,cAAQ,MAAM,uCAAwCA,CAAK,EACrDA,CACV,CACJ,CAKA,MAAO,CACH,GAAI,CAAC,KAAK,cAAe,CACrB,QAAQ,MAAM,gCAAgC,EAC9C,MACJ,CAEA,KAAK,UAAY,GACjB,KAAK,0BAAyB,EAC9B,KAAK,OAAM,EACX,KAAK,UAAU,UAAU,OAAO,QAAQ,EACxC,KAAK,UAAU,UAAU,IAAI,SAAS,EAGtC,KAAK,mBAAkB,EAEvB,QAAQ,IAAI,sBAAsB,CACtC,CAKA,MAAO,CACE,KAAK,YAEV,KAAK,UAAY,GACjB,KAAK,UAAU,UAAU,OAAO,SAAS,EACzC,KAAK,UAAU,UAAU,IAAI,QAAQ,EAGrC,KAAK,kBAAiB,EAEtB,QAAQ,IAAI,uBAAuB,EACvC,CAKA,2BAA4B,CACxB,MAAMT,EAAe,KAAK,aAAa,WAAU,EAC3CD,EAAa,KAAK,WAAW,WAEnC,KAAK,kBAAkB,QAAQW,GAAW,CACtC,MAAMC,EAAeD,EAAQ,YAAYX,EAAYC,CAAY,EACjEU,EAAQ,aAAeC,CAC3B,CAAC,CACL,CAKA,QAAS,CACL,GAAI,CAAC,KAAK,UAAW,OAErB,MAAMX,EAAe,KAAK,aAAa,WAAU,EAEjD,KAAK,UAAU,UAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yDAasBA,CAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCASnC,KAAK,eAAc,CAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UA2B/C,KAAK,0BAAyB,CAClC,CAKA,gBAAiB,CACb,OAAO,KAAK,kBAAkB,IAAIU,GAAW,CACzC,MAAMC,EAAeD,EAAQ,cAAgB,CAAE,YAAa,GAAO,OAAQ,SAAS,EAC9EE,EAAW,KAAK,eAAe,IAAIF,EAAQ,IAAI,EAC/CG,EAAcF,EAAa,aAAe,CAACC,EAEjD,IAAIE,EAAc,GACdC,EAAa,GACbC,EAAa,aAAaN,EAAQ,IAAI,MAE1C,OAAIE,GACAE,EAAc,SACdC,EAAa,SACbC,EAAa,kBACLL,EAAa,cACrBG,EAAc,cACVH,EAAa,SAAW,uBACxBI,EAAa,sBACbC,EAAa,QAAQN,EAAQ,KAAO,KAAK,aAAa,WAAU,CAAE,YAElEK,EAAa,cACbC,EAAa,oBAId;AAAA,4CACyBF,CAAW,oBAAoBJ,EAAQ,IAAI;AAAA,iDACtCA,EAAQ,IAAI;AAAA,gDACb,KAAK,kBAAkBA,EAAQ,IAAI,CAAC;AAAA,sDAC9BA,EAAQ,WAAW;AAAA;AAAA;AAAA,wDAGjBA,EAAQ,IAAI;AAAA;AAAA;AAAA;AAAA,8BAItCA,EAAQ,SAAW,GAAG,KAAK,KAAKA,EAAQ,SAAW,GAAI,CAAC,IAAM,WAAW;AAAA;AAAA;AAAA,mDAGpDK,CAAU;AAAA,qDACRF,EAAc,UAAY,UAAU;AAAA,6CAC5CH,EAAQ,IAAI;AAAA,8BAC1BG,EAA2B,GAAb,UAAe;AAAA,0BAClCG,CAAU;AAAA;AAAA;AAAA,aAI5B,CAAC,EAAE,KAAK,EAAE,CACd,CAKA,kBAAkBnV,EAAM,CACpB,OAAQA,EAAI,CACR,IAAK,aACD,MAAO,aACX,IAAK,cACD,MAAO,cACX,IAAK,gBACD,MAAO,gBACX,QACI,OAAOA,EAAK,QAAQ,KAAM,GAAG,EAAE,YAAW,EACrC,QAAQ,QAASoV,GAAKA,EAAE,YAAW,CAAE,CAC1D,CACI,CAKA,qBAAsB,CAElB,SAAS,iBAAiB,UAAY,GAAM,CACpC,EAAE,MAAQ,UAAY,KAAK,WAC3B,KAAK,MAAK,CAElB,CAAC,CACL,CAKA,2BAA4B,CAExB,MAAMC,EAAW,KAAK,UAAU,cAAc,kBAAkB,EAC5DA,GACAA,EAAS,iBAAiB,QAAS,IAAM,KAAK,MAAK,CAAE,EAIzD,MAAMC,EAAW,KAAK,UAAU,cAAc,iBAAiB,EAC3DA,GACAA,EAAS,iBAAiB,QAAS,IAAM,KAAK,MAAK,CAAE,EAIlC,KAAK,UAAU,iBAAiB,iCAAiC,EACzE,QAAQpX,GAAU,CAC7BA,EAAO,iBAAiB,QAAUqX,GAAM,CACpC,MAAMC,EAAcD,EAAE,OAAO,QAAQ,QACrC,KAAK,sBAAsBC,CAAW,CAC1C,CAAC,CACL,CAAC,CACL,CAKA,sBAAsBA,EAAa,CAC/B,MAAMX,EAAU,KAAK,kBAAkB,KAAKY,GAAKA,EAAE,OAASD,CAAW,EACvE,GAAI,CAACX,EAAS,CACV,QAAQ,MAAM,sBAAuBW,CAAW,EAChD,MACJ,CAGA,MAAMrB,EAAe,KAAK,aAAa,WAAU,EAC3CD,EAAa,KAAK,WAAW,WAC7BY,EAAeD,EAAQ,YAAYX,EAAYC,CAAY,EAEjE,GAAI,CAACW,EAAa,YAAa,CAC3B,QAAQ,KAAK,4BAA6BA,EAAa,MAAM,EAC7D,MACJ,CAGA,MAAMY,EAAc,KAAK,aAAa,YAAYb,EAAQ,KAAM,YAAYW,EAAY,YAAW,CAAE,EAAE,EACvG,GAAI,CAACE,EAAY,QAAS,CACtB,QAAQ,MAAM,0BAA2BA,EAAY,MAAM,EAC3D,MACJ,CAIA,GAAI,CADgBb,EAAQ,MAAMX,CAAU,EAC1B,CACd,QAAQ,MAAM,0BAA0B,EAExC,KAAK,aAAa,YAAYW,EAAQ,KAAM,iBAAiB,EAC7D,MACJ,CAGA,KAAK,eAAe,IAAIW,EAAaX,CAAO,EAG5C,KAAK,0BAAyB,EAC9B,KAAK,OAAM,EAGP,KAAK,oBACL,KAAK,mBAAmBA,EAASa,CAAW,EAGhD,QAAQ,IAAI,uBAAuBF,CAAW,QAAQX,EAAQ,IAAI,SAAS,CAC/E,CAKA,OAAQ,CACJ,KAAK,KAAI,EAEL,KAAK,SACL,KAAK,QAAO,CAEpB,CAKA,oBAAqB,CACjB,GAAI,KAAK,eAAgB,OAEzB,MAAMc,EAAU,IAAM,CAClB,KAAK,eAAiB,IAGtB,MAAMC,EAAO,KAAK,UAAU,cAAc,aAAa,EACjDC,EAAY,KAAK,UAAU,cAAc,aAAa,EAE5D,GAAID,EAAM,CACN,MAAME,EAAO,KAAK,IAAI,KAAK,aAAa,EAAI,GAAM,GAClDF,EAAK,MAAM,OAAS,mBAAmB,GAAKE,EAAO,EAAE,aACzD,CAEA,GAAID,EAAW,CACX,MAAMC,EAAO,KAAK,IAAI,KAAK,cAAgB,CAAC,EAAI,GAAM,GACtDD,EAAU,MAAM,OAAS,mBAAmB,EAAIC,EAAO,CAAC,aAC5D,CAEI,KAAK,YACL,KAAK,eAAiB,sBAAsBH,CAAO,EAE3D,EAEAA,EAAO,CACX,CAKA,mBAAoB,CACZ,KAAK,iBACL,qBAAqB,KAAK,cAAc,EACxC,KAAK,eAAiB,KAE9B,CAKA,qBAAqBvW,EAAW,CAC5B,MAAM8U,EAAa,KAAK,WAAW,WAEnC,SAAW,CAAClU,EAAM6U,CAAO,IAAK,KAAK,eACXA,EAAQ,OAAOzV,EAAW8U,CAAU,IAGpD,KAAK,eAAe,OAAOlU,CAAI,EAC/B,QAAQ,IAAI,qBAAqBA,CAAI,EAAE,EAGnD,CAKA,sBAAsBgG,EAAU,CAC5B,KAAK,mBAAqBA,CAC9B,CAEA,mBAAmBA,EAAU,CACzB,KAAK,gBAAkBA,CAC3B,CAEA,WAAWA,EAAU,CACjB,KAAK,QAAUA,CACnB,CAKA,SAAU,CACN,KAAK,kBAAiB,EAElB,KAAK,WAAa,KAAK,UAAU,YACjC,KAAK,UAAU,WAAW,YAAY,KAAK,SAAS,EAGxD,KAAK,UAAY,KACjB,KAAK,cAAgB,GAErB,QAAQ,IAAI,0BAA0B,CAC1C,CACJ,CC1aA,IAAIuP,EAAE,CAAC,iBAAiB,mBAAmB,oBAAoB,sBAAsB,iBAAiB,mBAAmB,oBAAoB,sBAAsB,sBAAsB,wBAAwB,oBAAoB,sBAAsB,aAAa,eAAe,qBAAqB,uBAAuB,uBAAuB,yBAAyB,sBAAsB,uBAAuB,EAAE7K,EAAE,UAAU,CAAC,SAASA,GAAG,CAAC,KAAK,oBAAoB,CAAA,CAAE,CAAC,OAAOA,EAAE,UAAU,gBAAgB,UAAU,CAAC,GAAG,CAAC,GAAG,OAAO,KAAK,OAAO,MAAM,OAAO,OAAO,OAAO,GAAG,OAAO,EAAE,CAAC,QAAQ,KAAK,4BAA4B,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,QAAQ,OAAO,SAAS,OAAO,OAAO,OAAO,MAAM,OAAO,EAAE,CAAC,QAAQ,KAAK,+BAA+B,EAAE,OAAO,CAAC,CAAC,OAAO,IAAI,EAAEA,EAAE,YAAY,UAAU,CAAC,OAAOA,EAAE,WAAWA,EAAE,SAAS,IAAIA,GAAGA,EAAE,QAAQ,EAAEA,EAAE,UAAU,+BAA+B,SAAS,EAAE,EAAE,CAAC,KAAK,oBAAoB,SAAS,CAAC,IAAI,OAAO,iBAAiB,UAAU,CAAC,EAAE,KAAK,oBAAoB,KAAK,CAAC,EAAE,EAAEA,EAAE,UAAU,iBAAiB,SAAS,EAAE,EAAE,CAAI,KAAK,kBAAmB,KAAK,+BAA+B,EAAE,SAASxF,EAAE,CAACA,EAAE,KAAK,aAAa,GAAG,EAAC,CAAE,CAAC,EAAO,QAAQ,MAAM,kDAAkD,CAAC,EAAEwF,EAAE,UAAU,YAAY,SAASA,EAAExF,EAAE,CAAC,IAAI6Q,EAAE,KAAK,kBAAkB,GAAGA,EAAE,CAAC,IAAI,EAAE,WAAW,UAAU,CAAC7Q,EAAEwF,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,+BAA+B6K,EAAE,iBAAiB,SAAS7K,EAAE,CAACA,EAAE,KAAK,aAAa6K,EAAE,mBAAmB,aAAa,CAAC,EAAErQ,EAAEwF,EAAE,KAAK,QAAQ,QAAQ,EAAE,CAAC,EAAEqL,EAAE,YAAY,CAAC,WAAWR,EAAE,iBAAiB,QAAQ,CAAC,YAAY7K,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,QAAQ,MAAM,kDAAkD,CAAC,EAAEA,EAAE,UAAU,aAAa,SAASA,EAAE,CAAC,IAAIxF,EAAE,KAAK,kBAAkBA,EAAEA,EAAE,YAAY,CAAC,WAAWqQ,EAAE,oBAAoB,QAAQ,CAAC,KAAK7K,CAAC,CAAC,EAAE,GAAG,EAAE,QAAQ,MAAM,kDAAkD,CAAC,EAAEA,EAAE,UAAU,SAAS,SAASA,EAAE,CAAC,IAAIxF,EAAE,KAAK,gBAAe,EAAGA,GAAG,QAAQ,IAAI,6BAA6BwF,CAAC,EAAExF,EAAE,YAAY,CAAC,WAAWqQ,EAAE,aAAa,QAAQ,CAAC,MAAM7K,CAAC,CAAC,EAAE,GAAG,GAAG,QAAQ,MAAM,kDAAkD,CAAC,EAAEA,EAAE,UAAU,WAAW,UAAU,CAAC,IAAIA,EAAE,KAAK,gBAAe,EAAGA,EAAEA,EAAE,YAAY,CAAC,WAAW6K,EAAE,mBAAmB,EAAE,GAAG,EAAE,QAAQ,MAAM,kDAAkD,CAAC,EAAE7K,EAAE,UAAU,YAAY,UAAU,CAAC,IAAIA,EAAE,KAAK,gBAAe,EAAGA,EAAEA,EAAE,YAAY,CAAC,WAAW6K,EAAE,oBAAoB,EAAE,GAAG,EAAE,QAAQ,MAAM,kDAAkD,CAAC,EAAE7K,EAAE,UAAU,WAAW,UAAU,CAAC,IAAIA,EAAE,KAAK,gBAAe,EAAGA,EAAEA,EAAE,YAAY,CAAC,WAAW6K,EAAE,qBAAqB,EAAE,GAAG,EAAE,QAAQ,MAAM,kDAAkD,CAAC,EAAE7K,EAAE,UAAU,aAAa,SAASA,EAAE,CAAC,KAAK,iBAAiB6K,EAAE,sBAAsB7K,CAAC,CAAC,EAAEA,EAAE,UAAU,cAAc,SAASA,EAAE,CAAC,KAAK,iBAAiB6K,EAAE,uBAAuB7K,CAAC,CAAC,EAAEA,EAAE,UAAU,WAAW,SAASA,EAAE,CAAC,KAAK,iBAAiB6K,EAAE,oBAAoB7K,CAAC,CAAC,EAAEA,CAAC,EAAC,EAAGxF,EAAEwF,EAAE,YAAW,EAAgB,OAAO,OAApB,MAA6B,OAAO,MAAMxF,GAAG,IAAI6Q,EAAE7Q,EAAE,YAAY8Q,EAAE9Q,EAAE,aAAa7D,EAAE6D,EAAE,SAASuG,EAAEvG,EAAE,WAAWtE,GAAEsE,EAAE,YAAY+Q,GAAE/Q,EAAE,WAAWgR,GAAEhR,EAAE,aAAaiR,GAAEjR,EAAE,cAAckR,GAAElR,EAAE,WCOjjG,MAAMmR,EAAiB,CAC1B,aAAc,CAEV,KAAK,WAAa,GAClB,KAAK,cAAgB,GAGrB,KAAK,WAAa,CAEd,WAAY,EACZ,aAAc,EACd,iBAAkB,EAClB,gBAAiB,EACjB,kBAAmB,EAGnB,gBAAiB,CAAA,EACjB,gBAAiB,CAAA,EACjB,cAAe,EAGf,mBAAoB,EACpB,qBAAsB,EACtB,sBAAuB,EAGvB,gBAAiB,CACb,YAAa,EACb,cAAe,KACf,iBAAkB,EAClB,sBAAuB,GACvB,mBAAoB,CAAA,CACpC,EAGY,eAAgB,CACZ,UAAW,KAAK,IAAG,EACnB,wBAAyB,EACzB,wBAAyB,EACzB,iBAAkB,CAClC,EAGY,aAAc,KAAK,IAAG,EACtB,YAAa,QACb,cAAe,CAC3B,EAGQ,KAAK,eAAiB,GACtB,KAAK,gBAAkB,KACvB,KAAK,eAAiB,EACtB,KAAK,WAAa,EAClB,KAAK,WAAa,IAGlB,KAAK,oBAAsB,KAC3B,KAAK,oBAAsB,KAC3B,KAAK,wBAA0B,KAG/B,KAAK,gBAAkB,GACvB,KAAK,iBAAmB,IACxB,KAAK,aAAe,KAAK,IAAG,EAE5B,QAAQ,IAAI,8BAA8B,CAC9C,CAKA,MAAM,YAAa,CACf,GAAI,MAAK,cAET,GAAI,CAEA,KAAK,WAAW,eAAe,UAAY,KAAK,IAAG,EACnD,KAAK,WAAW,gBAGhB,KAAK,uBAAsB,EAG3B,MAAM,KAAK,qBAAoB,EAG/BC,GAAgB,EAEhB,KAAK,WAAa,GAClB,KAAK,cAAgB,GAErB,QAAQ,IAAI,2CAA2C,EAGvD,MAAM,KAAK,eAAe,YAAY,CAE1C,OAAS1B,EAAO,CACZ,QAAQ,MAAM,yCAA0CA,CAAK,EAC7D,KAAK,WAAa,EACtB,CACJ,CAKA,wBAAyB,CAErB2B,GAAmB,IAAM,CACrB,QAAQ,IAAI,8BAA8B,EAC1C,KAAK,gBAAe,CACxB,CAAC,EAGDC,GAAoB,IAAM,CACtB,QAAQ,IAAI,+BAA+B,EAC3C,KAAK,iBAAgB,CACzB,CAAC,EAGDC,GAAiB,IAAM,CACnB,QAAQ,IAAI,sCAAsC,EAClD,KAAK,eAAc,CACvB,CAAC,CACL,CAKA,MAAM,sBAAuB,CACzB,GAAI,CAEA,MAAMC,EAAc,CAAE,gBAAiB,KAAK,WAAW,eAAe,EAGtEC,EAAkBD,EAAcE,GAAS,CACrC,GAAIA,GAAQA,EAAK,gBAAiB,CAC9B,MAAMC,EAAgBD,EAAK,gBACrBE,EAAQ,IAAI,KAAI,EAAG,aAAY,EAC/BC,EAAYF,EAAc,cAGhC,GAAIE,EAAW,CACX,MAAMC,EAAgB,IAAI,KAAKD,CAAS,EAClCE,EAAW,KAAK,OAAO,KAAK,IAAG,EAAKD,EAAc,QAAO,IAAO,IAAO,GAAK,GAAK,GAAG,EAEtFC,IAAa,EAEb,KAAK,WAAW,gBAAgB,aAAeJ,EAAc,aAAe,GAAK,EAC1EI,EAAW,EAElB,KAAK,WAAW,gBAAgB,YAAc,EAG9C,KAAK,WAAW,gBAAgB,YAAcJ,EAAc,aAAe,CAEnF,MAEI,KAAK,WAAW,gBAAgB,YAAc,EAIlD,KAAK,WAAW,gBAAgB,cAAgBC,EAGhD,KAAK,WAAW,gBAAgB,iBAAmBD,EAAc,kBAAoB,EACrF,KAAK,WAAW,gBAAgB,sBAAwBA,EAAc,uBAAyB,GAC/F,KAAK,WAAW,gBAAgB,mBAAqBA,EAAc,oBAAsB,CAAA,EAGzF,KAAK,4BAA2B,CACpC,CACJ,CAAC,CAEL,OAASjC,EAAO,CACZ,QAAQ,MAAM,mCAAoCA,CAAK,CAC3D,CACJ,CAKA,6BAA8B,CAC1B,MAAMsC,EAAS,KAAK,WAAW,gBAAgB,YAG/C,GAAIA,GAAU,GAAKA,GAAU,EAAG,CAC5B,MAAMC,EAAa,KAAK,IAAID,EAAS,EAAG,CAAC,EACzC,KAAK,WAAW,gBAAgB,iBAAmBC,EAEnD,QAAQ,IAAI,uBAAuBA,CAAU,oBAAoBD,CAAM,aAAa,EAGhF,KAAK,yBACL,KAAK,wBAAwB,cAAeC,CAAU,CAE9D,CAGI,KAAK,WAAW,gBAAgB,wBAChC,QAAQ,IAAI,sCAAsC,EAC9C,KAAK,yBACL,KAAK,wBAAwB,yBAA0B,EAAI,EAGvE,CAMA,qBAAqBC,EAAc,CAC/B,GAAI,CAACA,EAAc,OAwBnB,GArBIA,EAAa,QAAU,SACvB,KAAK,WAAW,WAAa,KAAK,IAAI,KAAK,WAAW,WAAYA,EAAa,KAAK,EACpF,KAAK,WAAW,eAAe,kBAAoBA,EAAa,aAAe,GAG/EA,EAAa,QAAU,SACvB,KAAK,WAAW,aAAe,KAAK,IAAI,KAAK,WAAW,aAAcA,EAAa,KAAK,GAGxFA,EAAa,eAAiB,SAC9B,KAAK,WAAW,kBAAoBA,EAAa,aACjD,KAAK,WAAW,mBAAqBA,EAAa,aAClD,KAAK,WAAW,eAAe,yBAA2BA,EAAa,cAGvEA,EAAa,cAAgB,SAC7B,KAAK,WAAW,iBAAmBA,EAAa,YAChD,KAAK,WAAW,mBAAqBA,EAAa,aAIlDA,EAAa,iBAAmB,OAAW,CAC3C,MAAMC,EAAQD,EAAa,eAO3B,GANK,KAAK,WAAW,gBAAgB,SAASC,CAAK,IAC/C,KAAK,WAAW,gBAAgB,KAAKA,CAAK,EAC1C,KAAK,WAAW,eAAe,2BAI/BD,EAAa,aAAe,OAAW,CACvC,MAAME,EAAc,KAAK,WAAW,gBAAgBD,CAAK,GAAK,EAC9D,KAAK,WAAW,gBAAgBA,CAAK,EAAI,KAAK,IAAIC,EAAaF,EAAa,UAAU,CAC1F,CACJ,CAWA,GARIA,EAAa,mBACb,KAAK,WAAW,qBAGhBA,EAAa,kBAAoB,SACjC,KAAK,WAAW,sBAAwBA,EAAa,iBAGrDA,EAAa,iBAAmB,OAAW,CAE3C,MAAMG,EAAc,KAAK,WAAW,gBAAgB,OAChDA,EAAc,IACd,KAAK,WAAW,uBACV,KAAK,WAAW,uBAAyBA,EAAc,GAAMH,EAAa,gBAAkBG,EAE1G,CAGA,MAAMC,EAAc,KAAK,IAAG,EACtBC,EAAcD,EAAc,KAAK,WAAW,eAAe,UACjE,KAAK,WAAW,eAAiBC,EACjC,KAAK,WAAW,eAAe,UAAYD,EAE3C,QAAQ,IAAI,2BAA4BJ,CAAY,CACxD,CAOA,MAAM,eAAepO,EAAS,SAAUrJ,EAAQ,GAAO,CACnD,GAAI,CAAC,KAAK,WACN,eAAQ,KAAK,iCAAiC,EACvC,GAGX,GAAI,KAAK,gBAAkB,CAACA,EACxB,eAAQ,IAAI,wCAAwC,EACpD,KAAK,gBAAkB,CAAE,GAAG,KAAK,UAAU,EACpC,GAGX,KAAK,eAAiB,GACtB,KAAK,WAAW,aAAe,KAAK,IAAG,EAEvC,MAAM+X,EAAa,CACf,GAAG,KAAK,WACR,WAAY1O,EACZ,cAAe,KAAK,IAAG,CACnC,EAEQ,GAAI,CAWA,GAVA,MAAM,KAAK,qBAAqB0O,CAAU,EAE1C,QAAQ,IAAI,mCAAmC1O,CAAM,GAAG,EAGpD,KAAK,qBACL,KAAK,oBAAoB0O,EAAY1O,CAAM,EAI3C,KAAK,gBAAiB,CACtB,MAAM2O,EAAc,KAAK,gBACzB,KAAK,gBAAkB,KACvB,WAAW,IAAM,KAAK,eAAe,gBAAgB,EAAG,GAAG,CAC/D,CAEA,MAAO,EAEX,OAAS/C,EAAO,CACZ,eAAQ,MAAM,8BAA+BA,CAAK,EAG9C,KAAK,qBACL,KAAK,oBAAoBA,EAAO5L,CAAM,EAGnC,EACX,QAAC,CACG,KAAK,eAAiB,GACtB,KAAK,eAAiB,CAC1B,CACJ,CAMA,MAAM,qBAAqB0O,EAAY,CACnC,QAASE,EAAU,EAAGA,GAAW,KAAK,WAAYA,IAC9C,GAAI,CAEA,MAAM,IAAI,QAAQ,CAACC,EAASC,IAAW,CACnC,GAAI,CACAC,EAAmBL,CAAU,EAE7B,WAAWG,EAAS,GAAG,CAC3B,OAASjD,EAAO,CACZkD,EAAOlD,CAAK,CAChB,CACJ,CAAC,EAED,MAEJ,OAASA,EAAO,CAGZ,GAFA,KAAK,eAAiBgD,EAAU,EAE5BA,EAAU,KAAK,WACf,QAAQ,KAAK,gBAAgBA,EAAU,CAAC,wBAAwB,KAAK,UAAU,MAAOhD,CAAK,EAC3F,MAAM,IAAI,QAAQiD,GAAW,WAAWA,EAAS,KAAK,UAAU,CAAC,EACjE,KAAK,YAAc,MAEnB,OAAMjD,CAEd,CAER,CAMA,MAAM,iBAAiB7L,EAAgB,aACnC,GAAI,CAACA,EAAgB,OAGrB,KAAK,qBAAqB,CACtB,MAAOA,EAAe,YACtB,eAAgBA,EAAe,YAC/B,aAAY5J,EAAA4J,EAAe,QAAf,YAAA5J,EAAsB,aAAc,EAChD,cAAayQ,EAAA7G,EAAe,QAAf,YAAA6G,EAAsB,aAAc,EACjD,eAAcoI,EAAAjP,EAAe,QAAf,YAAAiP,EAAsB,cAAe,EACnD,kBAAmBjP,EAAe,kBAClC,gBAAiBA,EAAe,gBAChC,eAAgBA,EAAe,cAC3C,CAAS,EAGD,MAAMkP,IAAaC,EAAAnP,EAAe,QAAf,YAAAmP,EAAsB,aAAc,EACvDC,EAAeF,CAAU,EAGzB,MAAM,KAAK,eAAe,iBAAiB,EAE3C,QAAQ,IAAI,SAASlP,EAAe,WAAW,iCAAiC,CACpF,CAMA,MAAM,gBAAgBqP,EAAW,CAC7B,GAAI,CAACA,EAAW,OAGhB,KAAK,qBAAqB,CACtB,aAAcA,EAAU,QAAU,EAClC,YAAaA,EAAU,OAAS,CAC5C,CAAS,GAG0BA,EAAU,QAAU,IAAMA,EAAU,OAAS,IAAM,IAE1E,MAAM,KAAK,eAAe,cAAc,CAEhD,CAKA,iBAAkB,CAEdC,EAAgB,EAGhB,KAAK,eAAe,aAAa,CACrC,CAKA,kBAAmB,CAEfC,GAAiB,EAGjB,KAAK,WAAW,eAAe,UAAY,KAAK,IAAG,CACvD,CAKA,MAAM,gBAAiB,CAEnB,MAAMb,EAAc,KAAK,IAAG,EAAK,KAAK,WAAW,eAAe,UAChE,KAAK,WAAW,eAAiBA,EAGjCU,EAAe,KAAK,WAAW,UAAU,EAGzC,MAAM,KAAK,eAAe,WAAW,EAErC,QAAQ,IAAI,4CAA4C,CAC5D,CAKA,gBAAiB,CACb,GAAI,CAAC,KAAK,iBAAmB,CAAC,KAAK,WAAY,OAE/C,MAAMI,EAAM,KAAK,IAAG,EAChBA,EAAM,KAAK,cAAgB,KAAK,mBAChC,KAAK,eAAe,WAAW,EAC/B,KAAK,aAAeA,EAE5B,CAMA,eAAgB,CACZ,MAAO,CAAE,GAAG,KAAK,UAAU,CAC/B,CAMA,oBAAqB,CACjB,MAAO,CAAE,GAAG,KAAK,WAAW,eAAe,CAC/C,CAMA,kBAAkBC,EAAe,CACxB,KAAK,WAAW,gBAAgB,mBAAmB,SAASA,CAAa,IAC1E,KAAK,WAAW,gBAAgB,mBAAmB,KAAKA,CAAa,EACrE,KAAK,eAAe,oBAAoB,EAExC,QAAQ,IAAI,yBAAyBA,CAAa,EAAE,EAEhD,KAAK,yBACL,KAAK,wBAAwB,qBAAsBA,CAAa,EAG5E,CAMA,yBAAyBC,EAAe,CACpC,KAAK,WAAW,gBAAgB,sBAAwBA,EACxD,KAAK,eAAe,mBAAmB,EAEnC,KAAK,yBACL,KAAK,wBAAwB,yBAA0BA,CAAa,CAE5E,CAMA,uBAAuBzS,EAAU,CAC7B,KAAK,oBAAsBA,CAC/B,CAMA,uBAAuBA,EAAU,CAC7B,KAAK,oBAAsBA,CAC/B,CAMA,2BAA2BA,EAAU,CACjC,KAAK,wBAA0BA,CACnC,CAOA,YAAY7D,EAASuW,EAAW,IAAO,CACnC,KAAK,gBAAkBvW,EACvB,KAAK,iBAAmBuW,EAEpBvW,IACA,KAAK,aAAe,KAAK,IAAG,GAGhC,QAAQ,IAAI,aAAaA,EAAU,UAAY,UAAU,SAASuW,CAAQ,aAAa,CAC3F,CAMA,mBAAoB,CAChB,MAAO,CACH,WAAY,KAAK,WACjB,eAAgB,KAAK,eACrB,eAAgB,KAAK,eACrB,aAAc,KAAK,WAAW,aAC9B,gBAAiB,KAAK,gBACtB,iBAAkB,KAAK,iBACvB,aAAc,KAAK,YAC/B,CACI,CAKA,OAAQ,CACJ,KAAK,WAAa,CACd,WAAY,EACZ,aAAc,EACd,iBAAkB,EAClB,gBAAiB,EACjB,kBAAmB,EACnB,gBAAiB,CAAA,EACjB,gBAAiB,CAAA,EACjB,cAAe,EACf,mBAAoB,EACpB,qBAAsB,EACtB,sBAAuB,EACvB,gBAAiB,CACb,YAAa,EACb,cAAe,KACf,iBAAkB,EAClB,sBAAuB,GACvB,mBAAoB,CAAA,CACpC,EACY,eAAgB,CACZ,UAAW,KAAK,IAAG,EACnB,wBAAyB,EACzB,wBAAyB,EACzB,iBAAkB,CAClC,EACY,aAAc,KAAK,IAAG,EACtB,YAAa,QACb,cAAe,CAC3B,EAEQ,KAAK,eAAiB,GACtB,KAAK,gBAAkB,KACvB,KAAK,eAAiB,EACtB,KAAK,WAAa,IAElB,QAAQ,IAAI,wBAAwB,CACxC,CACJ,CChmBO,MAAMC,EAAW,CACpB,YAAY5a,EAAQ6a,EAAW,CAC3B,KAAK,OAAS7a,EACd,KAAK,IAAMA,EAAO,WAAW,IAAI,EACjC,KAAK,UAAY6a,EACjB,KAAK,MAAQ,CAAA,EAGb,KAAK,UAAY,GACjB,KAAK,SAAW,GAGhB,KAAK,UAAY,GACjB,KAAK,cAAgB,IAAO,KAAK,UACjC,KAAK,aAAe,IAGpB,KAAK,cAAgB,EACrB,KAAK,YAAc,EACnB,KAAK,YAAc,EAGnB,KAAK,WAAa,EAClB,KAAK,SAAW,EAChB,KAAK,WAAa,EAGlB,KAAK,SAAW,KAAK,SAAS,KAAK,IAAI,EACvC,KAAK,aAAe,KAAK,aAAa,KAAK,IAAI,CACnD,CAEA,MAAM,MAAO,CACT,eAAQ,IAAI,uCAAuC,EAGnD,KAAK,YAAW,EAGhB,MAAM,KAAK,kBAAiB,EAG5B,KAAK,MAAK,EAEH,QAAQ,QAAO,CAC1B,CAEA,aAAc,CAEV,KAAK,IAAI,sBAAwB,GAGjC,KAAK,aAAY,EACjB,OAAO,iBAAiB,SAAU,KAAK,YAAY,EAEnD,QAAQ,IAAI,uBAAuB,KAAK,OAAO,KAAK,IAAI,KAAK,OAAO,MAAM,EAAE,CAChF,CAEA,qBAAsB,CAElB,QAASvX,EAAI,EAAGA,EAAI,IAAWA,IAC3B,KAAK,MAAM,KAAK,CACZ,EAAG,KAAK,OAAM,EAAK,KAAK,OAAO,MAC/B,EAAG,KAAK,OAAM,EAAK,KAAK,OAAO,OAC/B,KAAM,KAAK,OAAM,EAAK,IAAM,GAC5B,MAAQ,KAAK,OAAM,EAAK,GAAM,CAC9C,CAAa,CAET,CAEA,MAAM,mBAAoB,CAEtB,KAAK,aAAe,IAAIvD,EAAa,KAAK,MAAM,EAGhD,KAAK,kBAAoB,IAAI2H,EAG7B,MAAMvB,EAAS,KAAK,OAAO,MAAQ,EAC7BC,EAAS,KAAK,OAAO,OAAS,IACpC,KAAK,WAAa,IAAI9B,EAAW6B,EAAQC,EAAQ,KAAK,OAAO,MAAO,KAAK,OAAO,OAAQ,KAAK,iBAAiB,EAG9G,KAAK,aAAe,IAAI6C,EAAa,KAAK,iBAAiB,EAG3D,KAAK,aAAe,IAAI2I,EAAa,KAAK,OAAO,MAAO,KAAK,OAAO,OAAQ,KAAK,iBAAiB,EAGlG,KAAK,aAAe,IAAI2C,EAGxB,KAAK,iBAAmB,IAAI+D,GAG5B,KAAK,eAAiB,IAAI5B,EAAe,KAAK,aAAc,IAAI,EAGhE,KAAK,2BAA0B,EAG/B,KAAK,2BAA0B,EAG/B,KAAK,wBAAuB,EAG5B,KAAK,2BAA0B,EAG/B,MAAM,KAAK,eAAe,WAAU,EACpC,KAAK,6BAA4B,EAGjC,KAAK,oBAAmB,EAGxB,KAAK,oBAAmB,EAExB,QAAQ,IAAI,0BAA0B,CAG1C,CAEA,OAAQ,CACC,KAAK,YACN,KAAK,UAAY,GACjB,KAAK,SAAW,GAChB,KAAK,YAAc,YAAY,IAAG,EAClC,KAAK,cAAgB,KAAK,YAC1B,KAAK,YAAc,EACnB,KAAK,WAAa,EAClB,KAAK,SAAW,EAChB,sBAAsB,KAAK,QAAQ,EACnC,QAAQ,IAAI,uCAAuC,EAE3D,CAMA,UAAUoE,EAAO,KAAM,CACnB,QAAQ,IAAI,8BAA+BA,CAAI,EAG3C,KAAK,cACL,KAAK,aAAa,WAAW,CAAC,EAGlC,QAAQ,IAAI,oCAAoC,CACpD,CAEA,OAAQ,CACJ,KAAK,SAAW,GAChB,QAAQ,IAAI,aAAa,EAGrB,KAAK,kBACL,KAAK,iBAAiB,gBAAe,CAE7C,CAEA,QAAS,CACD,KAAK,WACL,KAAK,SAAW,GAEhB,KAAK,YAAc,YAAY,IAAG,EAClC,KAAK,cAAgB,KAAK,YAC1B,KAAK,YAAc,EACnB,QAAQ,IAAI,cAAc,EAGtB,KAAK,kBACL,KAAK,iBAAiB,iBAAgB,EAGlD,CAEA,MAAM,SAAU,CACZ,KAAK,UAAY,GAGb,KAAK,kBACL,MAAM,KAAK,iBAAiB,eAAc,EAI1C,KAAK,cACL,KAAK,aAAa,QAAO,EAG7B,QAAQ,IAAI,uBAAuB,CACvC,CAEA,SAASrB,EAAa,CAClB,GAAI,CAAC,KAAK,UAAW,OAGrB,IAAI/J,EAAY+J,EAAc,KAAK,cAOnC,GANI/J,EAAY,KAAK,eACjBA,EAAY,KAAK,cAGrB,KAAK,cAAgB+J,EAEjB,CAAC,KAAK,SAAU,CAKhB,IAHA,KAAK,aAAe/J,EAGb,KAAK,aAAe,KAAK,eAC5B,KAAK,OAAO,KAAK,aAAa,EAC9B,KAAK,aAAe,KAAK,cAI7B,MAAMpO,EAAgB,KAAK,YAAc,KAAK,cAG9C,KAAK,OAAOA,CAAa,EAGzB,KAAK,iBAAiBoO,CAAS,CACnC,CAEA,sBAAsB,KAAK,QAAQ,CACvC,CAEA,OAAOrO,EAAW,CASd,GARA,KAAK,gBAAgBA,CAAS,EAG1B,KAAK,cACL,KAAK,aAAa,OAAM,EAIxB,KAAK,YAAc,KAAK,aAAc,CACtC,MAAMoD,EAAgB,KAAK,aAAa,kBAAiB,EACzD,KAAK,WAAW,OAAOpD,EAAWoD,CAAa,EAG3C,KAAK,aAAa,aAAa,MAAM,GACrC,KAAK,WAAW,KAAI,EAIpB,KAAK,aAAa,aAAa,MAAM,GACrC,KAAK,WAAW,WAAW,EAAE,EAI7B,KAAK,aAAa,aAAa,MAAM,GACrC,KAAK,WAAW,KAAK,EAAE,EAIvB,KAAK,aAAa,aAAa,MAAM,GACrC,KAAK,WAAW,SAAS,CAAC,EAI1B,KAAK,aAAa,aAAa,MAAM,GACrC,KAAK,aAAa,YAAY,IAAK,cAAc,EAIjD,KAAK,aAAa,aAAa,MAAM,GACrC,KAAK,mBAAmB,CAAE,YAAa,EAAG,UAAW,EAAG,CAEhE,CAQA,GALI,KAAK,gBACL,KAAK,eAAe,qBAAqBpD,CAAS,EAIlD,KAAK,aAAc,CACnB,MAAMoJ,EAAY,CACd,gBAAiB,KAAK,WAAa,KAAK,WAAW,gBAAe,EAAG,YAAc,GACnF,kBAAmB,GACnB,WAAY,EACZ,SAAU,CAC1B,EACY,KAAK,aAAa,OAAOpJ,EAAWoJ,CAAS,CACjD,CAGA,GAAI,KAAK,cAAgB,KAAK,YAAc,KAAK,cAAgB,KAAK,aAAa,gBAAiB,CAChG,MAAMmE,EAAiB,KAAK,WAAW,SACvC,KAAK,aAAa,OAAOvN,EAAWuN,CAAc,EAGlD,KAAK,iBAAgB,CACzB,CAGI,KAAK,mBACL,KAAK,kBAAkB,OAAOvN,CAAS,EAI3C,KAAK,mBAAkB,EAGnB,KAAK,kBACL,KAAK,iBAAiB,eAAc,CAE5C,CAKA,oBAAqB,CACjB,GAAI,CAAC,KAAK,kBAAmB,OAE7B,MAAMQ,EAAS,CACX,KAAM,IACN,MAAO,KAAK,OAAO,MAAQ,GAC3B,IAAK,IACL,OAAQ,KAAK,OAAO,OAAS,EACzC,EAEcoS,EAAc,KAAK,kBAAkB,UAAU,YAAY,EACjE,UAAW/Q,KAAc+Q,EACjB/Q,EAAW,cAAcrB,CAAM,GAC/B,KAAK,kBAAkB,aAAa,aAAcqB,CAAU,CAGxE,CAEA,gBAAgB7B,EAAW,CACvB,MAAM0Z,EAAY1Z,EAAY,IAC9B,UAAW2Z,KAAQ,KAAK,MACpBA,EAAK,GAAKA,EAAK,MAAQD,EACnBC,EAAK,EAAI,KAAK,OAAO,SACrBA,EAAK,EAAI,EACTA,EAAK,EAAI,KAAK,OAAM,EAAK,KAAK,OAAO,MAGjD,CAEA,OAAO1Z,EAAgB,EAAG,CAEtB,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAAS,EAAG,EAAG,KAAK,OAAO,MAAO,KAAK,OAAO,MAAM,EAG7D,KAAK,gBAAe,EAGhB,KAAK,YACL,KAAK,WAAW,OAAO,KAAK,IAAKA,CAAa,EAI9C,KAAK,cACL,KAAK,aAAa,OAAO,KAAK,IAAKA,CAAa,EAIhD,KAAK,mBACL,KAAK,kBAAkB,OAAO,KAAK,IAAKA,CAAa,EAIzD,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,SACrB,KAAK,IAAI,SAAS,YAAa,KAAK,OAAO,MAAQ,EAAG,EAAE,EAGpD,KAAK,eACL,KAAK,iBAAgB,EAGrB,KAAK,aAAa,OAAO,KAAK,GAAG,GAIrC,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,OACrB,KAAK,IAAI,SAAS,QAAQ,KAAK,UAAU,GAAI,GAAI,EAAE,EAGnD,KAAK,uBAAsB,EAG3B,KAAK,sBAAqB,EAGtB,KAAK,cACL,KAAK,aAAa,OAAO,KAAK,IAAK,KAAK,aAAa,CAE7D,CAEA,kBAAmB,CACf,GAAI,CAAC,KAAK,aAAc,OAGxB,MAAMb,EAAW,KAAK,aAAa,kBAAiB,EACpD,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,OACrB,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAAS,aAAaA,EAAS,EAAE,QAAQ,CAAC,CAAC,KAAKA,EAAS,EAAE,QAAQ,CAAC,CAAC,GAAI,GAAI,EAAE,EAGxF,MAAMwa,EAAU,CAAC,OAAQ,QAAS,UAAU,EAC5C,IAAIC,EAAU,GAEd,UAAW3a,KAAU0a,EACb,KAAK,aAAa,aAAa1a,CAAM,GACrC,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAAS,GAAGA,EAAO,YAAW,CAAE,WAAY,GAAI2a,CAAO,IAEhE,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAAS,GAAG3a,CAAM,aAAc,GAAI2a,CAAO,GAExDA,GAAW,GAaf,GATA,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAAS,UAAU,KAAK,aAAa,cAAc,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,aAAa,cAAc,EAAE,QAAQ,CAAC,CAAC,GAAI,GAAIA,EAAU,EAAE,EAGzI,KAAK,aAAa,eAClB,KAAK,IAAI,SAAS,iBAAiB,KAAK,aAAa,QAAQ,IAAI,WAAY,GAAIA,EAAU,EAAE,EAI7F,KAAK,kBAAmB,CACxB,MAAMC,EAAQ,KAAK,kBAAkB,SAAQ,EAC7C,KAAK,IAAI,SAAS,YAAYA,EAAM,YAAY,KAAKA,EAAM,aAAa,WAAY,GAAID,EAAU,EAAE,EAEpG,MAAMjH,EAAc,KAAK,kBAAkB,UAAU,YAAY,EACjE,KAAK,IAAI,SAAS,gBAAgBA,EAAY,MAAM,GAAI,GAAIiH,EAAU,EAAE,CAC5E,CACJ,CAEA,iBAAiBxL,EAAW,CACxB,KAAK,aACL,KAAK,UAAYA,EAGb,KAAK,UAAY,MACjB,KAAK,WAAa,KAAK,MAAO,KAAK,WAAa,IAAQ,KAAK,QAAQ,EACrE,KAAK,WAAa,EAClB,KAAK,SAAW,EAExB,CAEA,iBAAkB,CAEd,KAAK,IAAI,UAAY,UACrB,UAAWsL,KAAQ,KAAK,MACpB,KAAK,IAAI,SAASA,EAAK,EAAGA,EAAK,EAAGA,EAAK,KAAMA,EAAK,IAAI,CAE9D,CAKA,wBAAyB,CACrB,GAAI,CAAC,KAAK,WAAY,OAEtB,MAAMI,EAAe,KAAK,WAAW,gBAAe,EAG9CC,EAAa,KAAK,OAAO,MAAQ,IACjCC,EAAa,GACbC,EAAiB,IACjBC,EAAkB,GAGxB,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAASH,EAAYC,EAAYC,EAAgBC,CAAe,EAGzE,KAAK,IAAI,YAAc,UACvB,KAAK,IAAI,UAAY,EACrB,KAAK,IAAI,WAAWH,EAAYC,EAAYC,EAAgBC,CAAe,EAG3E,MAAMC,EAAcF,EAAiBH,EAAa,iBAClD,IAAIM,EAAc,UAEdN,EAAa,iBAAmB,GAChCM,EAAc,UACPN,EAAa,iBAAmB,KACvCM,EAAc,WAGlB,KAAK,IAAI,UAAYA,EACrB,KAAK,IAAI,SAASL,EAAa,EAAGC,EAAa,EAAGG,EAAc,EAAGD,EAAkB,CAAC,EAGtF,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,SACrB,KAAK,IAAI,SACL,GAAGJ,EAAa,MAAM,IAAIA,EAAa,SAAS,GAChDC,EAAaE,EAAiB,EAC9BD,EAAaE,EAAkB,EAAI,CAC/C,EAGQ,MAAMG,EAASL,EAAaE,EAAkB,GAC9C,KAAK,IAAI,UAAY,QACrB,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,SAAS,UAAUJ,EAAa,KAAK,GAAI,KAAK,OAAO,MAAQ,GAAIO,CAAM,EAGhF,MAAMC,EAAe,GACfC,EAAkB,GAClBC,EAAiB,KAAK,OAAO,MAAQ,GAAMV,EAAa,MAAQS,EAEtE,QAASvY,EAAI,EAAGA,EAAI8X,EAAa,MAAO9X,IAAK,CACzC,MAAMyY,EAAQD,EAAkBxY,EAAIuY,EAC9BG,EAAQL,EAAS,GAGvB,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,UAAS,EAClB,KAAK,IAAI,OAAOI,EAAOC,EAAQJ,EAAe,CAAC,EAC/C,KAAK,IAAI,OAAOG,EAAQH,EAAe,EAAGI,EAAQJ,EAAe,CAAC,EAClE,KAAK,IAAI,OAAOG,EAAQH,EAAe,EAAGI,EAAQJ,EAAe,CAAC,EAClE,KAAK,IAAI,UAAS,EAClB,KAAK,IAAI,KAAI,CACjB,CAGA,GAAIR,EAAa,eAAgB,CAC7B,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,QACrB,MAAMa,GAAcb,EAAa,6BAA+B,KAAM,QAAQ,CAAC,EAC/E,KAAK,IAAI,SAAS,iBAAiBa,CAAU,IAAK,KAAK,OAAO,MAAQ,GAAIN,EAAS,EAAE,CACzF,CAGIP,EAAa,cACb,KAAK,IAAI,UAAY,qBACrB,KAAK,IAAI,SAAS,EAAG,EAAG,KAAK,OAAO,MAAO,KAAK,OAAO,MAAM,EAE7D,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,SACrB,KAAK,IAAI,SAAS,YAAa,KAAK,OAAO,MAAQ,EAAG,KAAK,OAAO,OAAS,CAAC,EAE5E,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,SAAS,qBAAsB,KAAK,OAAO,MAAQ,EAAG,KAAK,OAAO,OAAS,EAAI,EAAE,EAElG,CAKA,uBAAwB,CACpB,GAAI,CAAC,KAAK,aAAc,OAExB,MAAMc,EAAc,KAAK,aAAa,eAAc,EAcpD,GAXA,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,OACrB,KAAK,IAAI,SAAS,UAAUA,EAAY,YAAY,GAAI,GAAI,GAAG,EAG/D,KAAK,IAAI,SAAS,UAAUA,EAAY,MAAM,QAAQ,eAAc,CAAE,GAAI,GAAI,GAAG,EACjF,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,SAAS,gBAAgBA,EAAY,MAAM,MAAM,eAAc,CAAE,GAAI,GAAI,GAAG,EAGjFA,EAAY,gBAAiB,CAC7B,MAAMC,EAAe,YAAYD,EAAY,SAAS,eAAe,IAAIA,EAAY,SAAS,eAAe,GAC7G,KAAK,IAAI,SAASC,EAAc,GAAI,GAAG,EAEvC,MAAMC,EAAW,UAAUF,EAAY,SAAS,cAAc,IAAIA,EAAY,SAAS,aAAa,GACpG,KAAK,IAAI,SAASE,EAAU,GAAI,GAAG,EAGnC,MAAMC,GAAeH,EAAY,eAAiB,KAAM,QAAQ,CAAC,EAIjE,GAHA,KAAK,IAAI,SAAS,SAASG,CAAW,IAAK,GAAI,GAAG,EAG9CH,EAAY,aAAeA,EAAY,YAAY,UAAW,CAC9D,MAAMI,EAAgBJ,EAAY,YAAY,UAAaA,EAAY,eAAiB,IACpFI,GAAiB,IAAMA,EAAgB,GACvC,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAAS,mBAAmBA,EAAc,QAAQ,CAAC,CAAC,IAAK,GAAI,GAAG,GAClEA,GAAiB,IAAMA,EAAgB,IAC9C,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,SAAS,kBAAkBA,EAAc,QAAQ,CAAC,CAAC,IAAK,GAAI,GAAG,EAEhF,CACJ,CAUA,GAPIJ,EAAY,YAAY,oBACxB,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,SAAS,cAAe,GAAI,GAAG,GAIxCA,EAAY,aAAeA,EAAY,YAAY,YAAa,CAChE,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,MAAMK,EAAU,gBAAgBL,EAAY,YAAY,YAAY,YAAW,CAAE,GACjF,KAAK,IAAI,SAASK,EAAS,GAAI,GAAG,CACtC,CAGI,CAACL,EAAY,iBAAmBA,EAAY,aAC5C,KAAK,6BAA6BA,CAAW,CAErD,CAMA,6BAA6BA,EAAa,CAEtC,KAAK,IAAI,UAAY,qBACrB,KAAK,IAAI,SAAS,EAAG,EAAG,KAAK,OAAO,MAAO,KAAK,OAAO,MAAM,EAG7D,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,UAAY,SACrB,KAAK,IAAI,SAAS,SAASA,EAAY,YAAY,aAAc,KAAK,OAAO,MAAQ,EAAG,KAAK,OAAO,OAAS,EAAI,EAAE,EAGnH,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,SAAS,gBAAgBA,EAAY,MAAM,MAAM,eAAc,CAAE,GAAI,KAAK,OAAO,MAAQ,EAAG,KAAK,OAAO,OAAS,CAAC,EAE3H,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,SAAS,qBAAqBA,EAAY,SAAS,eAAe,GAAI,KAAK,OAAO,MAAQ,EAAG,KAAK,OAAO,OAAS,EAAI,EAAE,EAEjI,MAAMM,EAAW,qBAAqBN,EAAY,eAAiB,KAAM,QAAQ,CAAC,CAAC,IACnF,KAAK,IAAI,SAASM,EAAU,KAAK,OAAO,MAAQ,EAAG,KAAK,OAAO,OAAS,EAAI,EAAE,EAG9E,KAAK,IAAI,UAAY,UACrB,KAAK,IAAI,KAAO,aAChB,KAAK,IAAI,SAAS,0BAA2B,KAAK,OAAO,MAAQ,EAAG,KAAK,OAAO,OAAS,EAAI,EAAE,CACnG,CAKA,4BAA6B,CAEzB,KAAK,aAAa,gBAAgB,CAACtT,EAAauT,IAAgB,CAC5D,QAAQ,IAAI,SAASvT,CAAW,YAAauT,CAAW,EAGpD,KAAK,eACL,KAAK,aAAa,MAAK,EACvB,QAAQ,IAAI,mCAAmC,EAEvD,CAAC,EAGD,KAAK,aAAa,mBAAoBzR,GAAmB,CAGrD,GAFA,QAAQ,IAAI,mBAAoBA,CAAc,EAE1CA,EAAe,UAAW,CAM1B,GAJA,QAAQ,IAAI,SAASA,EAAe,WAAW,iBAAiBA,EAAe,eAAe,QAAQ,CAAC,CAAC,GAAG,EAC3G,QAAQ,IAAI,UAAUA,EAAe,MAAM,UAAU,cAAcA,EAAe,eAAe,EAAE,EAG/F,KAAK,aAAc,CACnB,MAAM0R,EAAc,KAAK,aAAa,qBAAqB1R,CAAc,EACrE0R,EAAY,YAAc,IACN,KAAK,aAAa,YAClCA,EAAY,YACZ,mBACA,CACI,YAAa1R,EAAe,YAC5B,eAAgBA,EAAe,eAC/B,MAAOA,EAAe,MAAM,WAC5B,QAASA,EAAe,QACxB,UAAW0R,EAAY,SACvD,CACA,EAEwB,QAAQ,IAAI,WAAWA,EAAY,WAAW,mCAAmC,EACjF,QAAQ,IAAI,0BAA2BA,EAAY,SAAS,EAG5D1R,EAAe,MAAM,YAAc0R,EAAY,YAEvD,CAGI,KAAK,kBACL,KAAK,iBAAiB,iBAAiB1R,CAAc,EAIzD,KAAK,mBAAmBA,CAAc,CAC1C,MAEI,QAAQ,IAAI,SAASA,EAAe,WAAW,YAAYA,EAAe,MAAM,EAAE,EAGlF,WAAW,IAAM,CACTA,EAAe,UACf,KAAK,aAAa,WAAWA,EAAe,WAAW,CAE/D,EAAG,GAAI,CAEf,CAAC,EAGD,KAAK,aAAa,iBAAkBD,GAAc,CAGlD,CAAC,CACL,CAKA,4BAA6B,CAEzB,KAAK,aAAa,eAAiB,CAACkB,EAAY4H,IAAU,CACtD,QAAQ,IAAI,QAAQ5H,CAAU,0BAA0B4H,CAAK,EAAE,EAG3D,KAAK,cACL,KAAK,aAAa,qBAAqB5H,EAAY4H,CAAK,CAEhE,EAGA,KAAK,aAAa,eAAkB9H,GAAU,CAC1C,QAAQ,IAAI,SAASA,EAAM,EAAE,qBAAqB,EAG9C,KAAK,cACL,KAAK,aAAa,kBAAkBA,EAAO,CAAC,CAEpD,CACJ,CAKA,kBAAmB,CACf,GAAI,CAAC,KAAK,cAAgB,CAAC,KAAK,WAAY,OAG5C,MAAM4Q,EAAmB,KAAK,aAAa,sBAAsB,KAAK,UAAU,EAChF,UAAW5Q,KAAS4Q,EAAkB,CAClC,MAAM3W,EAAS,KAAK,aAAa,2BAA2B,KAAK,WAAY+F,CAAK,EAClF,QAAQ,IAAI,0BAA2B/F,CAAM,CACjD,CAGA,MAAMiO,EAAc,KAAK,kBAAkB,UAAU,YAAY,EAC3D2I,EAAuB,KAAK,aAAa,0BAA0B3I,CAAW,EAEpF,UAAW4I,KAAaD,EAAsB,CAC1C,MAAM5W,EAAS,KAAK,aAAa,+BAC7B6W,EAAU,WACVA,EAAU,MACVA,EAAU,MAC1B,EAGgB7W,EAAO,gBAAkB,KAAK,cAC9B,KAAK,aAAa,kBAAkB6W,EAAU,MAAO7W,EAAO,WAAW,CAE/E,CACJ,CAKA,4BAA6B,CAEzB,KAAK,aAAa,mBAAmB,CAAC8W,EAASC,IAAe,CAC1D,QAAQ,IAAI,0BAA0BD,CAAO,cAAc,EAGvD,KAAK,kBACL,KAAK,iBAAiB,gBAAgB,CAClC,QAASA,EACT,WAAYC,CAChC,CAAiB,CAET,CAAC,EAGD,KAAK,aAAa,iBAAkB1H,GAAgB,CAIhD,GAHA,QAAQ,IAAI,qBAAsBA,CAAW,EAGzC,KAAK,kBAAoBA,EAAY,QAAU,GAAI,CACnD,MAAMgF,EAAYhF,EAAY,OAAS,SACjC,CAAE,OAAQA,EAAY,MAAM,EAC5B,CAAE,MAAOA,EAAY,MAAM,EAEjC,KAAK,iBAAiB,gBAAgBgF,CAAS,CACnD,CACJ,CAAC,EAGD,KAAK,aAAa,kBAAkB,CAAClF,EAAQlK,EAAQmK,IAAa,CAC9D,QAAQ,IAAI,wBAAwBD,CAAM,QAAQlK,CAAM,EAAE,CAC9D,CAAC,CACL,CAKA,8BAA+B,CAE3B,KAAK,eAAe,sBAAsB,CAAC6L,EAASzB,IAAgB,CAChE,QAAQ,IAAI,uBAAuByB,EAAQ,IAAI,QAAQA,EAAQ,IAAI,SAAS,CAIhF,CAAC,EAGD,KAAK,eAAe,mBAAmB,CAACkG,EAAY3H,IAAgB,CAChE,QAAQ,IAAI,2BAA2B2H,EAAW,IAAI,QAAQA,EAAW,IAAI,SAAS,CAC1F,CAAC,EAGD,KAAK,eAAe,WAAW,IAAM,CACjC,QAAQ,IAAI,uCAAuC,EACnD,KAAK,oBAAmB,CAC5B,CAAC,CACL,CAKA,mBAAmBhS,EAAgB,CAC/B,QAAQ,IAAI,8CAA8C,EAG1D,KAAK,mBAAqBA,EAG1B,KAAK,MAAK,EAGV,KAAK,eAAe,KAAI,CAC5B,CAKA,qBAAsB,CAClB,GAAI,KAAK,mBAAoB,CACzB,MAAMA,EAAiB,KAAK,mBAC5B,KAAK,mBAAqB,KAGtBA,EAAe,WAAa,KAAK,aAAa,WAC9C,QAAQ,IAAI,kBAAkBA,EAAe,SAAS,EAAE,EACxD,KAAK,aAAa,WAAWA,EAAe,SAAS,GAErD,QAAQ,IAAI,sCAAsC,CAG1D,CACJ,CAKA,MAAM,qBAAsB,CACxB,GAAI,CACA,MAAM,KAAK,iBAAiB,WAAU,EACtC,QAAQ,IAAI,qCAAqC,CACrD,OAAS6L,EAAO,CACZ,QAAQ,MAAM,mCAAoCA,CAAK,CAC3D,CACJ,CAKA,yBAA0B,CAEtB,KAAK,iBAAiB,2BAA2B,CAACoG,EAAY3Q,IAAU,CACpE,KAAK,oBAAoB2Q,EAAY3Q,CAAK,CAC9C,CAAC,EAGD,KAAK,iBAAiB,uBAAuB,CAACuM,EAAM5N,IAAW,CAC3D,QAAQ,IAAI,kCAAkCA,CAAM,GAAG,CAC3D,CAAC,EAGD,KAAK,iBAAiB,uBAAuB,CAAC4L,EAAO5L,IAAW,CAC5D,QAAQ,MAAM,6BAA6BA,CAAM,KAAM4L,CAAK,CAChE,CAAC,CACL,CAOA,oBAAoBoG,EAAY3Q,EAAO,CACnC,OAAQ2Q,EAAU,CACd,IAAK,cACG,KAAK,YAAc3Q,EAAQ,IAC3B,KAAK,WAAW,SAASA,CAAK,EAC9B,QAAQ,IAAI,wBAAwBA,CAAK,uBAAuB,GAEpE,MAEJ,IAAK,yBACGA,GACA,QAAQ,IAAI,sCAAsC,EAGtD,MAEJ,IAAK,qBACD,QAAQ,IAAI,yBAAyBA,CAAK,EAAE,EAE5C,MAEJ,QACI,QAAQ,IAAI,2BAA2B2Q,CAAU,MAAM3Q,CAAK,EAAE,CAC9E,CACI,CAEA,cAAe,CACX,MAAM4Q,EAAY,SAAS,eAAe,gBAAgB,EAC1D,GAAIA,EAAW,CACX,KAAM,CAAE,MAAApX,EAAO,OAAAC,GAAWmX,EAAU,sBAAqB,EACzD,KAAK,OAAO,MAAQpX,EACpB,KAAK,OAAO,OAASC,EACrB,QAAQ,IAAI,sBAAsB,KAAK,OAAO,KAAK,IAAI,KAAK,OAAO,MAAM,EAAE,CAC/E,CACJ,CACJ,CCn8BO,MAAMoX,EAAY,CACrB,YAAY/R,EAAS,GAAI,CACrB,KAAK,OAAS,CACV,QAAS,kCACT,gBAAiB,OAAO,SAAS,OACjC,SAAUA,EAAO,UAAY,gBAC7B,gBAAiBA,EAAO,iBAAmB,eAC3C,UAAWA,EAAO,WAAa,KAAK,mBAAkB,CAClE,EAEQ,KAAK,KAAO,KACZ,KAAK,MAAQ,KACb,KAAK,aAAe,KACpB,KAAK,gBAAkB,GACvB,KAAK,cAAgB,CAAA,CACzB,CAEA,oBAAqB,CACjB,OAAO,OAAO,SAAS,WAAa,aAC7B,OAAO,SAAS,WAAa,aAC7B,OAAO,SAAS,OAAO,SAAS,YAAY,CACvD,CAEA,MAAM,oBAAqB,CACvB,OAAI,KAAK,OAAO,WACZ,QAAQ,IAAI,uDAAuD,EAC5D,IAGJ,IAAI,QAAQ,CAAC0O,EAASC,IAAW,CAEpC,WAAW,IAAM,CAQb,GANA,QAAQ,IAAI,+BAAgC,CACxC,MAAO,CAAC,CAAC,OAAO,MAChB,SAAU,CAAC,CAAC,OAAO,SACnB,QAAS,CAAC,CAAC,OAAO,OACtC,CAAiB,EAEG,CAAC,OAAO,OAAS,CAAC,OAAO,UAAY,CAAC,OAAO,QAAS,CACtD,QAAQ,MAAM,iCAAkC,CAC5C,MAAO,CAAC,CAAC,OAAO,MAChB,SAAU,CAAC,CAAC,OAAO,SACnB,QAAS,CAAC,CAAC,OAAO,OAC1C,CAAqB,EACDA,EAAO,IAAI,MAAM,2EAA2E,CAAC,EAC7F,MACJ,CAEA,QAAQ,IAAI,sDAAsD,EAClE,KAAK,oBAAoBD,EAASC,CAAM,CAC5C,EAAG,GAAG,CACV,CAAC,CACL,CAIA,oBAAoBD,EAASC,EAAQ,CACjC,GAAI,CACA,MAAMmD,EAAY,SAAS,eAAe,sBAAsB,EAChE,GAAI,CAACA,EAAW,CACZnD,EAAO,IAAI,MAAM,+BAA+B,CAAC,EACjD,MACJ,CAEA,MAAMqD,EAAO,SAAS,WAAWF,CAAS,EAGpCG,EAAY,IAAI,gBAAgB,OAAO,SAAS,MAAM,EACtDC,EAAQD,EAAU,IAAI,OAAO,EAC7BE,EAAeF,EAAU,IAAI,cAAc,EAE7CC,GAASC,EAETH,EAAK,OACD,MAAM,cACF,OAAO,QAAQ,wBACf,KAAK,OACL,MAAM,cAAc,KAAK,wBAAwBE,EAAOC,CAAY,CAAC,CAC7F,CACA,EAGgBH,EAAK,OACD,MAAM,cACF,OAAO,QAAQ,wBACf,KAAK,OACL,MAAM,cAAc,OAAO,QAAQ,WAAY,CAC3C,MAAO,aACP,KAAM,uFACN,QAAS,cACT,iBAAkB,iBAClB,kBAAmB,GACnB,cAAe,KACf,SAAU,CACN,oBAAqB,GACrB,iBAAkB,GAClB,kBAAmB,GACnB,iBAAkB,EAClD,EAC4B,eAAgB,CAACtC,EAAM0C,IAAW,CAC9B,QAAQ,IAAI,2BAA4B1C,CAAI,EAC5C,KAAK,kBAAkB0C,EAAO,YAAaA,EAAO,YAAY,CAClE,EACA,aAAe3G,GAAU,CACrB,QAAQ,MAAM,yBAA0BA,CAAK,EAC7C,KAAK,gBAAgBA,CAAK,CAC9B,CAC5B,CAAyB,CACzB,CACA,EAIY,KAAK,wBAAuB,EAC5BiD,EAAQ,EAAI,CAChB,OAASjD,EAAO,CACZ,QAAQ,MAAM,qCAAsCA,CAAK,EACzDkD,EAAOlD,CAAK,CAChB,CACJ,CAEA,wBAAwByG,EAAOC,EAAc,CACzC,MAAME,EAAO,KACb,OAAO,UAA6B,CAChC,KAAM,CAAE,cAAAC,CAAa,EAAK,OAAO,QAAQ,mBAAkB,EAE3D,aAAM,UAAU,IAAM,CAClB,eAAeC,GAAkB,CAC7B,GAAI,CACgB,MAAMD,EAAcJ,EAAOC,CAAY,EAEnD,QAAQ,IAAI,gCAAgC,EAG5CE,EAAK,gBAAgB,IAAI,MAAM,uBAAuB,CAAC,CAE/D,OAAS5G,EAAO,CACZ,QAAQ,MAAM,6BAA8BA,CAAK,EACjD4G,EAAK,gBAAgB5G,CAAK,CAC9B,CACJ,CACA8G,EAAe,CACnB,EAAG,CAACD,CAAa,CAAC,EAEX,MAAM,cAAc,MAAO,CAC9B,MAAO,CAAE,UAAW,SAAU,QAAS,OAAQ,MAAO,SAAS,CAC/E,EAAe,8BAA8B,CACrC,CACJ,CAEA,yBAA0B,CAEtB,OAAO,iBAAiB,oBAAsBzd,GAAU,CACpD,KAAK,kBAAkBA,EAAM,OAAO,MAAOA,EAAM,OAAO,YAAY,CACxE,CAAC,EAED,OAAO,iBAAiB,kBAAoBA,GAAU,CAClD,KAAK,gBAAgBA,EAAM,OAAO,KAAK,CAC3C,CAAC,CACL,CAEA,MAAM,kBAAkBqd,EAAOC,EAAc,CACzC,KAAK,MAAQD,EACb,KAAK,aAAeC,EAEpB,GAAI,CACA,MAAMK,EAAc,MAAM,KAAK,cAAcN,CAAK,EAClD,KAAK,KAAOM,EACZ,KAAK,gBAAkB,GAGvB,MAAMC,EAAM,IAAI,IAAI,OAAO,QAAQ,EACnCA,EAAI,aAAa,OAAO,OAAO,EAC/BA,EAAI,aAAa,OAAO,cAAc,EACtC,OAAO,QAAQ,aAAa,CAAA,EAAI,SAAS,MAAOA,CAAG,EAEnD,KAAK,oBAAoB,GAAM,KAAK,IAAI,CAC5C,OAAShH,EAAO,CACZ,KAAK,gBAAgBA,CAAK,CAC9B,CACJ,CAEA,gBAAgBA,EAAO,CACnB,QAAQ,MAAM,wBAAyBA,CAAK,EAC5C,KAAK,gBAAkB,GACvB,KAAK,KAAO,KACZ,KAAK,MAAQ,KACb,KAAK,aAAe,KACpB,KAAK,oBAAoB,GAAOA,CAAK,CACzC,CAEA,MAAM,cAAcyG,EAAO,CACvB,MAAMQ,EAAW,MAAM,MAAM,mDAAoD,CAC7E,QAAS,CACL,cAAiB,UAAUR,CAAK,GAChC,eAAgB,kBAChC,CACA,CAAS,EAED,GAAI,CAACQ,EAAS,GACV,MAAM,IAAI,MAAM,yBAAyB,EAG7C,OAAO,MAAMA,EAAS,KAAI,CAC9B,CAEA,iBAAkB,CACd,KAAK,OAAO,UAAY,GACxB,KAAK,KAAO,CACR,GAAI,iBACJ,MAAO,oBACP,KAAM,aACN,SAAU,OACtB,EACQ,KAAK,gBAAkB,GACvB,KAAK,oBAAoB,GAAM,KAAK,IAAI,CAC5C,CAEA,MAAM,QAAS,CACX,KAAK,KAAO,KACZ,KAAK,MAAQ,KACb,KAAK,aAAe,KACpB,KAAK,gBAAkB,GACvB,KAAK,oBAAoB,GAAO,IAAI,CACxC,CAEA,SAAU,CACN,OAAO,KAAK,IAChB,CAEA,kBAAkB7V,EAAU,CACxB,KAAK,cAAc,KAAKA,CAAQ,CACpC,CAEA,oBAAoB8V,EAAiBlF,EAAM,CACvC,KAAK,cAAc,QAAQ5Q,GAAY,CACnCA,EAAS8V,EAAiBlF,CAAI,CAClC,CAAC,CACL,CACJ,CClPO,MAAMmF,EAAS,CACpB,YAAYpH,EAAY,CACtB,KAAK,WAAaA,EAClB,KAAK,YAAc,IAAIuG,GAAY,CACjC,SAAU,gBACV,gBAAiB,cACvB,CAAK,EAED,KAAK,UAAY,KACjB,KAAK,UAAY,GACjB,KAAK,oBAAmB,CAC1B,CAEA,MAAM,YAAa,CAMjB,GALA,KAAK,eAAc,EAEnB,QAAQ,IAAI,qCAAsC,KAAK,YAAY,OAAO,SAAS,EAG9E,KAAK,YAAY,OAAO,UAU3B,QAAQ,IAAI,uDAAuD,MATnE,IAAI,CACF,QAAQ,IAAI,2BAA2B,EACvC,MAAM,KAAK,YAAY,mBAAkB,EACzC,QAAQ,IAAI,oCAAoC,CAClD,OAAStG,EAAO,CACd,QAAQ,MAAM,kCAAmCA,CAAK,EACtD,KAAK,UAAU,yCAAyCA,EAAM,OAAO,oCAAoC,CAC3G,CAKF,KAAK,YAAY,kBAAkB,CAACkH,EAAiBlF,IAAS,CACxDkF,EACF,KAAK,wBAAwBlF,CAAI,EAEjC,KAAK,wBAAwBA,CAAI,CAErC,CAAC,CACH,CAEA,gBAAiB,CACf,KAAK,UAAY,SAAS,cAAc,KAAK,EAC7C,KAAK,UAAU,GAAK,YACpB,KAAK,UAAU,UAAY,YAE3B,KAAK,UAAU,UAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BAiBD,KAAK,YAAY,OAAO,UAAY,KAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA8B1E,SAAS,KAAK,YAAY,KAAK,SAAS,CAC1C,CAEA,qBAAsB,CACpB,SAAS,iBAAiB,QAAU5Y,GAAU,CACxCA,EAAM,OAAO,KAAO,kBACtB,KAAK,YAAY,gBAAe,EACvBA,EAAM,OAAO,KAAO,iBAC7B,KAAK,UAAS,EACLA,EAAM,OAAO,KAAO,iBAC7B,KAAK,cAAa,EACTA,EAAM,OAAO,KAAO,mBAC7B,KAAK,iBAAgB,EACZA,EAAM,OAAO,KAAO,cAC7B,KAAK,OAAM,CAEf,CAAC,CACH,CAEA,wBAAwB6a,EAAM,CAC5B,QAAQ,IAAI,6BAA8BA,CAAI,EAG9C,SAAS,eAAe,cAAc,EAAE,MAAM,QAAU,OACxD,SAAS,eAAe,eAAe,EAAE,MAAM,QAAU,QACzD,SAAS,eAAe,WAAW,EAAE,MAAM,QAAU,QAGrD,SAAS,eAAe,WAAW,EAAE,YAAcA,EAAK,MAAQA,EAAK,OAAS,SAG9E,KAAK,qBAAoB,EAEzB,KAAK,UAAS,CAChB,CAEA,wBAAwBjE,EAAO,CAC7B,QAAQ,MAAM,yBAA0BA,CAAK,EAG7C,SAAS,eAAe,cAAc,EAAE,MAAM,QAAU,QACxD,SAAS,eAAe,eAAe,EAAE,MAAM,QAAU,OACzD,SAAS,eAAe,WAAW,EAAE,MAAM,QAAU,OAEjDA,GAAS,OAAOA,GAAU,UAC5B,KAAK,UAAU,0CAA0C,CAE7D,CAEA,MAAM,sBAAuB,CAG3B,SAAS,eAAe,eAAe,EAAE,YAAc,KACzD,CAEA,WAAY,CACV,GAAI,CAAC,KAAK,YAAY,gBAAiB,CACrC,KAAK,UAAU,6BAA6B,EAC5C,MACF,CAEA,KAAK,KAAI,EACT,KAAK,WAAW,UAAU,KAAK,YAAY,QAAO,CAAE,CACtD,CAEA,eAAgB,CACd,GAAI,CAAC,KAAK,YAAY,gBAAiB,CACrC,KAAK,UAAU,2BAA2B,EAC1C,MACF,CAEA,QAAQ,IAAI,sCAAsC,EAG9C,KAAK,YAAc,KAAK,WAAW,gBAEjC,KAAK,WAAW,cAAgB,KAAK,WAAW,aAAa,WAAU,EAAK,MAC9E,KAAK,WAAW,aAAa,YAAY,IAAK,mBAAmB,EACjE,QAAQ,IAAI,kCAAkC,GAGhD,KAAK,WAAW,eAAe,KAAI,GAEnC,KAAK,UAAU,wDAAwD,CAE3E,CAEA,kBAAmB,CAyBjB,MAxBqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAwBH,CACpB,CAEA,MAAM,QAAS,CACb,MAAM,KAAK,YAAY,OAAM,EAC7B,KAAK,wBAAwB,IAAI,CACnC,CAEA,MAAO,CACD,KAAK,YACP,KAAK,UAAU,MAAM,QAAU,QAC/B,KAAK,UAAY,GAErB,CAEA,MAAO,CACD,KAAK,YACP,KAAK,UAAU,MAAM,QAAU,OAC/B,KAAK,UAAY,GAErB,CAEA,UAAUoH,EAAS,CACjB,MAAMC,EAAe,SAAS,eAAe,eAAe,EACxDA,IACFA,EAAa,YAAcD,EAC3BC,EAAa,MAAM,QAAU,QAEjC,CAEA,WAAY,CACV,MAAMA,EAAe,SAAS,eAAe,eAAe,EACxDA,IACFA,EAAa,MAAM,QAAU,OAEjC,CAEA,SAAU,CACJ,KAAK,YACP,SAAS,KAAK,YAAY,KAAK,SAAS,EACxC,KAAK,UAAY,KAErB,CACF,CCvPA,MAAMC,EAAK,CACT,aAAc,CACZ,KAAK,WAAa,KAClB,KAAK,SAAW,KAChB,KAAK,cAAgB,EACvB,CAEA,MAAM,YAAa,CACjB,GAAI,MAAK,cAET,GAAI,CAEF,MAAMne,EAAS,SAAS,eAAe,YAAY,EACnD,GAAI,CAACA,EACH,MAAM,IAAI,MAAM,uBAAuB,EAIzC,KAAK,WAAa,IAAI4a,GAAW5a,CAAM,EACvC,MAAM,KAAK,WAAW,KAAI,EAG1B,KAAK,SAAW,IAAIge,GAAS,KAAK,UAAU,EAC5C,MAAM,KAAK,SAAS,WAAU,EAG9B,KAAK,SAAS,KAAI,EAElB,KAAK,cAAgB,GACrB,QAAQ,IAAI,+BAA+B,CAC7C,OAASnH,EAAO,CACd,QAAQ,MAAM,6BAA8BA,CAAK,EACjD,KAAK,wBAAwBA,CAAK,CACpC,CACF,CAEA,wBAAwBA,EAAO,CAC7B,SAAS,KAAK,UAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAaRA,EAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAgBjC,CACF,CAGA,IAAIuH,EAAO,KAGX,SAAS,iBAAiB,mBAAoB,SAAY,CACxDA,EAAO,IAAID,GACX,MAAMC,EAAK,WAAU,CACvB,CAAC,EAGD,SAAS,iBAAiB,mBAAoB,IAAM,CAC9CA,GAAQA,EAAK,aACX,SAAS,OACXA,EAAK,WAAW,MAAK,EAErBA,EAAK,WAAW,OAAM,EAG5B,CAAC,EAGD,OAAO,iBAAiB,eAAgB,MAAOne,GAAU,CACnDme,GAAQA,EAAK,YAEf,MAAMA,EAAK,WAAW,QAAO,CAEjC,CAAC,EAGD,SAAS,iBAAiB,WAAY,MAAOne,GAAU,CACjDme,GAAQA,EAAK,YACf,MAAMA,EAAK,WAAW,QAAO,CAEjC,CAAC", "x_google_ignoreList": [16]}