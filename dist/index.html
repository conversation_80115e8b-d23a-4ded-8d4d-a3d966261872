<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WarpSpace - Reality Warping Shooter</title>
  <script type="module" crossorigin src="/assets/index-B3UBkQnu.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-C9RlFhuu.css">
</head>
<body>
    <div id="game-container">
        <canvas id="gameCanvas"></canvas>
    </div>
    
    <!-- React libraries for Orange ID -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>

    <!-- Bedrock Passport for Orange ID -->
    <script src="https://public-cdn-files.pages.dev/bedrock-passport.umd.js"></script>

    <!-- Optional: Tailwind CSS for Orange ID styling -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Game scripts -->
</body>
</html>
