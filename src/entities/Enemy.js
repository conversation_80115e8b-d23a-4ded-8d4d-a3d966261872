import { GameObject } from '../utils/GameObject.js';
import { Vector2 } from '../utils/Vector2.js';
import { GameMath } from '../utils/GameMath.js';
import { ENEMY_TYPES } from '../config/gameConfig.js';

/**
 * Enemy base class with various movement patterns and behaviors
 * Supports different enemy types with environmental effectiveness modifiers
 */
export class Enemy extends GameObject {
    constructor(x, y, type = ENEMY_TYPES.AIR, canvasWidth = 800, canvasHeight = 600) {
        super(x, y);
        
        // Canvas boundaries
        this.canvasWidth = canvasWidth;
        this.canvasHeight = canvasHeight;
        
        // Enemy properties
        this.type = type;
        this.maxHealth = this.getTypeMaxHealth(type);
        this.health = this.maxHealth;
        this.baseSpeed = this.getTypeBaseSpeed(type);
        this.currentSpeed = this.baseSpeed;
        
        // Visual properties
        this.width = 24;
        this.height = 24;
        this.collisionRadius = 12;
        
        // Movement behavior
        this.movementPattern = 'linear';
        this.movementTimer = 0;
        this.movementPhase = Math.random() * Math.PI * 2; // Random starting phase
        this.amplitude = 50; // For sine wave patterns
        this.frequency = 2; // For oscillating patterns

        // Predefined movement pattern properties
        this.predefinedPattern = null;
        this.predefinedState = 0; // Current state in predefined pattern
        this.predefinedTimer = 0; // Timer for predefined pattern phases
        this.initialPosition = new Vector2(x, y); // Store initial spawn position
        
        // Formation flying properties
        this.formationOffset = new Vector2(0, 0);
        this.formationTarget = new Vector2(x, y);
        this.isInFormation = false;
        
        // Attack behavior
        this.attackCooldown = 0;
        this.baseAttackRate = this.getTypeAttackRate(type);
        this.currentAttackRate = this.baseAttackRate;
        this.canAttack = true;
        
        // Environmental effectiveness
        this.environmentalEffectiveness = 1.0;
        this.currentEnvironment = 'space';
        
        // Animation properties
        this.animationTime = 0;
        this.animationSpeed = 4; // cycles per second
        this.spriteFrame = 0;
        this.totalFrames = 4;
        
        // State properties
        this.isDestroyed = false;
        this.deathAnimationTime = 0;
        this.deathAnimationDuration = 500; // 500ms death animation
        
        // Scoring
        this.scoreValue = this.getTypeScoreValue(type);
        
        // Add enemy tag
        this.addTag('enemy');
        this.addTag(type);
        
        // Set initial movement direction
        this.setMovementPattern('linear');
        
        console.log(`Enemy created: type=${type}, health=${this.health}, position=${this.position.toString()}`);
    }

    /**
     * Reset enemy for object pooling
     * Properly reinitializes all enemy properties
     */
    reset() {
        // Call parent reset first
        super.reset();

        // Reset enemy-specific properties
        this.type = ENEMY_TYPES.AIR;
        this.maxHealth = this.getTypeMaxHealth(this.type);
        this.health = this.maxHealth;
        this.baseSpeed = this.getTypeBaseSpeed(this.type);
        this.currentSpeed = this.baseSpeed;

        // Reset visual properties
        this.width = 24;
        this.height = 24;
        this.collisionRadius = 12;

        // Reset movement properties
        this.movementPattern = 'linear';
        this.movementTime = 0;
        this.patternData = {};
        this.targetPosition = null;
        this.formationPosition = null;
        this.formationOffset = new Vector2(0, 0);

        // Reset attack behavior
        this.attackCooldown = 0;
        this.baseAttackRate = this.getTypeAttackRate(this.type);
        this.currentAttackRate = this.baseAttackRate;
        this.canAttack = true;

        // Reset environmental effectiveness
        this.environmentalEffectiveness = 1.0;
        this.currentEnvironment = 'space';

        // Reset animation properties
        this.animationTime = 0;
        this.animationSpeed = 4;
        this.spriteFrame = 0;
        this.totalFrames = 4;

        // Reset state properties
        this.isDestroyed = false;
        this.deathAnimationTime = 0;
        this.deathAnimationDuration = 500;

        // Reset scoring
        this.scoreValue = this.getTypeScoreValue(this.type);

        // Re-add essential tags
        this.addTag('enemy');
        this.addTag(this.type);

        // Set initial movement direction
        this.setMovementPattern('linear');
    }

    /**
     * Get maximum health based on enemy type
     * @param {string} type - Enemy type
     * @returns {number} Maximum health
     */
    getTypeMaxHealth(type) {
        const healthMap = {
            [ENEMY_TYPES.AIR]: 20,
            [ENEMY_TYPES.WATER]: 30,
            [ENEMY_TYPES.FIRE]: 25,
            [ENEMY_TYPES.EARTH]: 40,
            [ENEMY_TYPES.CRYSTAL]: 35,
            [ENEMY_TYPES.SHADOW]: 15
        };
        return healthMap[type] || 20;
    }
    
    /**
     * Get base movement speed based on enemy type
     * @param {string} type - Enemy type
     * @returns {number} Base speed in pixels per second
     */
    getTypeBaseSpeed(type) {
        const speedMap = {
            [ENEMY_TYPES.AIR]: 120,
            [ENEMY_TYPES.WATER]: 80,
            [ENEMY_TYPES.FIRE]: 100,
            [ENEMY_TYPES.EARTH]: 60,
            [ENEMY_TYPES.CRYSTAL]: 90,
            [ENEMY_TYPES.SHADOW]: 140
        };
        return speedMap[type] || 100;
    }
    
    /**
     * Get base attack rate based on enemy type (attacks per second)
     * @param {string} type - Enemy type
     * @returns {number} Base attack rate
     */
    getTypeAttackRate(type) {
        const attackRateMap = {
            [ENEMY_TYPES.AIR]: 1.5,
            [ENEMY_TYPES.WATER]: 1.0,
            [ENEMY_TYPES.FIRE]: 2.0,
            [ENEMY_TYPES.EARTH]: 0.8,
            [ENEMY_TYPES.CRYSTAL]: 1.2,
            [ENEMY_TYPES.SHADOW]: 2.5
        };
        return attackRateMap[type] || 1.0;
    }
    
    /**
     * Get score value based on enemy type
     * @param {string} type - Enemy type
     * @returns {number} Score value
     */
    getTypeScoreValue(type) {
        const scoreMap = {
            [ENEMY_TYPES.AIR]: 100,
            [ENEMY_TYPES.WATER]: 150,
            [ENEMY_TYPES.FIRE]: 125,
            [ENEMY_TYPES.EARTH]: 200,
            [ENEMY_TYPES.CRYSTAL]: 175,
            [ENEMY_TYPES.SHADOW]: 300
        };
        return scoreMap[type] || 100;
    }
    
    /**
     * Update enemy behavior, movement, and animation
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's current position for targeting
     */
    update(deltaTime, playerPosition = null) {
        if (!this.active) return;
        
        // Update timers
        this.movementTimer += deltaTime / 1000;
        this.animationTime += deltaTime / 1000;
        
        // Handle death animation
        if (this.isDestroyed) {
            this.updateDeathAnimation(deltaTime);
            return;
        }
        
        // Update movement based on pattern
        this.updateMovement(deltaTime, playerPosition);
        
        // Update attack cooldown
        this.updateAttackCooldown(deltaTime);
        
        // Update animation frame
        this.updateAnimation(deltaTime);
        
        // Check if enemy has moved off screen (for cleanup)
        this.checkOffScreen();
        
        // Update collision bounds
        this.updateCollisionBounds();
    }
    
    /**
     * Update movement based on current movement pattern
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {Vector2} playerPosition - Player's current position
     */
    updateMovement(deltaTime, playerPosition) {
        const dt = deltaTime / 1000;
        
        switch (this.movementPattern) {
            case 'linear':
                this.updateLinearMovement(dt);
                break;
            case 'sine':
                this.updateSineMovement(dt);
                break;
            case 'spiral':
                this.updateSpiralMovement(dt);
                break;
            case 'dive':
                this.updateDiveMovement(dt, playerPosition);
                break;
            case 'formation':
                this.updateFormationMovement(dt);
                break;
            case 'zigzag':
                this.updateZigzagMovement(dt);
                break;
            case 'circle':
                this.updateCircleMovement(dt);
                break;
            case 'hover':
                this.updateHoverMovement(dt, playerPosition);
                break;
            case 'predefined':
                this.updatePredefinedMovement(dt, playerPosition);
                break;
        }
        
        // Apply environmental speed modifier
        const effectiveSpeed = this.currentSpeed * this.environmentalEffectiveness;
        this.velocity.multiplyInPlace(effectiveSpeed / this.velocity.magnitude());
        
        // Update position
        this.position.addInPlace(this.velocity.multiply(dt));
    }
    
    /**
     * Linear downward movement (classic Space Invaders style)
     * @param {number} dt - Delta time in seconds
     */
    updateLinearMovement(dt) {
        this.velocity.set(0, this.currentSpeed);
    }
    
    /**
     * Sine wave movement (classic Galaga style)
     * @param {number} dt - Delta time in seconds
     */
    updateSineMovement(dt) {
        const sineOffset = Math.sin(this.movementTimer * this.frequency + this.movementPhase) * this.amplitude;
        const targetX = this.formationTarget.x + sineOffset;
        
        // Move towards target X position while moving down
        const xDirection = Math.sign(targetX - this.position.x);
        this.velocity.set(xDirection * this.currentSpeed * 0.5, this.currentSpeed * 0.8);
    }
    
    /**
     * Spiral movement pattern
     * @param {number} dt - Delta time in seconds
     */
    updateSpiralMovement(dt) {
        const radius = this.amplitude * (1 - this.movementTimer * 0.1);
        const angle = this.movementTimer * this.frequency + this.movementPhase;
        
        const targetX = this.formationTarget.x + Math.cos(angle) * radius;
        const targetY = this.formationTarget.y + this.movementTimer * this.currentSpeed * 0.3;
        
        const direction = new Vector2(targetX - this.position.x, targetY - this.position.y).normalize();
        this.velocity = direction.multiply(this.currentSpeed);
    }
    
    /**
     * Dive attack movement (Galaga-style dive bomb)
     * @param {number} dt - Delta time in seconds
     * @param {Vector2} playerPosition - Player's position
     */
    updateDiveMovement(dt, playerPosition) {
        if (!playerPosition) {
            this.updateLinearMovement(dt);
            return;
        }
        
        if (this.movementTimer < 1.0) {
            // Initial curve out
            const curveDirection = this.movementPhase > Math.PI ? -1 : 1;
            this.velocity.set(curveDirection * this.currentSpeed, this.currentSpeed * 0.5);
        } else if (this.movementTimer < 2.0) {
            // Dive towards player
            const direction = playerPosition.subtract(this.position).normalize();
            this.velocity = direction.multiply(this.currentSpeed * 1.5);
        } else {
            // Return to formation or continue off screen
            this.velocity.set(0, this.currentSpeed);
        }
    }
    
    /**
     * Formation flying movement
     * @param {number} dt - Delta time in seconds
     */
    updateFormationMovement(dt) {
        const targetPos = this.formationTarget.add(this.formationOffset);
        const direction = targetPos.subtract(this.position);
        
        if (direction.magnitude() > 5) {
            this.velocity = direction.normalize().multiply(this.currentSpeed);
        } else {
            this.velocity.set(0, this.currentSpeed * 0.2); // Slow drift when in formation
        }
    }
    
    /**
     * Zigzag movement pattern
     * @param {number} dt - Delta time in seconds
     */
    updateZigzagMovement(dt) {
        const zigzagPhase = Math.floor(this.movementTimer * this.frequency) % 2;
        const direction = zigzagPhase === 0 ? 1 : -1;
        
        this.velocity.set(direction * this.currentSpeed * 0.7, this.currentSpeed * 0.8);
    }
    
    /**
     * Circular movement pattern
     * @param {number} dt - Delta time in seconds
     */
    updateCircleMovement(dt) {
        const angle = this.movementTimer * this.frequency + this.movementPhase;
        const centerX = this.formationTarget.x;
        const centerY = this.formationTarget.y + this.movementTimer * this.currentSpeed * 0.2;
        
        const targetX = centerX + Math.cos(angle) * this.amplitude;
        const targetY = centerY + Math.sin(angle) * this.amplitude * 0.5;
        
        const direction = new Vector2(targetX - this.position.x, targetY - this.position.y).normalize();
        this.velocity = direction.multiply(this.currentSpeed);
    }
    
    /**
     * Hover and strafe movement (stays on screen longer)
     * @param {number} dt - Delta time in seconds
     * @param {Vector2} playerPosition - Player's position
     */
    updateHoverMovement(dt, playerPosition) {
        if (!playerPosition) {
            this.updateLinearMovement(dt);
            return;
        }

        // Hover at a certain distance from player
        const hoverDistance = 150;
        const distanceToPlayer = this.position.distance(playerPosition);

        if (distanceToPlayer > hoverDistance + 20) {
            // Move closer to player
            const direction = playerPosition.subtract(this.position).normalize();
            this.velocity = direction.multiply(this.currentSpeed * 0.6);
        } else if (distanceToPlayer < hoverDistance - 20) {
            // Move away from player
            const direction = this.position.subtract(playerPosition).normalize();
            this.velocity = direction.multiply(this.currentSpeed * 0.6);
        } else {
            // Strafe around player
            const perpendicular = playerPosition.subtract(this.position).perpendicular().normalize();
            this.velocity = perpendicular.multiply(this.currentSpeed * 0.8);
        }
    }

    /**
     * Update predefined movement patterns
     * @param {number} dt - Delta time in seconds
     * @param {Vector2} playerPosition - Player's position
     */
    updatePredefinedMovement(dt, playerPosition) {
        this.predefinedTimer += dt;

        if (!this.predefinedPattern) {
            this.updateLinearMovement(dt);
            return;
        }

        switch (this.predefinedPattern) {
            case 'straight_down':
                this.updateStraightDownPattern(dt);
                break;
            case 'triangle_left_right':
                this.updateTriangleLeftRightPattern(dt);
                break;
            case 'triangle_zigzag':
                this.updateTriangleZigzagPattern(dt);
                break;
            case 'diamond_sine':
                this.updateDiamondSinePattern(dt);
                break;
            case 'coordinated_sweep_left':
                this.updateCoordinatedSweepPattern(dt, -1);
                break;
            case 'coordinated_sweep_right':
                this.updateCoordinatedSweepPattern(dt, 1);
                break;
            case 'advanced_weave':
                this.updateAdvancedWeavePattern(dt);
                break;
            case 'dive_attack':
                this.updateDiveAttackPattern(dt, playerPosition);
                break;
            case 'spiral_descent':
                this.updateSpiralDescentPattern(dt);
                break;
            case 'pincer_left':
                this.updatePincerPattern(dt, -1);
                break;
            case 'pincer_right':
                this.updatePincerPattern(dt, 1);
                break;
            case 'center_rush':
                this.updateCenterRushPattern(dt, playerPosition);
                break;
            case 'wedge_assault':
                this.updateWedgeAssaultPattern(dt);
                break;
            case 'side_sweep':
                this.updateSideSweepPattern(dt);
                break;
            default:
                this.updateLinearMovement(dt);
                break;
        }
    }
    
    /**
     * Update attack cooldown timer
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    updateAttackCooldown(deltaTime) {
        if (this.attackCooldown > 0) {
            this.attackCooldown -= deltaTime;
            this.canAttack = this.attackCooldown <= 0;
        }
    }
    
    /**
     * Update animation frame
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    updateAnimation(deltaTime) {
        const frameTime = 1000 / (this.animationSpeed * this.totalFrames);
        if (this.animationTime * 1000 % frameTime < deltaTime) {
            this.spriteFrame = (this.spriteFrame + 1) % this.totalFrames;
        }
    }
    
    /**
     * Straight down movement pattern
     * @param {number} dt - Delta time in seconds
     */
    updateStraightDownPattern(dt) {
        this.velocity.set(0, this.currentSpeed);
    }

    /**
     * Triangle formation moving left 2 spaces, right 3 spaces, left 1 space
     * @param {number} dt - Delta time in seconds
     */
    updateTriangleLeftRightPattern(dt) {
        const phaseTime = 2.0; // 2 seconds per phase
        const currentPhase = Math.floor(this.predefinedTimer / phaseTime) % 3;

        switch (currentPhase) {
            case 0: // Move left 2 spaces
                this.velocity.set(-this.currentSpeed * 0.8, this.currentSpeed * 0.6);
                break;
            case 1: // Move right 3 spaces
                this.velocity.set(this.currentSpeed * 1.2, this.currentSpeed * 0.6);
                break;
            case 2: // Move left 1 space
                this.velocity.set(-this.currentSpeed * 0.4, this.currentSpeed * 0.8);
                break;
        }
    }

    /**
     * Triangle formation with zigzag pattern
     * @param {number} dt - Delta time in seconds
     */
    updateTriangleZigzagPattern(dt) {
        const zigzagFreq = 1.5;
        const direction = Math.sin(this.predefinedTimer * zigzagFreq) > 0 ? 1 : -1;
        this.velocity.set(direction * this.currentSpeed * 0.6, this.currentSpeed * 0.8);
    }

    /**
     * Diamond formation with sine wave movement
     * @param {number} dt - Delta time in seconds
     */
    updateDiamondSinePattern(dt) {
        const sineOffset = Math.sin(this.predefinedTimer * 2) * 60;
        const targetX = this.initialPosition.x + sineOffset;
        const xDirection = Math.sign(targetX - this.position.x);
        this.velocity.set(xDirection * this.currentSpeed * 0.4, this.currentSpeed * 0.9);
    }

    /**
     * Coordinated sweep pattern
     * @param {number} dt - Delta time in seconds
     * @param {number} direction - Direction multiplier (-1 for left, 1 for right)
     */
    updateCoordinatedSweepPattern(dt, direction) {
        const sweepPhase = Math.sin(this.predefinedTimer * 0.8) * direction;
        this.velocity.set(sweepPhase * this.currentSpeed * 0.7, this.currentSpeed * 0.7);
    }

    /**
     * Advanced weave pattern
     * @param {number} dt - Delta time in seconds
     */
    updateAdvancedWeavePattern(dt) {
        const weaveX = Math.sin(this.predefinedTimer * 2.5) * this.currentSpeed * 0.8;
        const weaveY = Math.cos(this.predefinedTimer * 1.2) * this.currentSpeed * 0.3;
        this.velocity.set(weaveX, this.currentSpeed * 0.6 + weaveY);
    }

    /**
     * Dive attack pattern
     * @param {number} dt - Delta time in seconds
     * @param {Vector2} playerPosition - Player's position
     */
    updateDiveAttackPattern(dt, playerPosition) {
        if (!playerPosition) {
            this.updateLinearMovement(dt);
            return;
        }

        if (this.predefinedTimer < 1.5) {
            // Initial approach
            this.velocity.set(0, this.currentSpeed * 0.5);
        } else if (this.predefinedTimer < 3.0) {
            // Dive towards player
            const direction = playerPosition.subtract(this.position).normalize();
            this.velocity = direction.multiply(this.currentSpeed * 1.8);
        } else {
            // Continue off screen
            this.velocity.set(0, this.currentSpeed * 1.2);
        }
    }

    /**
     * Spiral descent pattern
     * @param {number} dt - Delta time in seconds
     */
    updateSpiralDescentPattern(dt) {
        const radius = 80 - (this.predefinedTimer * 10);
        const angle = this.predefinedTimer * 3;
        const spiralX = Math.cos(angle) * Math.max(0, radius);
        const spiralY = Math.sin(angle) * Math.max(0, radius) * 0.3;

        const targetX = this.initialPosition.x + spiralX;
        const targetY = this.initialPosition.y + this.predefinedTimer * this.currentSpeed * 0.4 + spiralY;

        const direction = new Vector2(targetX - this.position.x, targetY - this.position.y).normalize();
        this.velocity = direction.multiply(this.currentSpeed);
    }

    /**
     * Pincer movement pattern
     * @param {number} dt - Delta time in seconds
     * @param {number} side - Side multiplier (-1 for left, 1 for right)
     */
    updatePincerPattern(dt, side) {
        const curveIntensity = Math.sin(this.predefinedTimer * 1.5) * side;
        this.velocity.set(curveIntensity * this.currentSpeed * 0.9, this.currentSpeed * 0.8);
    }

    /**
     * Center rush pattern
     * @param {number} dt - Delta time in seconds
     * @param {Vector2} playerPosition - Player's position
     */
    updateCenterRushPattern(dt, playerPosition) {
        if (!playerPosition) {
            this.velocity.set(0, this.currentSpeed);
            return;
        }

        // Rush towards center of screen, then towards player
        const screenCenter = new Vector2(this.canvasWidth / 2, this.canvasHeight / 2);
        const target = this.predefinedTimer < 2.0 ? screenCenter : playerPosition;
        const direction = target.subtract(this.position).normalize();
        this.velocity = direction.multiply(this.currentSpeed * 1.3);
    }

    /**
     * Wedge assault pattern
     * @param {number} dt - Delta time in seconds
     */
    updateWedgeAssaultPattern(dt) {
        // Accelerating downward movement with slight weaving
        const weave = Math.sin(this.predefinedTimer * 3) * 20;
        const acceleration = 1 + (this.predefinedTimer * 0.3);
        this.velocity.set(weave, this.currentSpeed * acceleration);
    }

    /**
     * Side sweep pattern
     * @param {number} dt - Delta time in seconds
     */
    updateSideSweepPattern(dt) {
        const sweepDirection = this.position.x < this.canvasWidth / 2 ? 1 : -1;
        this.velocity.set(sweepDirection * this.currentSpeed * 1.1, this.currentSpeed * 0.5);
    }

    /**
     * Update death animation
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    updateDeathAnimation(deltaTime) {
        this.deathAnimationTime += deltaTime;

        // Slow down and fade during death animation
        this.velocity.multiplyInPlace(0.95);

        if (this.deathAnimationTime >= this.deathAnimationDuration) {
            this.destroy();
        }
    }
    
    /**
     * Check if enemy has moved off screen for cleanup
     */
    checkOffScreen() {
        const margin = 50;
        if (this.position.x < -margin || 
            this.position.x > this.canvasWidth + margin ||
            this.position.y > this.canvasHeight + margin) {
            this.destroy();
        }
    }
    
    /**
     * Render the enemy with sprite graphics and animations
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {number} interpolation - Interpolation factor for smooth rendering
     */
    render(ctx, interpolation = 0) {
        if (!this.visible) return;
        
        // Calculate interpolated position for smooth rendering
        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));
        
        ctx.save();
        ctx.translate(renderPos.x, renderPos.y);
        ctx.rotate(this.rotation);
        
        // Apply death animation effects
        if (this.isDestroyed) {
            this.renderDeathAnimation(ctx);
        } else {
            this.renderEnemySprite(ctx);
        }
        
        // Draw debug info if enabled
        if (window.DEBUG_MODE) {
            this.renderDebugInfo(ctx);
        }
        
        ctx.restore();
    }
    
    /**
     * Render the enemy sprite based on type and animation frame
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderEnemySprite(ctx) {
        // Apply environmental effects to appearance
        const effectivenessAlpha = 0.7 + (this.environmentalEffectiveness * 0.3);
        ctx.globalAlpha = effectivenessAlpha;
        
        // Get type-specific colors
        const colors = this.getTypeColors();
        
        // Animate sprite based on frame
        const pulsePhase = Math.sin(this.animationTime * this.animationSpeed * Math.PI * 2);
        const scale = 1 + pulsePhase * 0.1;
        
        ctx.scale(scale, scale);
        
        // Draw enemy based on type
        switch (this.type) {
            case ENEMY_TYPES.AIR:
                this.drawAirEnemy(ctx, colors, pulsePhase);
                break;
            case ENEMY_TYPES.WATER:
                this.drawWaterEnemy(ctx, colors, pulsePhase);
                break;
            case ENEMY_TYPES.FIRE:
                this.drawFireEnemy(ctx, colors, pulsePhase);
                break;
            case ENEMY_TYPES.EARTH:
                this.drawEarthEnemy(ctx, colors, pulsePhase);
                break;
            case ENEMY_TYPES.CRYSTAL:
                this.drawCrystalEnemy(ctx, colors, pulsePhase);
                break;
            case ENEMY_TYPES.SHADOW:
                this.drawShadowEnemy(ctx, colors, pulsePhase);
                break;
            default:
                this.drawDefaultEnemy(ctx, colors, pulsePhase);
                break;
        }
        
        ctx.globalAlpha = 1.0;
    }
    
    /**
     * Get type-specific color scheme
     * @returns {object} Color scheme for the enemy type
     */
    getTypeColors() {
        const colorSchemes = {
            [ENEMY_TYPES.AIR]: {
                primary: '#87CEEB',
                secondary: '#4682B4',
                accent: '#B0E0E6'
            },
            [ENEMY_TYPES.WATER]: {
                primary: '#4169E1',
                secondary: '#1E90FF',
                accent: '#00BFFF'
            },
            [ENEMY_TYPES.FIRE]: {
                primary: '#FF4500',
                secondary: '#FF6347',
                accent: '#FFD700'
            },
            [ENEMY_TYPES.EARTH]: {
                primary: '#8B4513',
                secondary: '#A0522D',
                accent: '#DEB887'
            },
            [ENEMY_TYPES.CRYSTAL]: {
                primary: '#9370DB',
                secondary: '#8A2BE2',
                accent: '#DDA0DD'
            },
            [ENEMY_TYPES.SHADOW]: {
                primary: '#2F2F2F',
                secondary: '#4A4A4A',
                accent: '#696969'
            }
        };
        
        return colorSchemes[this.type] || colorSchemes[ENEMY_TYPES.AIR];
    }
    
    /**
     * Draw air-type enemy (bird-like)
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {object} colors - Color scheme
     * @param {number} pulsePhase - Animation pulse phase
     */
    drawAirEnemy(ctx, colors, pulsePhase) {
        // Wing flapping animation
        const wingFlap = Math.sin(this.animationTime * 8) * 0.3;
        
        // Body
        ctx.fillStyle = colors.primary;
        ctx.beginPath();
        ctx.ellipse(0, 0, this.width * 0.3, this.height * 0.4, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // Wings
        ctx.fillStyle = colors.secondary;
        ctx.beginPath();
        ctx.ellipse(-this.width * 0.4, wingFlap * 5, this.width * 0.3, this.height * 0.2, -0.3, 0, Math.PI * 2);
        ctx.ellipse(this.width * 0.4, wingFlap * 5, this.width * 0.3, this.height * 0.2, 0.3, 0, Math.PI * 2);
        ctx.fill();
        
        // Eyes
        ctx.fillStyle = colors.accent;
        ctx.beginPath();
        ctx.arc(-4, -4, 2, 0, Math.PI * 2);
        ctx.arc(4, -4, 2, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Draw water-type enemy (jellyfish-like)
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {object} colors - Color scheme
     * @param {number} pulsePhase - Animation pulse phase
     */
    drawWaterEnemy(ctx, colors, pulsePhase) {
        // Pulsing bell
        const bellScale = 1 + pulsePhase * 0.2;
        
        // Bell/dome
        ctx.fillStyle = colors.primary;
        ctx.beginPath();
        ctx.ellipse(0, -this.height * 0.2, this.width * 0.4 * bellScale, this.height * 0.3 * bellScale, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // Tentacles
        ctx.strokeStyle = colors.secondary;
        ctx.lineWidth = 2;
        for (let i = 0; i < 4; i++) {
            const angle = (i / 4) * Math.PI * 2;
            const tentacleWave = Math.sin(this.animationTime * 6 + i) * 3;
            
            ctx.beginPath();
            ctx.moveTo(Math.cos(angle) * 6, this.height * 0.1);
            ctx.quadraticCurveTo(
                Math.cos(angle) * 8 + tentacleWave,
                this.height * 0.3,
                Math.cos(angle) * 4 + tentacleWave * 2,
                this.height * 0.4
            );
            ctx.stroke();
        }
        
        // Inner glow
        ctx.fillStyle = colors.accent;
        ctx.beginPath();
        ctx.ellipse(0, -this.height * 0.2, this.width * 0.2, this.height * 0.15, 0, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Draw fire-type enemy (flame-like)
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {object} colors - Color scheme
     * @param {number} pulsePhase - Animation pulse phase
     */
    drawFireEnemy(ctx, colors, pulsePhase) {
        // Flickering flame effect
        const flicker1 = Math.sin(this.animationTime * 12) * 0.2;
        const flicker2 = Math.sin(this.animationTime * 15 + 1) * 0.15;
        
        // Core flame
        ctx.fillStyle = colors.accent;
        ctx.beginPath();
        ctx.ellipse(0, this.height * 0.1, this.width * 0.3, this.height * 0.4, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // Outer flame
        ctx.fillStyle = colors.primary;
        ctx.beginPath();
        ctx.moveTo(0, -this.height * 0.4 + flicker1 * 5);
        ctx.quadraticCurveTo(-this.width * 0.3 + flicker2 * 3, 0, -this.width * 0.2, this.height * 0.3);
        ctx.quadraticCurveTo(0, this.height * 0.4, this.width * 0.2, this.height * 0.3);
        ctx.quadraticCurveTo(this.width * 0.3 + flicker1 * 3, 0, 0, -this.height * 0.4 + flicker1 * 5);
        ctx.fill();
        
        // Inner flame
        ctx.fillStyle = colors.secondary;
        ctx.beginPath();
        ctx.ellipse(0, 0, this.width * 0.25 + flicker2 * 2, this.height * 0.3 + flicker1 * 3, 0, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Draw earth-type enemy (rock-like)
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {object} colors - Color scheme
     * @param {number} pulsePhase - Animation pulse phase
     */
    drawEarthEnemy(ctx, colors, pulsePhase) {
        // Rocky, angular shape
        ctx.fillStyle = colors.primary;
        ctx.strokeStyle = colors.secondary;
        ctx.lineWidth = 2;
        
        ctx.beginPath();
        ctx.moveTo(0, -this.height * 0.4);
        ctx.lineTo(this.width * 0.3, -this.height * 0.2);
        ctx.lineTo(this.width * 0.4, this.height * 0.1);
        ctx.lineTo(this.width * 0.2, this.height * 0.4);
        ctx.lineTo(-this.width * 0.2, this.height * 0.4);
        ctx.lineTo(-this.width * 0.4, this.height * 0.1);
        ctx.lineTo(-this.width * 0.3, -this.height * 0.2);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // Rock texture details
        ctx.fillStyle = colors.accent;
        ctx.beginPath();
        ctx.arc(-this.width * 0.1, -this.height * 0.1, 3, 0, Math.PI * 2);
        ctx.arc(this.width * 0.15, this.height * 0.1, 2, 0, Math.PI * 2);
        ctx.arc(-this.width * 0.2, this.height * 0.2, 2, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Draw crystal-type enemy (crystalline)
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {object} colors - Color scheme
     * @param {number} pulsePhase - Animation pulse phase
     */
    drawCrystalEnemy(ctx, colors, pulsePhase) {
        // Glowing crystal with energy pulses
        const glowIntensity = 0.7 + pulsePhase * 0.3;
        
        // Outer glow
        const gradient = ctx.createRadialGradient(0, 0, 0, 0, 0, this.width * 0.6);
        gradient.addColorStop(0, colors.accent + '80');
        gradient.addColorStop(1, 'transparent');
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(0, 0, this.width * 0.6 * glowIntensity, 0, Math.PI * 2);
        ctx.fill();
        
        // Crystal facets
        ctx.fillStyle = colors.primary;
        ctx.strokeStyle = colors.secondary;
        ctx.lineWidth = 1;
        
        // Main crystal shape
        ctx.beginPath();
        ctx.moveTo(0, -this.height * 0.4);
        ctx.lineTo(this.width * 0.2, -this.height * 0.2);
        ctx.lineTo(this.width * 0.3, 0);
        ctx.lineTo(this.width * 0.2, this.height * 0.3);
        ctx.lineTo(0, this.height * 0.4);
        ctx.lineTo(-this.width * 0.2, this.height * 0.3);
        ctx.lineTo(-this.width * 0.3, 0);
        ctx.lineTo(-this.width * 0.2, -this.height * 0.2);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();
        
        // Inner facet lines
        ctx.strokeStyle = colors.accent;
        ctx.beginPath();
        ctx.moveTo(0, -this.height * 0.4);
        ctx.lineTo(0, this.height * 0.4);
        ctx.moveTo(-this.width * 0.3, 0);
        ctx.lineTo(this.width * 0.3, 0);
        ctx.stroke();
    }
    
    /**
     * Draw shadow-type enemy (dark, ethereal)
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {object} colors - Color scheme
     * @param {number} pulsePhase - Animation pulse phase
     */
    drawShadowEnemy(ctx, colors, pulsePhase) {
        // Wispy, ethereal appearance
        const wispiness = Math.sin(this.animationTime * 4) * 0.3;
        
        // Shadow body with transparency
        ctx.globalAlpha = 0.8 + pulsePhase * 0.2;
        
        // Main shadow form
        ctx.fillStyle = colors.primary;
        ctx.beginPath();
        ctx.ellipse(0, 0, this.width * 0.4 + wispiness * 5, this.height * 0.4 + wispiness * 3, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // Wispy tendrils
        ctx.strokeStyle = colors.secondary;
        ctx.lineWidth = 3;
        ctx.lineCap = 'round';
        
        for (let i = 0; i < 6; i++) {
            const angle = (i / 6) * Math.PI * 2;
            const tendrilWave = Math.sin(this.animationTime * 5 + i) * 8;
            const startX = Math.cos(angle) * this.width * 0.3;
            const startY = Math.sin(angle) * this.height * 0.3;
            
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(
                startX + Math.cos(angle) * (10 + tendrilWave),
                startY + Math.sin(angle) * (10 + tendrilWave)
            );
            ctx.stroke();
        }
        
        // Glowing eyes
        ctx.fillStyle = colors.accent;
        ctx.beginPath();
        ctx.arc(-6, -6, 2, 0, Math.PI * 2);
        ctx.arc(6, -6, 2, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Draw default enemy (fallback)
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     * @param {object} colors - Color scheme
     * @param {number} pulsePhase - Animation pulse phase
     */
    drawDefaultEnemy(ctx, colors, pulsePhase) {
        // Simple circular enemy
        ctx.fillStyle = colors.primary;
        ctx.strokeStyle = colors.secondary;
        ctx.lineWidth = 2;
        
        ctx.beginPath();
        ctx.arc(0, 0, this.width * 0.4, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
        
        // Simple face
        ctx.fillStyle = colors.accent;
        ctx.beginPath();
        ctx.arc(-4, -4, 2, 0, Math.PI * 2);
        ctx.arc(4, -4, 2, 0, Math.PI * 2);
        ctx.fill();
    }
    
    /**
     * Render death animation effects
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderDeathAnimation(ctx) {
        const progress = this.deathAnimationTime / this.deathAnimationDuration;
        const alpha = 1 - progress;
        const scale = 1 + progress * 2;
        
        ctx.globalAlpha = alpha;
        ctx.scale(scale, scale);
        
        // Explosion effect
        const colors = this.getTypeColors();
        const particleCount = 8;
        
        for (let i = 0; i < particleCount; i++) {
            const angle = (i / particleCount) * Math.PI * 2;
            const distance = progress * 30;
            const x = Math.cos(angle) * distance;
            const y = Math.sin(angle) * distance;
            
            ctx.fillStyle = colors.accent;
            ctx.beginPath();
            ctx.arc(x, y, 3 * (1 - progress), 0, Math.PI * 2);
            ctx.fill();
        }
        
        ctx.globalAlpha = 1.0;
    }
    
    /**
     * Render debug information
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderDebugInfo(ctx) {
        // Collision circle
        ctx.strokeStyle = '#FF0000';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.arc(0, 0, this.collisionRadius, 0, Math.PI * 2);
        ctx.stroke();
        
        // Velocity vector
        if (this.velocity.magnitude() > 1) {
            ctx.strokeStyle = '#00FF00';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, 0);
            const velocityScale = 0.1;
            ctx.lineTo(this.velocity.x * velocityScale, this.velocity.y * velocityScale);
            ctx.stroke();
        }
        
        // Health bar
        const barWidth = this.width;
        const barHeight = 4;
        const healthPercent = this.health / this.maxHealth;
        
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(-barWidth / 2, -this.height / 2 - 10, barWidth, barHeight);
        ctx.fillStyle = '#00FF00';
        ctx.fillRect(-barWidth / 2, -this.height / 2 - 10, barWidth * healthPercent, barHeight);
        
        // Type and effectiveness text
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${this.type} (${this.environmentalEffectiveness.toFixed(1)}x)`, 0, this.height / 2 + 15);
    }
    
    /**
     * Set movement pattern
     * @param {string} pattern - Movement pattern name
     * @param {object} options - Pattern-specific options
     */
    setMovementPattern(pattern, options = {}) {
        this.movementPattern = pattern;
        this.movementTimer = 0;
        
        // Apply pattern-specific settings
        switch (pattern) {
            case 'sine':
                this.amplitude = options.amplitude || 50;
                this.frequency = options.frequency || 2;
                break;
            case 'spiral':
                this.amplitude = options.amplitude || 80;
                this.frequency = options.frequency || 3;
                break;
            case 'formation':
                this.isInFormation = true;
                this.formationOffset = options.offset || new Vector2(0, 0);
                break;
            case 'zigzag':
                this.frequency = options.frequency || 1.5;
                break;
            case 'circle':
                this.amplitude = options.radius || 60;
                this.frequency = options.frequency || 2;
                break;
            case 'predefined':
                this.predefinedPattern = options.predefinedPattern || 'straight_down';
                this.predefinedState = 0;
                this.predefinedTimer = 0;
                this.initialPosition = this.position.clone();
                break;
        }
        
        console.log(`Enemy ${this.id} movement pattern set to: ${pattern}`);
    }
    
    /**
     * Set formation target and offset
     * @param {Vector2} target - Formation center target
     * @param {Vector2} offset - Offset from formation center
     */
    setFormationTarget(target, offset = new Vector2(0, 0)) {
        this.formationTarget = target.clone();
        this.formationOffset = offset.clone();
        this.isInFormation = true;
    }
    
    /**
     * Apply environmental effectiveness modifier
     * @param {string} environment - Current environment type
     * @param {number} effectiveness - Effectiveness multiplier (0.1 to 2.0)
     */
    applyEnvironmentalEffect(environment, effectiveness = 1.0) {
        this.currentEnvironment = environment;
        this.environmentalEffectiveness = GameMath.clamp(effectiveness, 0.1, 2.0);
        
        // Update speed and attack rate based on effectiveness
        this.currentSpeed = this.baseSpeed * this.environmentalEffectiveness;
        this.currentAttackRate = this.baseAttackRate * this.environmentalEffectiveness;
        
        // Removed spam logging - only log when effectiveness actually changes
        if (this.environmentalEffectiveness !== effectiveness) {
            console.log(`Enemy ${this.id} environmental effectiveness changed: ${this.environmentalEffectiveness} -> ${effectiveness} in ${environment}`);
        }
    }
    
    /**
     * Take damage and handle destruction
     * @param {number} damage - Amount of damage to take
     * @returns {object} Result object with damage taken, health remaining, and destroyed status
     */
    takeDamage(damage) {
        if (this.isDestroyed) {
            return {
                damageTaken: 0,
                health: this.health,
                destroyed: true,
                scoreValue: 0
            };
        }
        
        const actualDamage = Math.min(damage, this.health);
        this.health -= actualDamage;
        
        console.log(`Enemy ${this.id} took ${actualDamage} damage. Health: ${this.health}/${this.maxHealth}`);
        
        if (this.health <= 0) {
            this.startDeathAnimation();
            return {
                damageTaken: actualDamage,
                health: 0,
                destroyed: true,
                scoreValue: this.scoreValue
            };
        }
        
        return {
            damageTaken: actualDamage,
            health: this.health,
            destroyed: false,
            scoreValue: 0
        };
    }
    
    /**
     * Start death animation sequence
     */
    startDeathAnimation() {
        this.isDestroyed = true;
        this.deathAnimationTime = 0;
        this.canAttack = false;
        
        console.log(`Enemy ${this.id} destroyed! Score value: ${this.scoreValue}`);
    }
    
    /**
     * Check if enemy can attack
     * @param {Vector2} playerPosition - Player's position
     * @returns {boolean} True if enemy can attack
     */
    canAttackPlayer(playerPosition) {
        if (!this.canAttack || this.isDestroyed || !playerPosition) {
            return false;
        }
        
        // Check if player is in attack range (varies by enemy type)
        const attackRange = this.getAttackRange();
        const distanceToPlayer = this.position.distance(playerPosition);
        
        return distanceToPlayer <= attackRange;
    }
    
    /**
     * Get attack range based on enemy type
     * @returns {number} Attack range in pixels
     */
    getAttackRange() {
        const rangeMap = {
            [ENEMY_TYPES.AIR]: 200,
            [ENEMY_TYPES.WATER]: 150,
            [ENEMY_TYPES.FIRE]: 180,
            [ENEMY_TYPES.EARTH]: 120,
            [ENEMY_TYPES.CRYSTAL]: 250,
            [ENEMY_TYPES.SHADOW]: 300
        };
        return rangeMap[this.type] || 200;
    }
    
    /**
     * Trigger attack (to be called by EnemyManager)
     * @param {Vector2} playerPosition - Player's position for targeting
     * @returns {object|null} Attack data or null if can't attack
     */
    attack(playerPosition) {
        if (!this.canAttackPlayer(playerPosition)) {
            return null;
        }
        
        // Set attack cooldown
        this.attackCooldown = (1000 / this.currentAttackRate) + GameMath.random(-200, 200);
        this.canAttack = false;
        
        // Calculate attack direction
        const direction = playerPosition.subtract(this.position).normalize();
        
        return {
            position: this.position.clone(),
            direction: direction,
            type: this.type,
            damage: this.getAttackDamage()
        };
    }
    
    /**
     * Get attack damage based on enemy type
     * @returns {number} Attack damage
     */
    getAttackDamage() {
        const damageMap = {
            [ENEMY_TYPES.AIR]: 15,
            [ENEMY_TYPES.WATER]: 20,
            [ENEMY_TYPES.FIRE]: 25,
            [ENEMY_TYPES.EARTH]: 30,
            [ENEMY_TYPES.CRYSTAL]: 22,
            [ENEMY_TYPES.SHADOW]: 18
        };
        return damageMap[this.type] || 20;
    }
    
    /**
     * Get current status for debugging
     * @returns {object} Status object
     */
    getStatus() {
        return {
            id: this.id,
            type: this.type,
            position: this.position.clone(),
            health: this.health,
            maxHealth: this.maxHealth,
            movementPattern: this.movementPattern,
            environmentalEffectiveness: this.environmentalEffectiveness,
            currentEnvironment: this.currentEnvironment,
            canAttack: this.canAttack,
            isDestroyed: this.isDestroyed,
            scoreValue: this.scoreValue
        };
    }
}