import { Vector2 } from '../utils/Vector2.js';
import { Projectile } from '../entities/Projectile.js';

/**
 * WeaponSystem class - Manages weapon firing, projectile spawning, and weapon types
 * Handles firing rates, projectile patterns, and visual effects
 */
export class WeaponSystem {
    constructor(owner, gameObjectManager) {
        this.owner = owner; // The ship that owns this weapon system
        this.gameObjectManager = gameObjectManager;
        
        // Firing properties
        this.fireRate = 300; // milliseconds between shots
        this.lastFireTime = 0;
        this.canFire = true;
        
        // Projectile properties
        this.projectileSpeed = 600;
        this.projectileDamage = 25; // Increased from 1 to make enemies killable
        this.projectileLifetime = 3000;
        this.projectileType = 'player';
        
        // Weapon patterns
        this.currentPattern = 'single'; // 'single', 'double', 'triple', 'spread'
        this.previousPattern = 'single'; // For restoring after power-up expires
        this.spreadAngle = Math.PI / 6; // 30 degrees for spread pattern
        
        // Visual effects
        this.muzzleFlashDuration = 100; // milliseconds
        this.muzzleFlashTime = 0;
        this.muzzleFlashPositions = [];
        
        // Audio properties (for future sound integration)
        this.fireSound = null;
        this.soundVolume = 0.5;
        
        // Initialize projectile pool
        this.initializeProjectilePool();
        
        console.log('WeaponSystem initialized for owner:', owner.constructor.name);
    }
    
    /**
     * Initialize object pool for projectiles
     */
    initializeProjectilePool() {
        // Create projectile pool if it doesn't exist
        if (!this.gameObjectManager.pools.has('projectile')) {
            this.gameObjectManager.createPool(
                'projectile',
                () => new Projectile(),
                (projectile) => projectile.reset(),
                20 // Initial pool size
            );
        }
    }
    
    /**
     * Update weapon system
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    update(deltaTime) {
        // Update firing cooldown
        if (!this.canFire) {
            this.lastFireTime += deltaTime;
            if (this.lastFireTime >= this.fireRate) {
                this.canFire = true;
                this.lastFireTime = 0;
            }
        }
        
        // Update muzzle flash effect
        if (this.muzzleFlashTime > 0) {
            this.muzzleFlashTime -= deltaTime;
            if (this.muzzleFlashTime <= 0) {
                this.muzzleFlashPositions = [];
            }
        }
    }
    
    /**
     * Attempt to fire weapon
     * @param {Vector2} direction - Direction to fire (optional, defaults to up)
     * @returns {boolean} True if weapon fired successfully
     */
    fire(direction = Vector2.up()) {
        console.log('WeaponSystem.fire() called, canFire:', this.canFire);
        if (!this.canFire) {
            console.log('Cannot fire - weapon on cooldown');
            return false;
        }
        
        // Fire based on current pattern
        switch (this.currentPattern) {
            case 'single':
                this.fireSingle(direction);
                break;
            case 'double':
                this.fireDouble(direction);
                break;
            case 'triple':
                this.fireTriple(direction);
                break;
            case 'spread':
                this.fireSpread(direction);
                break;
            default:
                this.fireSingle(direction);
                break;
        }
        
        // Set firing cooldown
        this.canFire = false;
        this.lastFireTime = 0;
        
        // Trigger muzzle flash effect
        this.triggerMuzzleFlash();
        
        // Play fire sound (if available)
        this.playFireSound();
        
        return true;
    }
    
    /**
     * Fire single projectile
     * @param {Vector2} direction - Direction to fire
     */
    fireSingle(direction) {
        const firePosition = this.getFirePosition();
        this.createProjectile(firePosition, direction);
    }
    
    /**
     * Fire double projectiles (side by side)
     * @param {Vector2} direction - Direction to fire
     */
    fireDouble(direction) {
        const firePosition = this.getFirePosition();
        const offset = direction.perpendicular().multiply(8); // 8 pixels apart
        
        // Left projectile
        this.createProjectile(firePosition.subtract(offset), direction);
        // Right projectile
        this.createProjectile(firePosition.add(offset), direction);
    }
    
    /**
     * Fire triple projectiles (center + sides)
     * @param {Vector2} direction - Direction to fire
     */
    fireTriple(direction) {
        const firePosition = this.getFirePosition();
        const offset = direction.perpendicular().multiply(12); // 12 pixels apart
        
        // Center projectile
        this.createProjectile(firePosition, direction);
        // Left projectile
        this.createProjectile(firePosition.subtract(offset), direction);
        // Right projectile
        this.createProjectile(firePosition.add(offset), direction);
    }
    
    /**
     * Fire spread pattern projectiles
     * @param {Vector2} direction - Base direction to fire
     */
    fireSpread(direction) {
        const firePosition = this.getFirePosition();
        const baseAngle = direction.angle();
        const angleStep = this.spreadAngle / 2; // Spread across spreadAngle
        
        // Fire 5 projectiles in spread pattern
        for (let i = -2; i <= 2; i++) {
            const angle = baseAngle + (i * angleStep);
            const spreadDirection = Vector2.fromAngle(angle);
            this.createProjectile(firePosition, spreadDirection);
        }
    }
    
    /**
     * Create and initialize a projectile
     * @param {Vector2} position - Starting position
     * @param {Vector2} direction - Direction to fire
     */
    createProjectile(position, direction) {
        // Create projectile directly (bypass pool for now)
        const projectile = new Projectile();
        
        // Initialize projectile
        projectile.initialize(
            position,
            direction,
            this.projectileSpeed,
            this.projectileType,
            this.owner
        );
        
        // Set projectile properties
        projectile.damage = this.projectileDamage;
        projectile.lifetime = this.projectileLifetime;
        
        // Add to game object manager
        this.gameObjectManager.add(projectile);
        
        console.log('Projectile created at:', projectile.position.toString(), 'with velocity:', projectile.velocity.toString());
    }
    
    /**
     * Get the position where projectiles should spawn
     * @returns {Vector2} Fire position
     */
    getFirePosition() {
        // Default to owner's position with slight forward offset
        const ownerPos = this.owner.position.clone();
        
        // Offset based on owner's facing direction (assuming up for player)
        const forwardOffset = new Vector2(0, -this.owner.height / 2 - 5);
        
        return ownerPos.add(forwardOffset);
    }
    
    /**
     * Trigger muzzle flash visual effect
     */
    triggerMuzzleFlash() {
        this.muzzleFlashTime = this.muzzleFlashDuration;
        
        // Store muzzle flash positions based on current pattern
        this.muzzleFlashPositions = [];
        
        switch (this.currentPattern) {
            case 'single':
                this.muzzleFlashPositions.push(this.getFirePosition());
                break;
            case 'double':
                const firePos = this.getFirePosition();
                const offset = new Vector2(8, 0);
                this.muzzleFlashPositions.push(firePos.subtract(offset));
                this.muzzleFlashPositions.push(firePos.add(offset));
                break;
            case 'triple':
                const firePos3 = this.getFirePosition();
                const offset3 = new Vector2(12, 0);
                this.muzzleFlashPositions.push(firePos3);
                this.muzzleFlashPositions.push(firePos3.subtract(offset3));
                this.muzzleFlashPositions.push(firePos3.add(offset3));
                break;
            case 'spread':
                // Single muzzle flash for spread pattern
                this.muzzleFlashPositions.push(this.getFirePosition());
                break;
        }
    }
    
    /**
     * Play fire sound effect
     */
    playFireSound() {
        // Placeholder for sound system integration
        if (this.fireSound && typeof this.fireSound.play === 'function') {
            this.fireSound.volume = this.soundVolume;
            this.fireSound.currentTime = 0;
            this.fireSound.play().catch(e => {
                // Handle audio play errors silently
                console.warn('Could not play fire sound:', e);
            });
        }
    }
    
    /**
     * Render weapon effects (muzzle flash, etc.)
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    render(ctx) {
        if (this.muzzleFlashTime > 0) {
            this.renderMuzzleFlash(ctx);
        }
    }
    
    /**
     * Render muzzle flash effect
     * @param {CanvasRenderingContext2D} ctx - Canvas rendering context
     */
    renderMuzzleFlash(ctx) {
        const flashAlpha = this.muzzleFlashTime / this.muzzleFlashDuration;
        
        ctx.save();
        ctx.globalAlpha = flashAlpha;
        
        for (const flashPos of this.muzzleFlashPositions) {
            // Create radial gradient for muzzle flash
            const gradient = ctx.createRadialGradient(
                flashPos.x, flashPos.y, 0,
                flashPos.x, flashPos.y, 15
            );
            gradient.addColorStop(0, '#FFFFFF');
            gradient.addColorStop(0.3, '#FFD700');
            gradient.addColorStop(0.6, '#FF6B35');
            gradient.addColorStop(1, 'rgba(255, 107, 53, 0)');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(flashPos.x, flashPos.y, 15, 0, Math.PI * 2);
            ctx.fill();
            
            // Add bright center flash
            ctx.fillStyle = '#FFFFFF';
            ctx.beginPath();
            ctx.arc(flashPos.x, flashPos.y, 3, 0, Math.PI * 2);
            ctx.fill();
        }
        
        ctx.restore();
    }
    
    /**
     * Set weapon firing pattern
     * @param {string} pattern - Pattern name ('single', 'double', 'triple', 'spread')
     */
    setPattern(pattern) {
        const validPatterns = ['single', 'double', 'triple', 'spread'];
        if (validPatterns.includes(pattern)) {
            this.currentPattern = pattern;
            console.log(`Weapon pattern changed to: ${pattern}`);
        } else {
            console.warn(`Invalid weapon pattern: ${pattern}`);
        }
    }
    
    /**
     * Set firing rate
     * @param {number} rateMs - Time between shots in milliseconds
     */
    setFireRate(rateMs) {
        this.fireRate = Math.max(50, rateMs); // Minimum 50ms between shots
    }
    
    /**
     * Set projectile speed
     * @param {number} speed - Projectile speed in pixels per second
     */
    setProjectileSpeed(speed) {
        this.projectileSpeed = Math.max(100, speed); // Minimum speed
    }
    
    /**
     * Set projectile damage
     * @param {number} damage - Damage per projectile
     */
    setProjectileDamage(damage) {
        this.projectileDamage = Math.max(1, damage); // Minimum 1 damage
    }
    
    /**
     * Set spread angle for spread pattern
     * @param {number} angleRadians - Spread angle in radians
     */
    setSpreadAngle(angleRadians) {
        this.spreadAngle = Math.max(0, Math.min(Math.PI, angleRadians)); // 0 to 180 degrees
    }
    
    /**
     * Check if weapon can fire
     * @returns {boolean} True if weapon is ready to fire
     */
    isReady() {
        return this.canFire;
    }
    
    /**
     * Get firing cooldown progress (0-1)
     * @returns {number} Cooldown progress
     */
    getCooldownProgress() {
        if (this.canFire) return 1.0;
        return this.lastFireTime / this.fireRate;
    }
    
    /**
     * Force weapon to be ready (for power-ups, etc.)
     */
    resetCooldown() {
        this.canFire = true;
        this.lastFireTime = 0;
    }
    
    /**
     * Enable or disable spread pattern
     * @param {boolean} enabled - Whether to enable spread pattern
     */
    enableSpreadPattern(enabled) {
        if (enabled) {
            this.previousPattern = this.currentPattern;
            this.setPattern('spread');
        } else {
            // Restore previous pattern or default to single
            const restorePattern = this.previousPattern || 'single';
            this.setPattern(restorePattern);
        }
    }

    /**
     * Get weapon statistics
     * @returns {object} Weapon stats
     */
    getStats() {
        return {
            pattern: this.currentPattern,
            fireRate: this.fireRate,
            projectileSpeed: this.projectileSpeed,
            projectileDamage: this.projectileDamage,
            isReady: this.canFire,
            cooldownProgress: this.getCooldownProgress()
        };
    }
}